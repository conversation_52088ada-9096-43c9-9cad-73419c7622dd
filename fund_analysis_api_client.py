#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基金分析API客户端
用于Python脚本与Java后端API的通信
"""

import requests
import json
import logging
from datetime import datetime, date
from typing import List, Dict, Any, Optional

class FundAnalysisApiClient:
    """基金分析API客户端"""
    
    def __init__(self, base_url: str = "https://api.lntvs.cc/api/fund-analysis", timeout: int = 30):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'FundAnalyzer/1.0'
        })
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数
            
        Returns:
            响应数据字典
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析JSON响应
            result = response.json()

            # 检查业务状态码 - 适配Java后端的Response格式
            # Java后端成功时code为"0000"，失败时为其他值
            code = result.get('code', '')
            if code != '0000':
                error_msg = result.get('desc', '未知错误')
                raise Exception(f"API请求失败: {error_msg}")

            return result
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP请求失败: {url}, 错误: {e}")
            raise Exception(f"网络请求失败: {e}")
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            raise Exception(f"响应数据格式错误: {e}")
        except Exception as e:
            self.logger.error(f"请求处理失败: {e}")
            raise
    
    def get_ignored_fund_codes(self) -> List[str]:
        """
        获取被忽略的基金代码列表
        
        Returns:
            被忽略的基金代码列表
        """
        try:
            result = self._make_request('GET', '/ignored-funds')
            return result.get('data', [])
        except Exception as e:
            self.logger.warning(f"获取忽略基金列表失败: {e}")
            return []
    
    def save_fund_analysis_data(self, fund_list: List[Dict[str, Any]], analysis_date: Optional[str] = None) -> bool:
        """
        保存基金分析数据
        
        Args:
            fund_list: 基金数据列表
            analysis_date: 分析日期，格式为YYYY-MM-DD，默认为今天
            
        Returns:
            保存是否成功
        """
        if not fund_list:
            self.logger.warning("基金数据列表为空，跳过保存")
            return True
        
        if analysis_date is None:
            analysis_date = date.today().isoformat()
        
        data = {
            'fundList': fund_list,
            'analysisDate': analysis_date
        }
        
        try:
            result = self._make_request('POST', '/save', json=data)
            self.logger.info(f"保存基金分析数据成功: {result.get('data', '')}")
            return True
        except Exception as e:
            self.logger.error(f"保存基金分析数据失败: {e}")
            return False
    
    def get_latest_analysis_date(self) -> Optional[str]:
        """
        获取最新分析日期
        
        Returns:
            最新分析日期字符串，格式为YYYY-MM-DD
        """
        try:
            result = self._make_request('GET', '/latest-date')
            return result.get('data')
        except Exception as e:
            self.logger.warning(f"获取最新分析日期失败: {e}")
            return None
    
    def count_funds_by_date(self, analysis_date: str) -> int:
        """
        统计指定日期的基金数量
        
        Args:
            analysis_date: 分析日期，格式为YYYY-MM-DD
            
        Returns:
            基金数量
        """
        try:
            result = self._make_request('GET', '/count', params={'analysisDate': analysis_date})
            return result.get('data', 0)
        except Exception as e:
            self.logger.warning(f"统计基金数量失败: {e}")
            return 0
    
    def set_fund_ignore_status(self, fund_code: str, fund_name: str, is_ignored: bool, ignore_reason: str = '') -> bool:
        """
        设置基金忽略状态
        
        Args:
            fund_code: 基金代码
            fund_name: 基金名称
            is_ignored: 是否忽略
            ignore_reason: 忽略原因
            
        Returns:
            设置是否成功
        """
        data = {
            'fundCode': fund_code,
            'fundName': fund_name,
            'isIgnored': is_ignored,
            'ignoreReason': ignore_reason
        }
        
        try:
            result = self._make_request('POST', '/ignore', json=data)
            action = "忽略" if is_ignored else "取消忽略"
            self.logger.info(f"{action}基金成功: {fund_code} - {fund_name}")
            return True
        except Exception as e:
            action = "忽略" if is_ignored else "取消忽略"
            self.logger.error(f"{action}基金失败: {fund_code} - {fund_name}, 错误: {e}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否正常
        """
        try:
            self.get_latest_analysis_date()
            self.logger.info("API连接测试成功")
            return True
        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            return False

def convert_fund_data_for_api(fund_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将基金数据转换为API接口所需的格式
    
    Args:
        fund_data: 原始基金数据字典
        
    Returns:
        转换后的基金数据字典
    """
    # 基础字段映射
    api_data = {
        'fund_code': fund_data.get('fund_code', ''),
        'fund_name': fund_data.get('fund_name', ''),
        'company': fund_data.get('company', ''),
        'ftype': fund_data.get('ftype', ''),
        'fund_size': fund_data.get('fund_size'),
        'latest_date': fund_data.get('latest_date', ''),
        'latest_price': fund_data.get('latest_price'),
        'daily_change': fund_data.get('daily_change'),
        'recent_change': fund_data.get('recent_change'),
        'match_reason': fund_data.get('match_reason', ''),
        'drawdown_percent': fund_data.get('drawdown_percent'),
        'recovery_percent': fund_data.get('recovery_percent'),
        'hy_syl': fund_data.get('hy_syl'),
        'year_syl': fund_data.get('year_syl'),
        'try_syl': fund_data.get('try_syl'),
        'sy_syl': fund_data.get('sy_syl'),
        'stddev1': fund_data.get('stddev1'),
        'maxretra1': fund_data.get('maxretra1'),
        'sharp1': fund_data.get('sharp1'),
        'ma5': fund_data.get('ma5'),
        'ma10': fund_data.get('ma10'),
        'latest_ma5_above_ma10': fund_data.get('latest_ma5_above_ma10', False),
        'price_above_ma10': fund_data.get('price_above_ma10', False),
        'industry_name': fund_data.get('industry_name', ''),
        'estimate_value': fund_data.get('estimate_value'),
        'estimate_growth': fund_data.get('estimate_growth'),
        'estimate_time': fund_data.get('estimate_time', '')
    }
    
    # 清理None值，转换为字符串或保持None
    for key, value in api_data.items():
        if value is None:
            api_data[key] = None
        elif isinstance(value, (int, float)):
            api_data[key] = value
        elif isinstance(value, bool):
            api_data[key] = value
        else:
            api_data[key] = str(value) if value is not None else None
    
    return api_data

# 示例用法
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建API客户端
    client = FundAnalysisApiClient()
    
    # 测试连接
    if client.test_connection():
        print("API连接正常")
        
        # 获取忽略的基金列表
        ignored_funds = client.get_ignored_fund_codes()
        print(f"当前忽略的基金数量: {len(ignored_funds)}")
        
        # 获取最新分析日期
        latest_date = client.get_latest_analysis_date()
        print(f"最新分析日期: {latest_date}")
    else:
        print("API连接失败")
