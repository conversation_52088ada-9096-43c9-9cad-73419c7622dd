#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基金列表获取工具
参考URL: https://api.lntvs.cc/fund-api/fundSelect
原始API: https://condition.tiantianfunds.com/condition/conditionFund/fundSelect
"""

import requests
import json
from urllib.parse import urlencode
import time
from datetime import datetime, timedelta
import threading
import random
import os

# 可选导入pandas，如果没有安装则在导出Excel时提示
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

class FundListGetter:
    def __init__(self, proxy=None, thread_safe=True):
        # 原始API地址
        self.base_url = "https://condition.tiantianfunds.com/condition/conditionFund/fundSelect"
        
        # 代理配置
        self.proxy = proxy
        
        # 请求头
        self.headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://fund.eastmoney.com/data/fundranking.html',
            'Origin': 'https://fund.eastmoney.com'
        }

        # 🚀 优化多线程环境下的智能频率控制
        self.thread_safe = thread_safe
        if thread_safe:
            self.lock = threading.Lock()
        
        self.last_request_time = 0
        self.success_count = 0   # 连续成功次数
        self.error_count = 0     # 连续错误次数
        self.base_delay = 0.1    # 🚀 减少基础延迟（多线程环境下）
        self.max_delay = 0.5     # 🚀 减少最大延迟（多线程环境下）

    def _calculate_smart_delay(self):
        """
        智能计算请求延迟时间
        - 连续成功时减少延迟
        - 出现错误时增加延迟
        - 添加随机性避免规律性请求
        - 🚀 针对多线程环境优化
        """
        # 基于成功/失败情况调整延迟
        if self.error_count > 0:
            # 有错误时增加延迟
            delay_factor = 1 + (self.error_count * 0.2)  # 🚀 减少错误延迟增量
            base_delay = min(self.base_delay * delay_factor, self.max_delay)
        elif self.success_count > 5:
            # 连续成功5次以上时减少延迟
            delay_factor = max(0.5, 1 - (self.success_count - 5) * 0.05)  # 🚀 更快减少延迟
            base_delay = self.base_delay * delay_factor
        else:
            base_delay = self.base_delay

        # 添加随机性，避免规律性请求
        random_factor = random.uniform(0.7, 1.3)  # 🚀 增加随机性范围
        final_delay = base_delay * random_factor

        return max(0.05, min(final_delay, self.max_delay))  # 🚀 最小延迟降到0.05秒

    def get_fund_count(self, **params):
        """
        获取基金总数量

        参数：
        - abnormal: 异常标记，默认1
        - appVersion: 应用版本，默认6.7.1
        - rsbType: 基金类型筛选，默认'000001,000002,000003,000005,001001,002001,002002,002003,002004,002005,007001,007002,007003,007004,007005,007006,007007,007008,007009'
        - isBuy: 是否可买，默认1
        - kfType: 开放类型，默认1
        - pageType: 页面类型，默认1
        - plat: 平台，默认Iphone
        - product: 产品，默认Fund
        - purchaseRate: 申购费率，默认1
        - serverversion: 服务器版本，默认6.7.1
        - showHK: 显示港股，默认1
        - version: 版本，默认6.7.1
        """
        # 计数接口URL
        count_url = "https://condition.tiantianfunds.com/condition/conditionFund/fundCount"

        # 默认参数 - 使用新的业务参数（确保获取4286条数据）
        default_params = {
            'abnormal': 1,
            'appVersion': '6.7.3',
            'rsbType': '002004,002001,001001,000001,000005',
            'isBuy': 1,
            'kfType': 1,
            'pageType': 1,
            'plat': 'Iphone',
            'product': 'Fund',
            'purchaseRate': 1,
            'serverversion': '6.7.3',
            'showHK': 1,
            'version': '6.7.3'
        }

        # 合并用户参数
        final_params = {**default_params, **params}

        try:
            # 发送POST请求
            response = requests.post(
                count_url,
                data=urlencode(final_params),
                headers=self.headers,
                proxies=self.proxy,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()

                if data.get('Succeed') and 'Data' in data:
                    total_count = data['Data']
                    return total_count
                else:
                    print(f"❌ 获取基金总数量失败: {data}")
                    return None
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取基金总数量异常: {e}")
            return None

    def calculate_total_pages(self, page_size=200, **params):
        """
        动态计算总页数

        参数：
        - page_size: 每页数量，默认200
        - **params: 其他查询参数

        返回：
        - (总页数, 基金总数量) 元组
        """
        # 获取基金总数量
        total_count = self.get_fund_count(**params)

        if total_count is None:
            print("❌ 无法获取基金总数量，使用默认页数")
            return None, None

        # 计算总页数
        import math
        total_pages = math.ceil(total_count / page_size)

        return total_pages, total_count

    def get_fund_list(self, **kwargs):
        """
        获取基金列表

        参数说明：
        - abnormal: 异常标记，默认1
        - appVersion: 应用版本，默认6.7.1
        - rsbType: 基金类型筛选，默认'000001,000002,000003,000005,001001,002001,002002,002003,002004,002005,007001,007002,007003,007004,007005,007006,007007,007008,007009'
        - isBuy: 是否可买，默认1
        - kfType: 开放类型，默认1
        - pageType: 页面类型，默认1
        - plat: 平台，默认Iphone
        - product: 产品，默认Fund
        - purchaseRate: 申购费率，默认1
        - serverversion: 服务器版本，默认6.7.1
        - showHK: 显示港股，默认1
        - version: 版本，默认6.7.1
        - pageIndex: 页码，默认1
        - pageNum: 每页数量，默认200
        """

        # 默认参数 - 使用新的业务参数（与数量查询保持一致，获取4286条数据）
        default_params = {
            'abnormal': 1,
            'appVersion': '6.7.3',
            'rsbType': '002004,002001,001001,000001,000005',
            'isBuy': 1,
            'kfType': 1,
            'pageType': 1,
            'plat': 'Iphone',
            'product': 'Fund',
            'purchaseRate': 1,
            'serverversion': '6.7.3',
            'showHK': 1,
            'version': '6.7.3',
            'pageIndex': 1,
            'pageNum': 200
        }
        
        # 合并参数
        params = {**default_params, **kwargs}
        
        # 移除空参数
        clean_params = {}
        for key, value in params.items():
            if value != '':
                clean_params[key] = value
        
        try:
            # 🚀 线程安全的智能延迟控制
            current_time = time.time()
            
            if self.thread_safe:
                with self.lock:
                    delay_needed = self._handle_delay_logic(current_time)
            else:
                delay_needed = self._handle_delay_logic(current_time)
            
            if delay_needed > 0:
                time.sleep(delay_needed)

            self.last_request_time = time.time()

            # 发送POST请求
            response = requests.post(
                self.base_url,
                data=urlencode(clean_params),
                headers=self.headers,
                proxies=self.proxy,
                timeout=30
            )

            if response.status_code == 200:
                # 解析响应数据
                data = response.json()
                # 请求成功，更新计数器
                if self.thread_safe:
                    with self.lock:
                        self.success_count += 1
                        self.error_count = 0  # 重置错误计数
                else:
                    self.success_count += 1
                    self.error_count = 0
                return data
            else:
                # 请求失败，更新计数器
                if self.thread_safe:
                    with self.lock:
                        self.error_count += 1
                        self.success_count = 0  # 重置成功计数
                else:
                    self.error_count += 1
                    self.success_count = 0
                return None
                
        except requests.exceptions.RequestException as e:
            # 网络异常，更新计数器
            if self.thread_safe:
                with self.lock:
                    self.error_count += 1
                    self.success_count = 0
            else:
                self.error_count += 1
                self.success_count = 0
            return None
        except json.JSONDecodeError as e:
            # JSON解析异常，更新计数器
            if self.thread_safe:
                with self.lock:
                    self.error_count += 1
                    self.success_count = 0
            else:
                self.error_count += 1
                self.success_count = 0
            return None
    
    def _handle_delay_logic(self, current_time):
        """
        处理延迟逻辑（线程安全）
        """
        if self.last_request_time > 0:
            elapsed = current_time - self.last_request_time
            smart_delay = self._calculate_smart_delay()
            if elapsed < smart_delay:
                return smart_delay - elapsed
        return 0
    
    def print_fund_list(self, data):
        """
        格式化打印基金列表
        """
        if not data:
            print("没有获取到基金数据")
            return
        
        if 'Data' not in data:
            print("响应数据格式异常")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            return
        
        funds = data['Data']
        total_count = data.get('TotalCount', 0)
        
        print(f"\n{'='*50}")
        print(f"基金列表 (共{total_count}条)")
        print(f"{'='*50}")
        
        if not funds:
            print("没有找到基金数据")
            return
        
        for i, fund in enumerate(funds, 1):
            print(f"\n{i}. 基金代码: {fund.get('fundCode', 'N/A')}")
            print(f"   基金名称: {fund.get('fundName', 'N/A')}")
            print(f"   基金类型: {fund.get('ftype', 'N/A')}")
            print(f"   净值日期: {fund.get('jzrq', 'N/A')}")
            print(f"   单位净值: {fund.get('perNav', 'N/A')}")
            print(f"   累计净值: {fund.get('accPerNav', 'N/A')}")
            print(f"   日增长率: {fund.get('daySyl', 'N/A')}%")
            print(f"   近1周: {fund.get('weekSyl', 'N/A')}%")
            print(f"   近1月: {fund.get('monthSyl', 'N/A')}%")
            print(f"   近3月: {fund.get('quarterReturn', 'N/A')}%")
            print(f"   近6月: {fund.get('hyReturn', 'N/A')}%")
            print(f"   近1年: {fund.get('yearReturn', 'N/A')}%")
            print(f"   近2年: {fund.get('twyReturn', 'N/A')}%")
            print(f"   近3年: {fund.get('tryReturn', 'N/A')}%")
            print(f"   成立以来: {fund.get('lnSyl', 'N/A')}%")
            print(f"   基金规模: {self.format_fund_size(fund.get('fundSize', 0))}")
            print(f"   基金公司: {fund.get('company', 'N/A')}")
            print(f"   风险等级: {fund.get('riskLevel', 'N/A')}")
            print(f"   成立日期: {fund.get('establishDate', 'N/A')}")
            print(f"   其他类型: {fund.get('otherTypeName', 'N/A')}")
    
    def get_fund_history_net(self, fund_code, fund_name=None, days=50, range_type='1m'):
        """
        获取基金历史净值数据

        参数：
        - fund_code: 基金代码
        - fund_name: 基金名称（可选）
        - days: 获取的天数，默认50天（当range_type='ln'时使用）
        - range_type: 时间范围类型，默认'1m'（一个月），可选'ln'（全部历史数据）
        """
        # 原始API地址
        url = "https://fundcomapi.tiantianfunds.com/mm/newCore/FundVPageDiagram"
        
        # 请求参数
        params = {
            'FCODE': fund_code,
            'RANGE': range_type  # 使用指定的时间范围参数
        }
        
        try:
            # 🚀 多线程环境下优化延迟策略
            if self.thread_safe:
                with self.lock:
                    delay_needed = self._handle_delay_logic(time.time())
            else:
                delay_needed = self._handle_delay_logic(time.time())
            
            if delay_needed > 0:
                time.sleep(delay_needed)
            
            response = requests.post(
                url,
                data=urlencode(params),
                headers=self.headers,
                proxies=self.proxy,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success') and data.get('data'):
                    # 按日期升序排列
                    history_data = sorted(data['data'], key=lambda x: x['FSRQ'], reverse=True)
                    
                    # 如果是获取全部历史数据，则按days参数截取
                    if range_type == 'ln':
                        return history_data[:days]
                    else:
                        # 其他情况返回全部数据（比如1m就是一个月的数据）
                        return history_data
                else:
                    return []
            else:
                return []
                
        except Exception as e:
            return []

    def get_rsi_limits(self, fund_code):
        """
        获取基金RSI指标极值（高估区和低估区阈值）

        参数：
        - fund_code: 基金代码

        返回：
        - dict: 包含RSI极值数据的字典
        """
        url = 'https://dq.10jqka.com.cn/fuyao/fund/default/v1/fund/indic'

        params = {
            'tradeCodeList': fund_code,
            'typeList': 'rsiBestLimitDown,rsiBestLimitUp'
        }

        headers = {
            'Accept': '*/*',
            'Connection': 'keep-alive',
            'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0'
        }

        try:
            # 智能延迟控制
            if self.thread_safe:
                with self.lock:
                    delay_needed = self._handle_delay_logic(time.time())
            else:
                delay_needed = self._handle_delay_logic(time.time())

            if delay_needed > 0:
                time.sleep(delay_needed)

            response = requests.get(
                url,
                params=params,
                headers=headers,
                proxies=self.proxy,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()

                if (data.get('status_code') == 0 and
                    data.get('data') and
                    fund_code in data['data']):

                    fund_data = data['data'][fund_code]
                    return {
                        'success': True,
                        'fund_code': fund_code,
                        'rsiBestLimitUp': float(fund_data.get('rsiBestLimitUp', 0)) if fund_data.get('rsiBestLimitUp') else None,
                        'rsiBestLimitDown': float(fund_data.get('rsiBestLimitDown', 0)) if fund_data.get('rsiBestLimitDown') else None,
                        'raw_data': fund_data
                    }
                else:
                    return {
                        'success': False,
                        'fund_code': fund_code,
                        'message': f'未找到基金{fund_code}的RSI极值数据',
                        'rsiBestLimitUp': None,
                        'rsiBestLimitDown': None
                    }
            else:
                return {
                    'success': False,
                    'fund_code': fund_code,
                    'message': f'请求失败，状态码: {response.status_code}',
                    'rsiBestLimitUp': None,
                    'rsiBestLimitDown': None
                }

        except Exception as e:
            return {
                'success': False,
                'fund_code': fund_code,
                'message': f'获取RSI极值数据异常: {str(e)}',
                'rsiBestLimitUp': None,
                'rsiBestLimitDown': None
            }

    def get_rsi_trend_data(self, fund_code, start_date=None, end_date=None):
        """
        获取基金RSI趋势数据

        参数：
        - fund_code: 基金代码
        - start_date: 开始日期，格式YYYYMMDD，默认为30天前
        - end_date: 结束日期，格式YYYYMMDD，默认为今天

        返回：
        - dict: 包含RSI趋势数据的字典
        """
        url = 'https://fund.10jqka.com.cn/quotation/fund/v1/card/info'

        # 如果没有提供日期，使用默认的30天范围
        if not start_date or not end_date:
            end_dt = datetime.now()
            start_dt = end_dt - timedelta(days=30)
            start_date = start_dt.strftime('%Y%m%d')
            end_date = end_dt.strftime('%Y%m%d')

        request_data = {
            "cardList": [
                {
                    "ext": {
                        "timeType": "1",
                        "startTime": start_date,
                        "endTime": end_date,
                        "codeList": [fund_code]
                    },
                    "cardEnum": "RSI_TREND_V1"
                }
            ]
        }

        headers = {
            'Accept': '*/*',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0'
        }

        try:
            # 智能延迟控制
            if self.thread_safe:
                with self.lock:
                    delay_needed = self._handle_delay_logic(time.time())
            else:
                delay_needed = self._handle_delay_logic(time.time())

            if delay_needed > 0:
                time.sleep(delay_needed)

            response = requests.post(
                url,
                json=request_data,
                headers=headers,
                proxies=self.proxy,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()

                if (data.get('status_code') == 0 and
                    data.get('data') and
                    data['data'].get('RSI_TREND_V1') and
                    data['data']['RSI_TREND_V1'].get('trendMap') and
                    fund_code in data['data']['RSI_TREND_V1']['trendMap']):

                    trend_data = data['data']['RSI_TREND_V1']['trendMap'][fund_code]

                    return {
                        'success': True,
                        'fund_code': fund_code,
                        'fund_name': trend_data.get('fundName', ''),
                        'rsiValuation': float(trend_data.get('rsiValuation', 0)) if trend_data.get('rsiValuation') else None,
                        'trendList': trend_data.get('trendList', []),
                        'raw_data': trend_data
                    }
                else:
                    return {
                        'success': False,
                        'fund_code': fund_code,
                        'message': f'未找到基金{fund_code}的RSI趋势数据',
                        'rsiValuation': None,
                        'trendList': []
                    }
            else:
                return {
                    'success': False,
                    'fund_code': fund_code,
                    'message': f'请求失败，状态码: {response.status_code}',
                    'rsiValuation': None,
                    'trendList': []
                }

        except Exception as e:
            return {
                'success': False,
                'fund_code': fund_code,
                'message': f'获取RSI趋势数据异常: {str(e)}',
                'rsiValuation': None,
                'trendList': []
            }

    def check_rsi_low_zone(self, fund_code):
        """
        检查基金是否处于RSI低位区

        参数：
        - fund_code: 基金代码

        返回：
        - dict: 包含RSI低位区检查结果的字典
        """
        # 获取RSI极值
        rsi_limits = self.get_rsi_limits(fund_code)
        if not rsi_limits['success']:
            return {
                'success': False,
                'fund_code': fund_code,
                'in_low_zone': False,
                'message': f'获取RSI极值失败: {rsi_limits.get("message", "未知错误")}',
                'rsi_current': None,
                'rsi_limit_down': None,
                'rsi_distance_to_low': None
            }

        # 获取当前RSI值
        rsi_trend = self.get_rsi_trend_data(fund_code)
        if not rsi_trend['success']:
            return {
                'success': False,
                'fund_code': fund_code,
                'in_low_zone': False,
                'message': f'获取RSI趋势数据失败: {rsi_trend.get("message", "未知错误")}',
                'rsi_current': None,
                'rsi_limit_down': rsi_limits.get('rsiBestLimitDown'),
                'rsi_distance_to_low': None
            }

        rsi_current = rsi_trend.get('rsiValuation')
        rsi_limit_down = rsi_limits.get('rsiBestLimitDown')

        if rsi_current is None or rsi_limit_down is None:
            return {
                'success': False,
                'fund_code': fund_code,
                'in_low_zone': False,
                'message': 'RSI数据不完整',
                'rsi_current': rsi_current,
                'rsi_limit_down': rsi_limit_down,
                'rsi_distance_to_low': None
            }

        # 计算距离低位区的差值
        rsi_distance_to_low = rsi_current - rsi_limit_down
        in_low_zone = rsi_current < rsi_limit_down

        # 安全格式化RSI值
        rsi_current_str = f"{rsi_current:.4f}" if rsi_current is not None else "N/A"
        rsi_limit_str = f"{rsi_limit_down:.4f}" if rsi_limit_down is not None else "N/A"

        return {
            'success': True,
            'fund_code': fund_code,
            'fund_name': rsi_trend.get('fund_name', ''),
            'in_low_zone': in_low_zone,
            'rsi_current': rsi_current,
            'rsi_limit_down': rsi_limit_down,
            'rsi_limit_up': rsi_limits.get('rsiBestLimitUp'),
            'rsi_distance_to_low': round(rsi_distance_to_low, 4),
            'message': f'当前RSI({rsi_current_str}) {"在" if in_low_zone else "不在"}低位区(阈值:{rsi_limit_str})'
        }

    def check_drawdown_recovery(self, fund_code, fund_name=None):
        """
        检查基金是否正在传统回撤修复的第2-5天（已放宽条件）

        传统回撤定义（与FundHistoryNet.vue一致）：
        - 以历史最高点作为峰值
        - 从峰值下跌到最低点形成回撤区域
        - 从最低点开始上涨即为修复期

        参数：
        - fund_code: 基金代码
        - fund_name: 基金名称（可选）

        返回：
        - 字典包含检查结果
        """
        # 获取真正的一个月数据，使用RANGE=1m参数
        history_data = self.get_fund_history_net(fund_code, fund_name, range_type='1m')
        
        if not history_data or len(history_data) < 10:
            return {
                'success': False,
                'message': f'一个月历史数据不足，需要至少10天数据，实际获取{len(history_data)}天',
                'data_source': '1m接口',
                'data_count': len(history_data) if history_data else 0
            }
        
        # 提取净值数据（按时间倒序，最新的在前面）
        # 静默处理字符串转换异常，避免干扰进度条
        prices = []
        dates = []
        for item in history_data:
            try:
                if item.get('DWJZ') and item.get('DWJZ') != '':
                    prices.append(float(item['DWJZ']))
                    dates.append(item['FSRQ'])
            except (ValueError, TypeError):
                # 静默跳过无效数据
                continue
        
        # 由于数据是倒序的，需要反转来正确计算（最老的在前，最新的在后）
        prices.reverse()
        dates.reverse()
        
        # 寻找最近的回撤区间
        drawdown_info = self._find_recent_drawdown(prices, dates)

        if not drawdown_info['found']:
            return {
                'success': True,
                'fund_code': fund_code,
                'recovery_day_3': False,
                'message': f'未发现传统回撤：{drawdown_info.get("message", "未知原因")}',
                'drawdown_info': drawdown_info,
                'data_source': '1m接口',
                'data_count': len(history_data),
                'date_range': f"{dates[0]} 到 {dates[-1]}" if dates else "无数据"
            }
        
        # 检查是否正在修复第3-5天（传入prices数组以便验证）
        recovery_result = self._check_recovery_day_range(drawdown_info, len(prices) - 1, prices)
        is_recovery_day_range = recovery_result['is_recovery']
        recovery_day = recovery_result['recovery_day']
        
        # 计算最近3天涨跌幅
        recent_3_day_change = 0.0
        if len(prices) >= 4:
            try:
                latest_price = prices[-1]
                three_days_ago_price = prices[-4]
                recent_3_day_change = (latest_price - three_days_ago_price) / three_days_ago_price * 100
            except (ValueError, ZeroDivisionError):
                recent_3_day_change = 0.0
        
        return {
            'success': True,
            'fund_code': fund_code,
            'recovery_day_3': is_recovery_day_range and recovery_day == 3,  # 兼容原有字段
            'recovery_day_range': is_recovery_day_range,  # 新增：2-4天范围匹配
            'recovery_day': recovery_day,  # 新增：具体第几天
            'drawdown_info': drawdown_info,
            'recent_3_day_change': round(recent_3_day_change, 2),
            'latest_data': {
                'date': dates[-1],
                'price': prices[-1]
            },
            'match_reason': f'正在回撤修复第{recovery_day}天' if is_recovery_day_range else f'不符合回撤修复第3-5天条件（当前第{recovery_day}天）',
            'historical_prices': prices[-10:] if len(prices) >= 10 else prices,
            'historical_dates': dates[-10:] if len(dates) >= 10 else dates,
            'data_source': '1m接口',
            'data_count': len(history_data),
            'date_range': f"{dates[0]} 到 {dates[-1]}" if dates else "无数据"
        }

    def _find_recent_drawdown(self, prices, dates):
        """
        找到最近的回撤区间 - 使用传统最大回撤方法（与FundHistoryNet.vue一致）

        传统回撤定义：
        1. 以历史最高点作为峰值
        2. 从峰值下跌到最低点形成回撤区域
        3. 从最低点开始上涨即为修复期
        4. 只有回撤幅度大于3%的才被认为是有效回撤

        参数：
        - prices: 价格列表（时间升序，第一个是最早的数据）
        - dates: 日期列表（时间升序，第一个是最早的日期）

        返回：
        - 回撤信息字典
        """
        if len(prices) < 5:
            return {'found': False, 'message': '数据点不足'}

        # 使用传统最大回撤算法（与Vue文件一致）
        max_drawdown = 0
        peak_value = prices[0]
        peak_index = 0
        bottom_index = 0
        temp_peak_index = 0

        # 遍历每个净值，计算从该点开始到后续的最大回撤
        for i in range(len(prices)):
            # 如果当前净值创新高，更新峰值
            if prices[i] > peak_value:
                peak_value = prices[i]
                temp_peak_index = i

            # 计算当前回撤
            drawdown = (peak_value - prices[i]) / peak_value * 100

            # 如果当前回撤大于已记录的最大回撤，更新最大回撤信息
            if drawdown > max_drawdown:
                max_drawdown = drawdown
                peak_index = temp_peak_index
                bottom_index = i

        # 过滤掉太小的回撤（小于3%）
        if max_drawdown < 3.0:
            return {
                'found': False,
                'message': f'回撤幅度太小: {max_drawdown:.2f}%（需要大于3%）',
                'peak_value': peak_value,
                'peak_date': dates[peak_index] if peak_index < len(dates) else 'N/A'
            }

        # 检查当前是否还在回撤中，还是已经在修复
        latest_price = prices[-1]
        bottom_value = prices[bottom_index]
        is_in_recovery = latest_price > bottom_value  # 从最低点开始上涨就是修复期

        # 计算修复进度（从最低点到峰值的恢复比例）
        recovery_percent = 0
        if peak_value > bottom_value:
            recovery_percent = (latest_price - bottom_value) / (peak_value - bottom_value) * 100

        return {
            'found': True,
            'peak_index': peak_index,           # 峰值索引
            'peak_value': peak_value,           # 峰值净值
            'peak_date': dates[peak_index],     # 峰值日期
            'bottom_index': bottom_index,       # 最低点索引
            'bottom_value': bottom_value,       # 最低点净值
            'bottom_date': dates[bottom_index], # 最低点日期
            'drawdown_percent': round(max_drawdown, 2),  # 回撤幅度（相对峰值）
            'is_in_recovery': is_in_recovery,   # 是否在修复期
            'latest_price': latest_price,       # 最新价格
            'recovery_percent': round(recovery_percent, 2)  # 修复进度
        }

    def _check_recovery_day_range(self, drawdown_info, latest_index, prices):
        """
        检查是否正在回撤修复的第3天 - 严格筛选条件

        回撤修复定义：
        1. 必须从历史峰值下跌形成回撤
        2. 从最低点开始上涨即为修复期
        3. 检查从最低点到当前的天数必须严格为第3天
        4. 回撤修复期筛选逻辑：严格第3天

        参数：
        - drawdown_info: 回撤信息
        - latest_index: 最新数据的索引
        - prices: 价格数组

        返回：
        - dict: 包含检查结果和具体天数的字典
        """
        if not drawdown_info['found']:
            return {'is_recovery': False, 'recovery_day': 0, 'reason': '未找到回撤区间'}

        bottom_index = drawdown_info['bottom_index']
        peak_value = drawdown_info['peak_value']  # 峰值净值（历史最高点）
        latest_price = prices[latest_index]

        # 检查当前是否在修复期（价格必须高于最低点）
        if not drawdown_info.get('is_in_recovery', False):
            return {'is_recovery': False, 'recovery_day': 0, 'reason': '当前未在修复期（价格未高于最低点）'}

        # 计算从谷值到当前的天数
        recovery_days = latest_index - bottom_index

        # 严格检查是否为第3天
        if recovery_days != 3:
            return {'is_recovery': False, 'recovery_day': recovery_days, 'reason': f'回撤修复第{recovery_days}天，不符合严格的第3天要求'}

        # 检查从谷值开始是否呈上升趋势
        recovery_prices = prices[bottom_index:latest_index + 1]
        min_required_points = recovery_days + 1  # 谷值+修复天数
        if len(recovery_prices) < min_required_points:
            return {'is_recovery': False, 'recovery_day': recovery_days, 'reason': f'数据点不足，需要{min_required_points}个点，实际{len(recovery_prices)}个'}

        # 验证修复趋势（至少70%的时间在上升）
        rising_count = 0
        total_comparisons = len(recovery_prices) - 1

        for i in range(1, len(recovery_prices)):
            if recovery_prices[i] > recovery_prices[i-1]:
                rising_count += 1

        rising_ratio = rising_count / total_comparisons if total_comparisons > 0 else 0
        if rising_ratio < 0.7:  # 至少70%的时间在上升
            return {'is_recovery': False, 'recovery_day': recovery_days, 'reason': f'上升趋势不明显，上升比例{rising_ratio:.1%}'}

        # 获取修复进度信息
        recovery_percent = drawdown_info.get('recovery_percent', 0)

        return {
            'is_recovery': True,
            'recovery_day': recovery_days,
            'reason': f'正在传统回撤修复第{recovery_days}天，趋势良好（修复进度{recovery_percent:.1f}%）'
        }

    def _check_recovery_day_3(self, drawdown_info, latest_index, prices):
        """
        保持向后兼容的方法，调用新的范围检查方法
        """
        result = self._check_recovery_day_range(drawdown_info, latest_index, prices)
        return result['is_recovery'] and result['recovery_day'] == 3

    def calculate_moving_average(self, prices, period):
        """
        计算移动平均线

        参数：
        - prices: 价格列表
        - period: 周期（天数）

        返回：
        - 移动平均线列表
        """
        if len(prices) < period:
            return []

        ma_values = []
        for i in range(len(prices)):
            if i < period - 1:
                ma_values.append(None)
            else:
                # 计算前period天的平均值
                avg = sum(prices[i-period+1:i+1]) / period
                ma_values.append(avg)

        return ma_values

    def check_ma_alignment(self, ma5, ma10, ma20, tolerance=0.0020):
        """
        检查移动平均线排列：20日线 > 10日线 > 5日线

        参数：
        - ma5: 5日移动平均线列表
        - ma10: 10日移动平均线列表
        - ma20: 20日移动平均线列表
        - tolerance: 容错范围，默认0.0020

        返回：
        - dict: 包含检查结果的字典
        """
        if not ma5 or not ma10 or not ma20:
            return {
                'aligned': False,
                'reason': '移动平均线数据不足',
                'latest_ma5': None,
                'latest_ma10': None,
                'latest_ma20': None
            }

        # 获取最新的移动平均线值
        latest_ma5 = ma5[-1] if ma5[-1] is not None else None
        latest_ma10 = ma10[-1] if ma10[-1] is not None else None
        latest_ma20 = ma20[-1] if ma20[-1] is not None else None

        if latest_ma5 is None or latest_ma10 is None or latest_ma20 is None:
            return {
                'aligned': False,
                'reason': '最新移动平均线数据缺失',
                'latest_ma5': latest_ma5,
                'latest_ma10': latest_ma10,
                'latest_ma20': latest_ma20
            }

        # 检查排列：20日线 > 10日线 > 5日线（允许容错）
        ma20_above_ma10 = latest_ma20 > latest_ma10 or abs(latest_ma20 - latest_ma10) <= tolerance
        ma10_above_ma5 = latest_ma10 > latest_ma5 or abs(latest_ma10 - latest_ma5) <= tolerance

        aligned = ma20_above_ma10 and ma10_above_ma5

        if not aligned:
            if not ma20_above_ma10:
                reason = f'20日线({latest_ma20:.4f})未高于10日线({latest_ma10:.4f})'
            elif not ma10_above_ma5:
                reason = f'10日线({latest_ma10:.4f})未高于5日线({latest_ma5:.4f})'
            else:
                reason = '移动平均线排列不符合要求'
        else:
            reason = f'移动平均线排列正确：20日线({latest_ma20:.4f}) > 10日线({latest_ma10:.4f}) > 5日线({latest_ma5:.4f})'

        return {
            'aligned': aligned,
            'reason': reason,
            'latest_ma5': latest_ma5,
            'latest_ma10': latest_ma10,
            'latest_ma20': latest_ma20,
            'ma20_above_ma10': ma20_above_ma10,
            'ma10_above_ma5': ma10_above_ma5
        }

    def check_price_above_ma10(self, current_price, ma10, tolerance=0.0020):
        """
        检查当前净值是否在10日线之上

        参数：
        - current_price: 当前净值
        - ma10: 10日移动平均线列表
        - tolerance: 容错范围，默认0.0020

        返回：
        - dict: 包含检查结果的字典
        """
        if not ma10 or ma10[-1] is None:
            return {
                'above_ma10': False,
                'reason': '10日移动平均线数据缺失',
                'current_price': current_price,
                'latest_ma10': None
            }

        latest_ma10 = ma10[-1]

        # 检查当前价格是否在10日线之上（允许容错）
        above_ma10 = current_price > latest_ma10 or abs(current_price - latest_ma10) <= tolerance

        if above_ma10:
            reason = f'当前净值({current_price:.4f})在10日线({latest_ma10:.4f})之上'
        else:
            reason = f'当前净值({current_price:.4f})未在10日线({latest_ma10:.4f})之上'

        return {
            'above_ma10': above_ma10,
            'reason': reason,
            'current_price': current_price,
            'latest_ma10': latest_ma10,
            'price_diff': current_price - latest_ma10
        }
    
    def check_trend_strengthening(self, ma5, ma10, crossover_index, latest_index):
        """
        检查上穿后趋势是否逐步增强（5日线和10日线差距逐步扩大）
        
        参数：
        - ma5: 5日均线数据
        - ma10: 10日均线数据  
        - crossover_index: 上穿发生的索引
        - latest_index: 最新数据的索引
        
        返回：
        - bool: 趋势是否逐步增强
        """
        if crossover_index >= latest_index:
            return False
            
        # 需要至少有上穿后的数据来判断趋势
        if latest_index - crossover_index < 1:
            return False
        
        # 计算从上穿开始每天的差距
        gaps = []
        for i in range(crossover_index, latest_index + 1):
            if ma5[i] is not None and ma10[i] is not None:
                gap = ma5[i] - ma10[i]
                gaps.append(gap)
        
        # 需要至少2个数据点来比较趋势
        if len(gaps) < 2:
            return False
        
        # 检查差距是否逐步扩大（允许小幅波动）
        strengthening_count = 0
        total_comparisons = len(gaps) - 1
        
        for i in range(1, len(gaps)):
            if gaps[i] > gaps[i-1]:  # 当前差距比前一天大
                strengthening_count += 1
        
        # 至少70%的时间在增强才认为趋势良好
        strengthening_ratio = strengthening_count / total_comparisons
        return strengthening_ratio >= 0.7

    def check_crossover_ma(self, prices, dates, ma_period=20):
        """
        检查当天净值是否上穿指定日均线
        
        上穿定义：
        1. 当天净值在均线之上
        2. 前一天净值在均线之下
        
        参数：
        - prices: 价格列表（时间升序）
        - dates: 日期列表（时间升序）
        - ma_period: 均线周期，默认20日
        
        返回：
        - dict: 包含检查结果的字典
        """
        if not prices or not dates or len(prices) < ma_period + 2:
            return {
                'crossover_found': False,
                'reason': f'数据不足，需要至少{ma_period + 2}天数据，实际{len(prices)}天',
                'current_price': None,
                'ma_value': None,
                'crossover_date': None
            }
        
        # 计算指定周期的移动平均线
        ma_values = self.calculate_moving_average(prices, ma_period)
        
        if not ma_values or len(ma_values) < 2:
            return {
                'crossover_found': False,
                'reason': f'移动平均线计算失败',
                'current_price': None,
                'ma_value': None,
                'crossover_date': None
            }
        
        # 获取最新和前一天的数据
        latest_price = prices[-1]
        prev_price = prices[-2]
        latest_ma = ma_values[-1]
        prev_ma = ma_values[-2]
        latest_date = dates[-1]
        
        if latest_ma is None or prev_ma is None:
            return {
                'crossover_found': False,
                'reason': f'最新均线数据不足',
                'current_price': latest_price,
                'ma_value': latest_ma,
                'crossover_date': latest_date
            }
        
        # 检查上穿条件
        # 1. 当天净值在均线之上
        current_above_ma = latest_price > latest_ma
        
        # 2. 前一天净值在均线之下
        prev_below_ma = prev_price < prev_ma
        
        # 综合判断
        crossover_found = current_above_ma and prev_below_ma
        
        # 计算价格与均线的差距百分比
        price_ma_diff_percent = ((latest_price - latest_ma) / latest_ma) * 100
        
        # 构建详细信息
        if crossover_found:
            reason = f'当天净值({latest_price:.4f})上穿{ma_period}日线({latest_ma:.4f})，' \
                    f'偏离{price_ma_diff_percent:.2f}%'
        else:
            failed_conditions = []
            if not current_above_ma:
                failed_conditions.append(f'当天净值({latest_price:.4f})未在{ma_period}日线({latest_ma:.4f})之上')
            if not prev_below_ma:
                failed_conditions.append(f'前一天净值({prev_price:.4f})未在{ma_period}日线({prev_ma:.4f})之下')
            
            reason = ' | '.join(failed_conditions)
        
        return {
            'crossover_found': crossover_found,
            'reason': reason,
            'current_price': latest_price,
            'prev_price': prev_price,
            'ma_value': latest_ma,
            'prev_ma_value': prev_ma,
            'price_ma_diff_percent': price_ma_diff_percent,
            'crossover_date': latest_date,
            'ma_period': ma_period
        }

    def check_overall_trend_strengthening(self, ma5, ma10, check_days=3):
        """
        检查整体趋势增强情况
        用于判断均线趋势是否健康，即使没有上穿
        """
        if not ma5 or not ma10 or len(ma5) < check_days + 1 or len(ma10) < check_days + 1:
            return False

        # 检查最近几天的趋势增强情况（比趋势增强的要求稍低）
        strengthening_count = 0
        total_comparisons = 0

        for i in range(len(ma5) - check_days, len(ma5)):
            if i > 0 and ma5[i] is not None and ma5[i-1] is not None and ma10[i] is not None and ma10[i-1] is not None:
                # 检查5日均线相对于10日均线是否在增强
                prev_diff = ma5[i-1] - ma10[i-1]
                curr_diff = ma5[i] - ma10[i]

                if curr_diff > prev_diff:  # 差距在扩大，表示趋势增强
                    strengthening_count += 1

                total_comparisons += 1

        if total_comparisons == 0:
            return False

        # 至少60%的时间在增强才认为整体趋势良好（比上穿后的要求稍低）
        strengthening_ratio = strengthening_count / total_comparisons
        return strengthening_ratio >= 0.6

    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """
        计算布林带指标

        参数：
        - prices: 价格列表（时间升序）
        - period: 移动平均线周期，默认20天
        - std_dev: 标准差倍数，默认2倍

        返回：
        - dict: 包含上轨、中轨、下轨的字典
        """
        if len(prices) < period:
            return {
                'upper_band': [],
                'middle_band': [],
                'lower_band': []
            }

        import math

        upper_band = []
        middle_band = []
        lower_band = []

        for i in range(len(prices)):
            if i < period - 1:
                # 数据不足时填充None
                upper_band.append(None)
                middle_band.append(None)
                lower_band.append(None)
            else:
                # 计算移动平均线（中轨）
                period_prices = prices[i-period+1:i+1]
                ma = sum(period_prices) / period

                # 计算标准差
                variance = sum((price - ma) ** 2 for price in period_prices) / period
                std = math.sqrt(variance)

                # 计算布林带
                upper = ma + (std_dev * std)
                lower = ma - (std_dev * std)

                upper_band.append(upper)
                middle_band.append(ma)
                lower_band.append(lower)

        return {
            'upper_band': upper_band,
            'middle_band': middle_band,
            'lower_band': lower_band
        }

    def check_bollinger_lower_band_crossdown(self, prices, dates, period=20, std_dev=2):
        """
        检查当天净值是否下穿布林带下轨

        下穿定义：
        1. 当天净值在布林带下轨之下
        2. 前一天净值在布林带下轨之上

        参数：
        - prices: 价格列表（时间升序）
        - dates: 日期列表（时间升序）
        - period: 布林带周期，默认20天
        - std_dev: 标准差倍数，默认2倍

        返回：
        - dict: 包含检查结果的字典
        """
        if not prices or not dates or len(prices) < period + 2:
            return {
                'crossdown_found': False,
                'reason': f'数据不足，需要至少{period + 2}天数据，实际{len(prices)}天',
                'current_price': None,
                'lower_band_value': None,
                'crossdown_date': None
            }

        # 计算布林带
        bollinger_bands = self.calculate_bollinger_bands(prices, period, std_dev)
        lower_band = bollinger_bands['lower_band']

        if not lower_band or len(lower_band) < 2:
            return {
                'crossdown_found': False,
                'reason': f'布林带计算失败',
                'current_price': None,
                'lower_band_value': None,
                'crossdown_date': None
            }

        # 获取最新和前一天的数据
        latest_price = prices[-1]
        prev_price = prices[-2]
        latest_lower_band = lower_band[-1]
        prev_lower_band = lower_band[-2]
        latest_date = dates[-1]

        if latest_lower_band is None or prev_lower_band is None:
            return {
                'crossdown_found': False,
                'reason': f'最新布林带数据不足',
                'current_price': latest_price,
                'lower_band_value': latest_lower_band,
                'crossdown_date': latest_date
            }

        # 检查下穿条件
        # 1. 当天净值在布林带下轨之下
        current_below_lower = latest_price < latest_lower_band

        # 2. 前一天净值在布林带下轨之上
        prev_above_lower = prev_price >= prev_lower_band

        # 综合判断
        crossdown_found = current_below_lower and prev_above_lower

        # 计算价格与下轨的差距百分比
        price_band_diff_percent = ((latest_price - latest_lower_band) / latest_lower_band) * 100

        # 构建详细信息
        if crossdown_found:
            reason = f'当天净值({latest_price:.4f})下穿布林带下轨({latest_lower_band:.4f})，' \
                    f'偏离{price_band_diff_percent:.2f}%'
        else:
            failed_conditions = []
            if not current_below_lower:
                failed_conditions.append(f'当天净值({latest_price:.4f})未在布林带下轨({latest_lower_band:.4f})之下')
            if not prev_above_lower:
                failed_conditions.append(f'前一天净值({prev_price:.4f})未在布林带下轨({prev_lower_band:.4f})之上')

            reason = ' | '.join(failed_conditions)

        return {
            'crossdown_found': crossdown_found,
            'reason': reason,
            'current_price': latest_price,
            'prev_price': prev_price,
            'lower_band_value': latest_lower_band,
            'prev_lower_band_value': prev_lower_band,
            'price_band_diff_percent': price_band_diff_percent,
            'crossdown_date': latest_date,
            'bollinger_period': period,
            'std_dev': std_dev
        }

    def check_ma5_crossover_ma10_recent(self, ma5, ma10, check_days=3, tolerance=0.0020):
        """
        检查近三日内是否发生5日线上穿10日线，并且当前5日线站在10日线上

        参数：
        - ma5: 5日移动平均线列表
        - ma10: 10日移动平均线列表
        - check_days: 检查的天数，默认3天
        - tolerance: 容错范围，默认0.0020

        返回：
        - dict: 包含检查结果的字典
        """
        if not ma5 or not ma10 or len(ma5) < check_days + 2 or len(ma10) < check_days + 2:
            return {
                'crossover_found': False,
                'reason': '移动平均线数据不足',
                'crossover_day': None,
                'current_ma5_above_ma10': False,
                'latest_ma5': None,
                'latest_ma10': None,
                'crossover_details': None
            }

        # 获取最新的移动平均线值
        latest_ma5 = ma5[-1] if ma5[-1] is not None else None
        latest_ma10 = ma10[-1] if ma10[-1] is not None else None

        if latest_ma5 is None or latest_ma10 is None:
            return {
                'crossover_found': False,
                'reason': '最新移动平均线数据缺失',
                'crossover_day': None,
                'current_ma5_above_ma10': False,
                'latest_ma5': latest_ma5,
                'latest_ma10': latest_ma10,
                'crossover_details': None
            }

        # 检查当前5日线是否站在10日线上
        current_ma5_above_ma10 = latest_ma5 > latest_ma10 or abs(latest_ma5 - latest_ma10) <= tolerance

        if not current_ma5_above_ma10:
            return {
                'crossover_found': False,
                'reason': f'当前5日线({latest_ma5:.4f})未站在10日线({latest_ma10:.4f})上',
                'crossover_day': None,
                'current_ma5_above_ma10': False,
                'latest_ma5': latest_ma5,
                'latest_ma10': latest_ma10,
                'crossover_details': None
            }

        # 检查近三日内是否发生上穿
        crossover_found = False
        crossover_day = None
        crossover_details = None

        # 从最新数据往前检查check_days天
        for i in range(len(ma5) - 1, len(ma5) - check_days - 1, -1):
            if i <= 0:  # 确保有前一天的数据
                break

            current_ma5_val = ma5[i]
            current_ma10_val = ma10[i]
            prev_ma5_val = ma5[i-1]
            prev_ma10_val = ma10[i-1]

            # 检查数据完整性
            if (current_ma5_val is None or current_ma10_val is None or
                prev_ma5_val is None or prev_ma10_val is None):
                continue

            # 检查是否发生上穿：前一天5日线在10日线下方，当天5日线在10日线上方
            prev_ma5_below_ma10 = prev_ma5_val < prev_ma10_val
            current_ma5_above_ma10_day = current_ma5_val > current_ma10_val or abs(current_ma5_val - current_ma10_val) <= tolerance

            if prev_ma5_below_ma10 and current_ma5_above_ma10_day:
                crossover_found = True
                crossover_day = len(ma5) - i  # 距离当前的天数
                crossover_details = {
                    'crossover_index': i,
                    'prev_ma5': prev_ma5_val,
                    'prev_ma10': prev_ma10_val,
                    'crossover_ma5': current_ma5_val,
                    'crossover_ma10': current_ma10_val,
                    'days_ago': crossover_day
                }
                break

        if crossover_found:
            reason = f'近{check_days}日内发生5日线上穿10日线（{crossover_day}天前），且当前5日线({latest_ma5:.4f})站在10日线({latest_ma10:.4f})上'
        else:
            reason = f'近{check_days}日内未发生5日线上穿10日线'

        return {
            'crossover_found': crossover_found,
            'reason': reason,
            'crossover_day': crossover_day,
            'current_ma5_above_ma10': current_ma5_above_ma10,
            'latest_ma5': latest_ma5,
            'latest_ma10': latest_ma10,
            'crossover_details': crossover_details
        }

    def check_strict_fund_conditions(self, fund_code, fund_name=None):
        """
        检查基金是否满足修改后的筛选条件：

        筛选条件：
        1. 正在传统回撤修复的第3天（严格，回撤率大于3%）
        2. 如果基金当前RSI值处于低位区（RSI < rsiBestLimitDown），则该基金也应被筛选出来
        3. 🆕 当天净值上穿20日线
        4. 🆕 当天净值下穿布林带下轨

        筛选逻辑：满足回撤修复条件(1) OR 处于RSI低位区(2) OR 上穿20日线(3) OR 下穿布林带下轨(4)

        传统回撤定义：
        - 以历史最高点作为峰值
        - 从峰值下跌到最低点形成回撤区域（回撤率必须大于3%）
        - 从最低点开始上涨即为修复期

        参数：
        - fund_code: 基金代码
        - fund_name: 基金名称（可选）

        返回：
        - 字典包含检查结果
        """
        # 获取全部历史数据用于更准确的均线计算
        history_data = self.get_fund_history_net(fund_code, fund_name, range_type='ln')

        # 初始化结果变量
        prices = []
        dates = []
        prices_asc = []
        dates_asc = []

        if history_data and len(history_data) >= 10:  # 需要至少10天数据来检测回撤
            # 提取净值数据（按时间倒序，最新的在前面）
            # 静默处理字符串转换异常，避免干扰进度条
            prices = []
            dates = []
            for item in history_data:
                try:
                    if item.get('DWJZ') and item.get('DWJZ') != '':
                        prices.append(float(item['DWJZ']))
                        dates.append(item['FSRQ'])
                except (ValueError, TypeError):
                    # 静默跳过无效数据
                    continue

            # 由于数据是倒序的，需要反转来正确计算（最老的在前，最新的在后）
            prices_asc = prices[::-1]  # 时间升序
            dates_asc = dates[::-1]    # 时间升序
        else:
            # 即使数据不足，也要提供基本的 latest_data 字段
            latest_date = None
            latest_price = None
            if history_data and len(history_data) > 0:
                latest_date = history_data[0]['FSRQ']  # 最新日期（倒序第一个）
                latest_price = float(history_data[0]['DWJZ'])  # 最新净值

            return {
                'success': False,
                'fund_code': fund_code,
                'match_found': False,
                'message': f'历史数据不足，需要至少10天数据检测回撤，实际获取{len(history_data) if history_data else 0}天',
                'data_points': len(history_data) if history_data else 0,
                'latest_data': {
                    'date': latest_date,
                    'price': latest_price
                },
                'recent_3_day_change': 0.0,
                'daily_change': 0.0
            }

        # 🆕 条件1：检查回撤修复第3天（使用最近一个月数据）
        # 从全部历史数据中截取最近一个月的数据用于回撤计算
        # 取最近23天数据（约一个月）
        recent_days = 23
        if len(prices_asc) >= recent_days:
            prices_1m_asc = prices_asc[-recent_days:]  # 最近23天的价格数据
            dates_1m_asc = dates_asc[-recent_days:]    # 最近23天的日期数据
        else:
            # 如果数据不足23天，使用全部数据
            prices_1m_asc = prices_asc
            dates_1m_asc = dates_asc

        drawdown_recovery_result = self._check_drawdown_recovery_with_data(
            fund_code, prices_1m_asc, dates_1m_asc, fund_name
        )

        # 条件2：检查RSI低位区
        rsi_low_zone_result = self.check_rsi_low_zone(fund_code)

        # 🆕 条件3：检查当天净值上穿20日线
        crossover_20ma_result = None
        if len(prices_asc) >= 22:  # 需要至少22天数据来计算20日均线
            crossover_20ma_result = self.check_crossover_ma(prices_asc, dates_asc, ma_period=20)

        # 🆕 条件7：检查当天净值下穿布林带下轨
        bollinger_crossdown_result = None
        if len(prices_asc) >= 22:  # 需要至少22天数据来计算20日布林带
            bollinger_crossdown_result = self.check_bollinger_lower_band_crossdown(prices_asc, dates_asc, period=20, std_dev=2)

        # 检查筛选条件
        # 条件1：回撤修复第3天（去掉RSI条件）
        drawdown_recovery_day_3 = (drawdown_recovery_result.get('success', False) and
                                  drawdown_recovery_result.get('recovery_day_3', False))  # 回撤修复第3天

        condition_1_met = drawdown_recovery_day_3

        # 检查RSI低位区条件
        condition_2_met = (rsi_low_zone_result.get('success', False) and
                          rsi_low_zone_result.get('in_low_zone', False))  # RSI在低位区

        # 🆕 条件3：当天净值上穿20日线
        condition_3_met = (crossover_20ma_result is not None and
                          crossover_20ma_result.get('crossover_found', False))

        # 🆕 条件4：当天净值下穿布林带下轨
        condition_4_met = (bollinger_crossdown_result is not None and
                          bollinger_crossdown_result.get('crossdown_found', False))

        # 最终匹配结果：满足任一条件
        final_match = (condition_1_met or condition_2_met or condition_3_met or condition_4_met)

        # 确定匹配原因
        if final_match:
            matched_conditions = []
            if condition_1_met:
                recovery_day = drawdown_recovery_result.get('recovery_day', 0)
                matched_conditions.append(f"回撤修复第{recovery_day}天")
            if condition_2_met:
                matched_conditions.append("RSI低位区条件")
            # 🆕 添加新条件3的匹配原因（上穿20日线）
            if condition_3_met:
                crossover_info = crossover_20ma_result
                current_price = crossover_info.get('current_price', 0)
                ma_value = crossover_info.get('ma_value', 0)
                diff_percent = crossover_info.get('price_ma_diff_percent', 0)
                matched_conditions.append(f"当天净值({current_price:.4f})上穿20日线({ma_value:.4f})，偏离{diff_percent:.2f}%")

            # 🆕 添加新条件4的匹配原因（下穿布林带下轨）
            if condition_4_met:
                bollinger_info = bollinger_crossdown_result
                current_price = bollinger_info.get('current_price', 0)
                lower_band_value = bollinger_info.get('lower_band_value', 0)
                diff_percent = bollinger_info.get('price_band_diff_percent', 0)
                matched_conditions.append(f"当天净值({current_price:.4f})下穿布林带下轨({lower_band_value:.4f})，偏离{diff_percent:.2f}%")

            if matched_conditions:
                match_reason = " + ".join(matched_conditions)
            else:
                match_reason = "未知匹配原因"
        else:
            failed_conditions = []

            # 检查回撤修复条件失败原因
            if not condition_1_met:
                if not drawdown_recovery_result.get('success', False):
                    failed_conditions.append("回撤检查失败")
                elif not drawdown_recovery_day_3:
                    recovery_day = drawdown_recovery_result.get('recovery_day', 0)
                    failed_conditions.append(f"回撤修复第{recovery_day}天，不符合严格的第3天要求")
                else:
                    failed_conditions.append("未在回撤修复期")

            # 检查RSI条件失败原因
            if not condition_2_met:
                if not rsi_low_zone_result.get('success', False):
                    failed_conditions.append(f"RSI检查失败: {rsi_low_zone_result.get('message', '未知错误')}")
                else:
                    rsi_current = rsi_low_zone_result.get('rsi_current', 0)
                    rsi_limit_down = rsi_low_zone_result.get('rsi_limit_down', 0)
                    rsi_current_str = f"{rsi_current:.4f}" if rsi_current is not None else "N/A"
                    rsi_limit_str = f"{rsi_limit_down:.4f}" if rsi_limit_down is not None else "N/A"
                    failed_conditions.append(f"RSI不在低位区: 当前RSI({rsi_current_str}) >= 低位阈值({rsi_limit_str})")



            match_reason = " | ".join(failed_conditions) if failed_conditions else "所有条件均不满足"

        # 计算涨跌幅
        recent_3_day_change = 0.0
        daily_change = 0.0
        latest_date = None
        latest_price = None

        # 确保总是有最新数据，即使数据不足4天
        if len(prices) >= 1:
            latest_price = prices[0]  # 最新价格
            latest_date = dates[0]    # 最新日期

            # 只有在数据足够时才计算涨跌幅
            if len(prices) >= 4:
                try:
                    # 最近3天涨跌幅
                    three_days_ago_price = prices[3]  # 3天前的价格
                    recent_3_day_change = (latest_price - three_days_ago_price) / three_days_ago_price * 100

                    # 当日涨跌幅
                    previous_price = prices[1]  # 前一天的价格
                    daily_change = (latest_price - previous_price) / previous_price * 100
                except (ValueError, ZeroDivisionError, IndexError):
                    pass

        return {
            'success': True,
            'fund_code': fund_code,
            'match_found': final_match,  # 是否匹配筛选条件
            'match_reason': match_reason,
            'drawdown_recovery_met': condition_1_met,  # 回撤修复条件
            'rsi_low_zone_met': condition_2_met,  # RSI低位区条件
            'crossover_20ma_met': condition_3_met,  # 🆕 上穿20日线条件
            'bollinger_crossdown_met': condition_4_met,  # 🆕 下穿布林带下轨条件
            'drawdown_recovery_result': drawdown_recovery_result,  # 回撤修复检查结果
            'rsi_low_zone_result': rsi_low_zone_result,  # RSI低位区检查结果
            'crossover_20ma_result': crossover_20ma_result,  # 🆕 上穿20日线检查结果
            'bollinger_crossdown_result': bollinger_crossdown_result,  # 🆕 下穿布林带下轨检查结果
            'recent_3_day_change': round(recent_3_day_change, 2),
            'daily_change': round(daily_change, 2),
            'latest_data': {
                'date': latest_date,
                'price': latest_price
            },
            'data_points': len(history_data) if history_data else 0
        }

    def _check_drawdown_recovery_with_data(self, fund_code, prices_asc, dates_asc, fund_name=None):
        """
        🚀 新增：使用已获取的数据检查回撤修复条件，避免重复API调用
        
        参数：
        - fund_code: 基金代码
        - prices_asc: 价格数据（时间升序）
        - dates_asc: 日期数据（时间升序）
        - fund_name: 基金名称（可选）
        
        返回：
        - 字典包含检查结果
        """
        if not prices_asc or len(prices_asc) < 10:
            return {
                'success': False,
                'message': f'历史数据不足，需要至少10天数据，实际获取{len(prices_asc)}天',
                'data_source': '复用数据',
                'data_count': len(prices_asc)
            }
        
        # 寻找最近的回撤区间
        drawdown_info = self._find_recent_drawdown(prices_asc, dates_asc)
        
        if not drawdown_info['found']:
            return {
                'success': True,
                'fund_code': fund_code,
                'recovery_day_3': False,
                'message': '未发现明显的回撤区间',
                'drawdown_info': drawdown_info,
                'data_source': '复用数据',
                'data_count': len(prices_asc),
                'date_range': f"{dates_asc[0]} 到 {dates_asc[-1]}" if dates_asc else "无数据"
            }
        
        # 检查是否正在修复第2-5天
        recovery_result = self._check_recovery_day_range(drawdown_info, len(prices_asc) - 1, prices_asc)
        is_recovery_day_range = recovery_result['is_recovery']
        recovery_day = recovery_result['recovery_day']
        recovery_reason = recovery_result['reason']
        
        # 计算最近3天涨跌幅
        recent_3_day_change = 0.0
        if len(prices_asc) >= 4:
            try:
                latest_price = prices_asc[-1]
                three_days_ago_price = prices_asc[-4]
                recent_3_day_change = (latest_price - three_days_ago_price) / three_days_ago_price * 100
            except (ValueError, ZeroDivisionError):
                recent_3_day_change = 0.0
        
        return {
            'success': True,
            'fund_code': fund_code,
            'recovery_day_3': is_recovery_day_range and recovery_day == 3,  # 兼容原有字段
            'recovery_day_range': is_recovery_day_range,  # 新增：2-5天范围匹配
            'recovery_day': recovery_day,  # 新增：具体第几天
            'recovery_reason': recovery_reason,  # 新增：详细原因
            'drawdown_info': drawdown_info,
            'recent_3_day_change': round(recent_3_day_change, 2),
            'latest_data': {
                'date': dates_asc[-1],
                'price': prices_asc[-1]
            },
            'match_reason': f'正在回撤修复第{recovery_day}天' if is_recovery_day_range else recovery_reason,
            'historical_prices': prices_asc[-10:] if len(prices_asc) >= 10 else prices_asc,
            'historical_dates': dates_asc[-10:] if len(dates_asc) >= 10 else dates_asc,
            'data_source': '复用数据',
            'data_count': len(prices_asc),
            'date_range': f"{dates_asc[0]} 到 {dates_asc[-1]}" if dates_asc else "无数据"
        }

    def check_multiple_funds_strict_conditions(self, fund_codes, fund_names=None, fund_info=None):
        """
        批量检查多个基金的严格筛选条件

        参数：
        - fund_codes: 基金代码列表
        - fund_names: 基金名称字典，key为基金代码，value为基金名称（可选）
        - fund_info: 基金完整信息字典，key为基金代码，value为基金信息字典（可选）
        """
        results = []
        qualified_messages = []  # 收集符合条件的基金信息，避免干扰进度条
        filtered_by_size_messages = []  # 收集被规模过滤的基金信息
        total_funds = len(fund_codes)

        for i, fund_code in enumerate(fund_codes, 1):
            # 📊 简化的进度显示（减少更新频率）
            # 只在每完成10%或最后一个基金时显示进度
            if (i % max(1, total_funds // 10) == 0 or i == total_funds):
                progress_percent = (i / total_funds) * 100

                # 获取基金名称
                fund_name = None
                if fund_names and fund_code in fund_names:
                    fund_name = fund_names[fund_code]
                    print(f"📈 筛选进度: {i}/{total_funds} ({progress_percent:.1f}%) - 正在检查: {fund_code}({fund_name})")
                else:
                    print(f"📈 筛选进度: {i}/{total_funds} ({progress_percent:.1f}%) - 正在检查: {fund_code}")
            else:
                # 获取基金名称（不显示进度）
                fund_name = None
                if fund_names and fund_code in fund_names:
                    fund_name = fund_names[fund_code]

            # 🆕 基金规模过滤 - 排除规模在1000万以下的基金
            if fund_info and fund_code in fund_info:
                fund_size = fund_info[fund_code].get('fundSize', 0)
                try:
                    fund_size_float = float(fund_size) if fund_size else 0
                    if fund_size_float < 10000000:  # 1000万 = 10,000,000
                        # 收集被规模过滤的基金信息
                        size_formatted = self.format_fund_size(fund_size_float)
                        filter_message = f"🚫 [{i}/{total_funds}] {fund_code}({fund_name or 'Unknown'}) 被规模过滤：基金规模{size_formatted}小于1000万"
                        filtered_by_size_messages.append(filter_message)
                        
                        # 创建一个表示被过滤的结果
                        filtered_result = {
                            'success': True,
                            'fund_code': fund_code,
                            'match_found': False,
                            'match_reason': f'基金规模{size_formatted}小于1000万，已过滤',
                            'filtered_by_size': True,
                            'fund_size': fund_size_float,
                            'recent_3_day_change': 0.0,
                            'daily_change': 0.0,
                            'latest_data': {'date': None, 'price': None},
                            'data_points': 0
                        }
                        
                        # 添加基金的基本信息到结果中
                        fund_data = fund_info[fund_code]
                        filtered_result['company'] = fund_data.get('company', '')
                        filtered_result['fund_type'] = fund_data.get('ftype', '')
                        filtered_result['establish_date'] = fund_data.get('establishDate', '')
                        
                        results.append(filtered_result)
                        continue
                except (ValueError, TypeError):
                    # 如果无法解析基金规模，继续正常处理
                    pass

            result = self.check_strict_fund_conditions(fund_code, fund_name)

            # 添加基金的基本信息到结果中
            if fund_info and fund_code in fund_info:
                fund_data = fund_info[fund_code]
                result['fund_size'] = fund_data.get('fundSize', 0)
                result['company'] = fund_data.get('company', '')
                result['fund_type'] = fund_data.get('ftype', '')
                result['establish_date'] = fund_data.get('establishDate', '')

            results.append(result)

            if result['success'] and result['match_found']:
                match_reason = result.get('match_reason', '未知原因')

                # 收集符合条件的基金信息，不立即显示（避免干扰进度条）
                message_lines = []
                message_lines.append(f"🎉 发现符合扩展条件的基金！{fund_code} - 匹配原因: {match_reason}")
                message_lines.append(f"   最近三天涨跌幅: {result['recent_3_day_change']:.2f}%")

                # 显示匹配类型
                drawdown_met = result.get('drawdown_recovery_met', False)
                rsi_met = result.get('rsi_low_zone_met', False)

                match_types = []
                if drawdown_met:
                    match_types.append("📈 回撤修复条件")
                if rsi_met:
                    match_types.append("📉 RSI低位区条件")

                if match_types:
                    message_lines.append(f"   🌟 匹配类型: {' + '.join(match_types)}")

                # 显示条件详情
                if result.get('drawdown_recovery_met', False):
                    drawdown_result = result.get('drawdown_recovery_result', {})
                    drawdown_info = drawdown_result.get('drawdown_info', {})
                    rsi_result = result.get('rsi_low_zone_result', {})
                    if drawdown_info.get('found'):
                        recovery_day = drawdown_result.get('recovery_day', 0)
                        rsi_current = rsi_result.get('rsi_current', 0)
                        rsi_str = f"{rsi_current:.4f}" if rsi_current is not None else "N/A"
                        message_lines.append(f"   ✅ 回撤修复条件：正在修复第{recovery_day}天且RSI({rsi_str})<0.6")
                        message_lines.append(f"   峰值: {drawdown_info['peak_value']} ({drawdown_info['peak_date']})")
                        message_lines.append(f"   谷值: {drawdown_info['bottom_value']} ({drawdown_info['bottom_date']})")
                        message_lines.append(f"   回撤幅度: {drawdown_info['drawdown_percent']}%")

                if result.get('rsi_low_zone_met', False):
                    rsi_result = result.get('rsi_low_zone_result', {})
                    if rsi_result.get('success'):
                        rsi_current = rsi_result.get('rsi_current', 0)
                        rsi_limit_down = rsi_result.get('rsi_limit_down', 0)
                        rsi_distance = rsi_result.get('rsi_distance_to_low', 0)
                        rsi_current_str = f"{rsi_current:.4f}" if rsi_current is not None else "N/A"
                        rsi_limit_str = f"{rsi_limit_down:.4f}" if rsi_limit_down is not None else "N/A"
                        rsi_distance_str = f"{rsi_distance:.4f}" if rsi_distance is not None else "N/A"
                        message_lines.append(f"   ✅ RSI低位区条件：当前RSI({rsi_current_str}) < 阈值({rsi_limit_str})")
                        message_lines.append(f"   RSI距离低位区差值: {rsi_distance_str} (负值表示在低位区内)")

                # 显示RSI状态（无论是否满足条件）
                rsi_result = result.get('rsi_low_zone_result', {})
                if rsi_result.get('success'):
                    rsi_status = "在低位区内" if rsi_result.get('in_low_zone') else "不在低位区"
                    message_lines.append(f"   📊 RSI状态: {rsi_status}")

                # 将所有消息行添加到qualified_messages
                qualified_messages.extend(message_lines)
            else:
                if result['success']:
                    latest = result['latest_data']
                    if latest.get('date') and latest.get('price'):
                        print(f"   最新数据({latest['date']}): 净值={latest['price']}")

                    print(f"   不符合筛选条件原因: {result.get('match_reason', '未知原因')}")

                    # 显示各条件的详细检查结果
                    drawdown_met = result.get('drawdown_recovery_met', False)
                    rsi_met = result.get('rsi_low_zone_met', False)

                    print(f"   📋 筛选条件检查结果:")
                    if not drawdown_met:
                        drawdown_result = result.get('drawdown_recovery_result', {})
                        rsi_result = result.get('rsi_low_zone_result', {})
                        if drawdown_result.get('success'):
                            recovery_day = drawdown_result.get('recovery_day', 0)
                            rsi_current = rsi_result.get('rsi_current', 0)
                            if drawdown_result.get('recovery_day_3', False):
                                rsi_str = f"{rsi_current:.4f}" if rsi_current is not None else "N/A"
                                if rsi_current is not None and rsi_current >= 0.6:
                                    print(f"     ❌ 回撤修复：第{recovery_day}天但RSI({rsi_str})不小于0.6")
                                else:
                                    print(f"     ✅ 回撤修复：第{recovery_day}天且RSI({rsi_str})<0.6")
                            else:
                                print(f"     ❌ 回撤修复：第{recovery_day}天，不符合严格的第3天要求")
                        else:
                            print(f"     ❌ 回撤修复：{drawdown_result.get('message', '检查失败')}")
                    else:
                        recovery_day = result.get('drawdown_recovery_result', {}).get('recovery_day', 0)
                        rsi_current = result.get('rsi_low_zone_result', {}).get('rsi_current', 0)
                        rsi_str = f"{rsi_current:.4f}" if rsi_current is not None else "N/A"
                        print(f"     ✅ 回撤修复：第{recovery_day}天且RSI({rsi_str})<0.6")

                    if not rsi_met:
                        rsi_result = result.get('rsi_low_zone_result', {})
                        if rsi_result.get('success'):
                            rsi_current = rsi_result.get('rsi_current', 0)
                            rsi_limit_down = rsi_result.get('rsi_limit_down', 0)
                            rsi_distance = rsi_result.get('rsi_distance_to_low', 0)
                            rsi_current_str = f"{rsi_current:.4f}" if rsi_current is not None else "N/A"
                            rsi_limit_str = f"{rsi_limit_down:.4f}" if rsi_limit_down is not None else "N/A"
                            rsi_distance_str = f"{rsi_distance:.4f}" if rsi_distance is not None else "N/A"
                            print(f"     ❌ RSI低位区：当前RSI({rsi_current_str}) >= 阈值({rsi_limit_str})")
                            print(f"     RSI距离低位区差值: {rsi_distance_str}")
                        else:
                            print(f"     ❌ RSI检查失败：{rsi_result.get('message', '未知错误')}")
                    else:
                        print(f"     ✅ RSI低位区：符合要求")
                else:
                    print(f"   检查失败: {result.get('message', '未知错误')}")

        # 在处理完成后，统一显示符合条件的基金信息
        if qualified_messages:
            print(f"\n🎯 发现符合条件的基金：")
            for message in qualified_messages:
                print(message)
            print()  # 添加空行分隔

        # 显示规模过滤统计信息
        if filtered_by_size_messages:
            display_count = min(10, len(filtered_by_size_messages))  # 最多显示10个
            print(f"💰 规模过滤统计: 共{len(filtered_by_size_messages)}只基金因规模小于1000万被过滤（显示前{display_count}个）：")
            for message in filtered_by_size_messages[:display_count]:
                print(message)
            if len(filtered_by_size_messages) > display_count:
                print(f"   ... 还有 {len(filtered_by_size_messages) - display_count} 只基金被规模过滤")
            print()  # 添加空行分隔

        return results

    def format_fund_size(self, fund_size):
        """
        将基金规模转换为万或亿单位的格式化字符串

        参数：
        - fund_size: 基金规模（元）

        返回：
        - 格式化后的字符串，如 "5.41万" 或 "84.77亿"
        """
        if not fund_size:
            return "未知"

        try:
            size_float = float(fund_size)

            if size_float <= 0:
                return "未知"

            # 如果大于等于1亿，使用亿作为单位
            if size_float >= 100000000:  # 1亿 = 100,000,000
                size_yi = size_float / 100000000
                return f"{size_yi:.2f}亿"
            # 如果大于等于1万，使用万作为单位
            elif size_float >= 10000:  # 1万 = 10,000
                size_wan = size_float / 10000
                return f"{size_wan:.2f}万"
            # 小于1万的直接显示元
            else:
                return f"{size_float:.2f}元"

        except (ValueError, TypeError):
            return "未知"

    def export_results_to_excel(self, results, filename='fund_screening_results.xlsx'):
        """
        将筛选结果导出到Excel文件，包含RSI相关数据

        参数：
        - results: 筛选结果列表
        - filename: 输出文件名
        """
        if not PANDAS_AVAILABLE:
            print("❌ 导出Excel需要安装pandas和openpyxl库")
            print("   请运行: pip install pandas openpyxl")
            return False

        try:
            import pandas as pd

            # 准备数据
            export_data = []

            for result in results:
                if not result.get('success'):
                    continue

                # 基础信息
                row = {
                    '基金代码': result.get('fund_code', ''),
                    '基金名称': result.get('rsi_low_zone_result', {}).get('fund_name', '') if result.get('rsi_low_zone_result', {}).get('success') else '',
                    '基金公司': result.get('company', ''),
                    '基金类型': result.get('fund_type', ''),
                    '基金规模': self.format_fund_size(result.get('fund_size', 0)),
                    '匹配结果': '是' if result.get('match_found') else '否',
                    '匹配原因': result.get('match_reason', ''),
                    '匹配类型': self._get_match_type(result),
                    'RSI值': result.get('rsi_low_zone_result', {}).get('rsi_current', 0) if result.get('rsi_low_zone_result', {}).get('success') else 0,
                    '最近3天涨跌幅(%)': result.get('recent_3_day_change', 0),
                    '当日涨跌幅(%)': result.get('daily_change', 0),
                    '最新日期': result.get('latest_data', {}).get('date', ''),
                    '最新净值': result.get('latest_data', {}).get('price', 0),
                    '数据点数': result.get('data_points', 0)
                }

                # 筛选条件结果
                row['回撤修复条件'] = '是' if result.get('drawdown_recovery_met') else '否'
                row['RSI低位区条件'] = '是' if result.get('rsi_low_zone_met') else '否'

                # RSI相关数据
                rsi_result = result.get('rsi_low_zone_result', {})
                if rsi_result.get('success'):
                    row['当前RSI'] = rsi_result.get('rsi_current', 0)
                    row['RSI低位阈值'] = rsi_result.get('rsi_limit_down', 0)
                    row['RSI距离低位区差值'] = rsi_result.get('rsi_distance_to_low', 0)
                    row['RSI高位阈值'] = rsi_result.get('rsi_limit_up', 0)
                    row['RSI状态'] = '低位区内' if rsi_result.get('in_low_zone') else '低位区外'
                    row['基金名称'] = rsi_result.get('fund_name', '')
                else:
                    row['当前RSI'] = 0
                    row['RSI低位阈值'] = 0
                    row['RSI距离低位区差值'] = 0
                    row['RSI高位阈值'] = 0
                    row['RSI状态'] = '未知'
                    row['基金名称'] = ''

                # 回撤数据
                drawdown_result = result.get('drawdown_recovery_result', {})
                if drawdown_result.get('success'):
                    drawdown_info = drawdown_result.get('drawdown_info', {})
                    if drawdown_info.get('found'):
                        row['回撤深度(%)'] = drawdown_info.get('drawdown_percent', 0)
                        row['峰值日期'] = drawdown_info.get('peak_date', '')
                        row['谷值日期'] = drawdown_info.get('bottom_date', '')
                        row['修复天数'] = drawdown_result.get('recovery_day', 0)
                        row['当前回撤修复率(%)'] = drawdown_info.get('recovery_percent', 0)
                    else:
                        row['回撤深度(%)'] = 0
                        row['峰值日期'] = ''
                        row['谷值日期'] = ''
                        row['修复天数'] = 0
                        row['当前回撤修复率(%)'] = 0
                else:
                    row['回撤深度(%)'] = 0
                    row['峰值日期'] = ''
                    row['谷值日期'] = ''
                    row['修复天数'] = 0
                    row['当前回撤修复率(%)'] = 0

                export_data.append(row)

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 按匹配结果和RSI距离低位区差值排序
            df = df.sort_values(['匹配结果', 'RSI距离低位区差值'], ascending=[False, True])

            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='筛选结果', index=False)

                # 获取工作表对象进行格式化
                worksheet = writer.sheets['筛选结果']

                # 设置列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            print(f"✅ 筛选结果已导出到Excel文件: {filename}")
            print(f"   总计 {len(export_data)} 条记录")

            # 统计信息
            matched_count = len([r for r in export_data if r['匹配结果'] == '是'])
            drawdown_matched = len([r for r in export_data if r['回撤修复条件'] == '是'])
            rsi_matched = len([r for r in export_data if r['RSI低位区条件'] == '是'])

            print(f"   匹配基金数量: {matched_count}")
            print(f"   其中满足回撤修复条件: {drawdown_matched}")
            print(f"   其中满足RSI低位区条件: {rsi_matched}")

            return True

        except Exception as e:
            print(f"❌ 导出Excel失败: {str(e)}")
            return False

    def _get_match_type(self, result):
        """
        获取匹配类型描述
        """
        if not result.get('match_found'):
            return '不匹配'

        drawdown_met = result.get('drawdown_recovery_met', False)
        rsi_met = result.get('rsi_low_zone_met', False)

        match_types = []
        if drawdown_met:
            match_types.append('回撤修复')
        if rsi_met:
            match_types.append('RSI低位区')

        if match_types:
            return '+'.join(match_types)
        else:
            return '未知类型'

# 该文件为库文件，仅提供类和函数供其他模块调用
# 主要功能由 test_ma_crossover.py 调用