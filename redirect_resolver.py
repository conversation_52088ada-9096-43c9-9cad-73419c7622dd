import requests
from requests.exceptions import HTTPError, TooManyRedirects # Import TooManyRedirects
from urllib.parse import urlparse, urljoin # Import urljoin
from typing import Optional # 新增导入
from colorama import Fore, Style, init # 新增导入

# 初始化 colorama
init(autoreset=True)

# 定义 EMOJI 字典 (可以根据需要从 cursor_pro_keep_alive.py 复制或自定义)
EMOJI = {
    "INFO": "ℹ️",
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "ERROR": "❌",
    "REDIRECT": "➡️",
    "REQUEST": "📡",
    "RESPONSE": "💬",
    "NETWORK_ERROR": "🌐",
    "HTTP_ERROR": "🔥",
    "TIMEOUT": "⏱️",
    "MAX_REDIRECT": "🚫",
    "URL_RESOLVED": "🔗",
    "URL_PARSE_ERROR": "💥"
}

def get_final_url(url: str, proxies: Optional[dict] = None) -> str: # 添加 proxies 参数
    """
    获取给定 URL 经过所有重定向后的最终 URL，完全手动处理所有重定向步骤。

    :param url: 初始 URL 字符串。
    :param proxies: 可选的代理字典 (例如 {'http': 'http://proxy.com:8080', 'https': 'http://proxy.com:8080'})。
    :return: 最终重定向的 URL 字符串。
    :raises: 如果请求过程中发生错误，或超过最大重定向次数，则抛出。
    """
    MAX_MANUAL_REDIRECTS = 10 
    session = requests.Session()
    
    base_headers = {
        'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0',
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
    }
    session.headers.update(base_headers)

    current_url = url.strip()
    if current_url.startswith('@') and not current_url.startswith('@http'):
        current_url = current_url[1:]

    print(f"{Fore.CYAN}{EMOJI['INFO']} 正在解析 URL: {url} (代理: {Fore.YELLOW}{proxies if proxies.get('http') else '无'}{Style.RESET_ALL})")

    try:
        for i in range(MAX_MANUAL_REDIRECTS):
            parsed_current_url = urlparse(current_url)
            current_host = parsed_current_url.hostname
            if not current_host:
                error_msg = f"无法从URL中解析主机名: {current_url}"
                print(f"{Fore.RED}{EMOJI['URL_PARSE_ERROR']} {error_msg}{Style.RESET_ALL}")
                raise ValueError(error_msg)
            session.headers['Host'] = current_host
            
            print(f"{Fore.CYAN}{EMOJI['REQUEST']} 第 {i+1}/{MAX_MANUAL_REDIRECTS} 次请求: GET {current_url}{Style.RESET_ALL}")
            try:
                # 将 proxies 参数传递给 session.get
                response = session.get(current_url, allow_redirects=False, timeout=15, proxies=proxies)
            except requests.exceptions.RequestException as e:
                print(f"{Fore.RED}{EMOJI['NETWORK_ERROR']} 在 GET {current_url} 时发生网络错误 (代理: {Fore.YELLOW}{proxies if proxies else '无'}{Style.RESET_ALL}): {e}{Style.RESET_ALL}")
                raise
            print(f"{Fore.BLUE}{EMOJI['RESPONSE']} 来自 {response.url} 的响应 | 状态码: {Fore.MAGENTA}{response.status_code}{Style.RESET_ALL}")

            if 200 <= response.status_code < 300:
                #print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功获取最终 URL (状态码 {response.status_code}): {response.url}{Style.RESET_ALL}")
                return response.url

            if 300 <= response.status_code < 400 and response.headers.get('Location'):
                location_val = response.headers['Location']
                next_url = urljoin(response.url, location_val)
                #print(f"{Fore.CYAN}{EMOJI['REDIRECT']} 重定向 ({response.status_code}) 到: {Fore.GREEN}{next_url}{Style.RESET_ALL}")
                current_url = next_url
            else:
                #print(f"{Fore.YELLOW}{EMOJI['WARNING']} 非重定向、非2xx成功的状态码 ({response.status_code}) 来自 {response.url}. 准备抛出错误.{Style.RESET_ALL}")
                response.raise_for_status()
                # 如果 raise_for_status() 没有抛出错误 (理论上不应该发生这种情况，除非状态码是2xx且之前逻辑未捕获)
                #print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 最终URL (未处理或非错误的状态码 {response.status_code}): {response.url}{Style.RESET_ALL}")
                return response.url
        
        print(f"{Fore.RED}{EMOJI['MAX_REDIRECT']} 超出最大手动重定向次数 ({MAX_MANUAL_REDIRECTS}). 最后尝试的 URL: {current_url}{Style.RESET_ALL}")
        raise TooManyRedirects(f"已超出 {MAX_MANUAL_REDIRECTS} 次手动重定向. 最后URL: {current_url}")

    except HTTPError as http_err:
        print(f"{Fore.RED}{EMOJI['HTTP_ERROR']} 发生 HTTP 错误: {http_err} (代理: {Fore.YELLOW}{proxies if proxies else '无'}{Style.RESET_ALL}){Style.RESET_ALL}")
        if http_err.response is not None:
            print(f"{Fore.RED}--- 引发 HTTPError 的响应 ({http_err.request.url} -> {http_err.response.url if hasattr(http_err.response, 'url') else 'N/A'}) 的状态码: {http_err.response.status_code}{Style.RESET_ALL}")
            # 增加 cloudflare 检查的详细日志
            is_cloudflare_error = False
            if hasattr(http_err.response, 'text') and "cloudflare" in http_err.response.text.lower():
                is_cloudflare_error = True
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 检测到响应中包含 'cloudflare' 关键词。{Style.RESET_ALL}")

            if http_err.response.status_code != 403 or not is_cloudflare_error:
                 if hasattr(http_err.response, 'text') and http_err.response.text:
                    print(f"{Fore.RED}HTTP 错误响应内容的前500字符: {http_err.response.text[:500]}...{Style.RESET_ALL}")
        raise
    except requests.exceptions.Timeout as timeout_err: # 单独捕获超时错误
        print(f"{Fore.RED}{EMOJI['TIMEOUT']} 请求超时: {url if 'current_url' not in locals() else current_url} (代理: {Fore.YELLOW}{proxies if proxies else '无'}{Style.RESET_ALL}){Style.RESET_ALL}")
        print(f"{Fore.RED}超时详情: {timeout_err}{Style.RESET_ALL}")
        raise
    except requests.exceptions.RequestException as e: # 捕获其他网络请求错误
        print(f"{Fore.RED}{EMOJI['NETWORK_ERROR']} 请求 URL 时发生错误: {url if 'current_url' not in locals() else current_url} (代理: {Fore.YELLOW}{proxies if proxies else '无'}{Style.RESET_ALL}){Style.RESET_ALL}")
        print(f"{Fore.RED}错误详情: {e}{Style.RESET_ALL}")
        raise
    except ValueError as ve: # 捕获由本函数内部逻辑引发的 ValueError
        # ValueError 已经在发生时打印过，这里只重新抛出
        raise
    except Exception as generic_err: # 捕获任何其他未预料到的错误
        print(f"{Fore.RED}{EMOJI['ERROR']} 解析URL时发生未知错误: {url if 'current_url' not in locals() else current_url} (代理: {Fore.YELLOW}{proxies if proxies else '无'}{Style.RESET_ALL}){Style.RESET_ALL}")
        print(f"{Fore.RED}错误详情: {generic_err}{Style.RESET_ALL}")
        raise

if __name__ == "__main__":
    example_url_1 = "https://auth.jike.life/login" # 替换为有多次重定向的URL进行测试
    # 示例代理，如果需要测试代理功能，请替换为有效的代理地址
    # test_proxies = {
    #    'http': 'http://your_proxy_address:port',
    #    'https': 'http://your_proxy_address:port'
    # }
    test_proxies = None # 默认不使用代理进行直接测试

    print(f"{Fore.CYAN}{Style.BRIGHT}测试 URL: {example_url_1} (代理: {Fore.YELLOW}{test_proxies if test_proxies else '无'}{Style.RESET_ALL})")
    try:
        final_url_1 = get_final_url(example_url_1, proxies=test_proxies)
        print(f"{Fore.BLUE}初始 URL: {example_url_1}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{EMOJI['URL_RESOLVED']} 最终 URL: {final_url_1}{Style.RESET_ALL}")
    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 无法获取 {example_url_1} 的最终 URL。异常类型: {type(e).__name__}{Style.RESET_ALL}")
    except ValueError as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 解析 {example_url_1} 时发生值错误: {e}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{Style.BRIGHT}{'-' * 30}{Style.RESET_ALL}")

    # 另一个测试重定向的例子 (http -> https)
    example_url_2 = "http://google.com"
    # 包含多次重定向的例子
    # example_url_3 = "http://tinyurl.com/2tx" # 这个短链接会重定向到 example.com

    print(f"{Fore.CYAN}{Style.BRIGHT}测试 URL: {example_url_2}{Style.RESET_ALL}")
    try:
        final_url_2 = get_final_url(example_url_2)
        print(f"{Fore.BLUE}初始 URL: {example_url_2}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{EMOJI['URL_RESOLVED']} 最终 URL: {final_url_2}{Style.RESET_ALL}")
    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 无法获取 {example_url_2} 的最终 URL。异常: {type(e).__name__}{Style.RESET_ALL}")
    except ValueError as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 解析 {example_url_2} 时发生值错误: {e}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{Style.BRIGHT}{'-' * 30}{Style.RESET_ALL}")

    example_url_3 = "http://github.com/nonexistentuser/nonexistentrepo" # 一个会导致404的例子
    print(f"{Fore.CYAN}{Style.BRIGHT}测试 URL (预期404): {example_url_3}{Style.RESET_ALL}")
    try:
        final_url_3 = get_final_url(example_url_3)
        print(f"{Fore.BLUE}初始 URL: {example_url_3}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{EMOJI['URL_RESOLVED']} 最终 URL: {final_url_3}{Style.RESET_ALL}")
    except HTTPError as e: # 特别捕捉 HTTPError 来显示预期的404
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 预期的 HTTP 错误: {e} (状态码: {e.response.status_code if e.response else 'N/A'}){Style.RESET_ALL}")
    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 无法获取 {example_url_3} 的最终 URL。异常: {type(e).__name__}{Style.RESET_ALL}")
    except ValueError as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 解析 {example_url_3} 时发生值错误: {e}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{Style.BRIGHT}{'-' * 30}{Style.RESET_ALL}")
    
    # # 测试一个可能出错的 URL
    invalid_url = "https://thissitedoesnotexist12345.com"
    print(f"{Fore.CYAN}{Style.BRIGHT}测试无效 URL: {invalid_url}{Style.RESET_ALL}")
    try:
        final_url_invalid = get_final_url(invalid_url)
        print(f"{Fore.BLUE}初始 URL: {invalid_url}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{EMOJI['URL_RESOLVED']} 最终 URL: {final_url_invalid}{Style.RESET_ALL}")
    except requests.exceptions.RequestException as e:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法获取 {invalid_url} 的最终 URL (符合预期)。异常: {type(e).__name__}{Style.RESET_ALL}")
    except ValueError as e: # 如果URL格式本身就有问题
        print(f"{Fore.RED}{EMOJI['ERROR']} 解析无效 URL {invalid_url} 时发生值错误: {e}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{Style.BRIGHT}{'-' * 30}{Style.RESET_ALL}")

    # 测试 @ 开头的 URL
    at_url = "@http://gitlab.com/test/test"
    print(f"{Fore.CYAN}{Style.BRIGHT}测试带 '@' 前缀的 URL: {at_url}{Style.RESET_ALL}")
    try:
        final_at_url = get_final_url(at_url)
        print(f"{Fore.BLUE}初始 URL: {at_url}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{EMOJI['URL_RESOLVED']} 最终 URL: {final_at_url}{Style.RESET_ALL}")
    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 无法获取 {at_url} 的最终 URL。异常类型: {type(e).__name__}{Style.RESET_ALL}")
    except ValueError as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 解析 {at_url} 时发生值错误: {e}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{Style.BRIGHT}{'-' * 30}{Style.RESET_ALL}")