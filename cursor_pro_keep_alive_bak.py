import os
import platform
import json
import sys
import string
from colorama import Fore, Style
from enum import Enum
from typing import Optional
import threading
import subprocess # 用于执行 Git 命令
import totally_reset_cursor # 确保导入
import pathlib
import shutil
import sqlite3

# Imports for Outlook email verification
import imaplib 
import email # For parsing email
from email.header import decode_header as decode_email_header # Alias to avoid conflict if any
import re # For regex in extract_verification_code

from exit_cursor import ExitCursor
import go_cursor_help
import patch_cursor_get_machine_id
from reset_machine import MachineIDResetter
from reset_machine_manual import run as reset_machine_manual_run
from cursor_acc_info import display_account_info, UsageManager, format_subscription_type, get_display_width # Import UsageManager and helper functions
from config_vip import get_config  # 导入config_vip中的get_config函数
from get_user_token import get_token_from_cookie # 导入新的 token 获取函数
from patch_cursor_get_machine_id import get_cursor_paths 
import bypass_token_limit
import requests
import concurrent.futures
import time # 确保导入 time

os.environ["PYTHONVERBOSE"] = "0"
os.environ["PYINSTALLER_VERBOSE"] = "0"

# import time # Already imported
import random
from cursor_auth_manager import CursorAuthManager
# import os # Already imported
from logger import logging
from browser_utils import BrowserManager
# from get_email_code import EmailVerificationHandler # This will be replaced by new logic for Outlook
from logo import print_logo
from config import Config
import traceback # 添加traceback模块导入
from datetime import datetime
import uuid
from redirect_resolver import get_final_url # +++ 新增导入 +++

# 定义 EMOJI 字典
EMOJI = {
    "ERROR": "❌",
    "WARNING": "⚠️",
    "INFO": "ℹ️",
    "SUCCESS": "✅",
    # 添加 register manual 中的 emoji
    'START': '🚀',
    'FORM': '📝',
    'VERIFY': '🔄',
    'PASSWORD': '🔑',
    'CODE': '📱',
    'DONE': '✨',
    'WAIT': '⏳',
    'MAIL': '📧',
    'KEY': '🔐',
    'UPDATE': '🔄',
    'SAVE': '💾',
    'SUBSCRIPTION': '📅',
    'TIME': '⏳',
    'USAGE': '📊',
    'PREMIUM': '⭐',
    'BASIC': '📝',
    'USER': '👤',
    'SEARCH': '🔍',
    'RESET': '🔄',
    'OUTLOOK': '🔵' # Added for Outlook specific messages
}

# 定义日志和账号文件常量
LOG_DIR = "log"
# ACCOUNT_FILE = os.path.join(LOG_DIR, "account_info.json") # 旧的单账号文件
ACCOUNTS_JSON_FILE = os.path.join(LOG_DIR, "accounts.json") # 新的多账号文件
OUTLOOKS_TXT_FILE = os.path.join(LOG_DIR, "outlooks.txt") # Added for Outlook accounts

# --- BEGIN Outlook Email Verification Functions ---
# (Copied and adapted from read_outlook_inbox.py logic)

def get_outlook_access_token_from_refresh_token(refresh_token, client_id, translator=None):
    headers = {
        'Host': 'login.microsoftonline.com',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
    }
    data = {
        "client_id": client_id,
        "refresh_token": refresh_token,
        "grant_type": "refresh_token",
        "scope": "https://outlook.office.com/IMAP.AccessAsUser.All https://outlook.office.com/SMTP.Send offline_access"
    }
    token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
    try:
        response = requests.post(token_url, headers=headers, data=data, timeout=15) # Added timeout
        response.raise_for_status()
        token_data = response.json()
        if "access_token" in token_data:
            new_refresh_token = token_data.get("refresh_token")
            return {
                "code": 0,
                "access_token": token_data["access_token"],
                "refresh_token": new_refresh_token or refresh_token
            }
        error_msg = token_data.get("error_description", "Unknown error from token endpoint")
        logging.warning(f"Failed to get Outlook access token: {error_msg}")
        if translator:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('outlook.token_error', error=error_msg) if translator else f'Outlook令牌获取失败: {error_msg}'}{Style.RESET_ALL}")
        return {"code": 1, "message": error_msg}
    except requests.exceptions.RequestException as e:
        logging.error(f"Network error getting Outlook access token: {e}")
        if translator:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.token_network_error', error=str(e)) if translator else f'Outlook令牌网络错误: {str(e)}'}{Style.RESET_ALL}")
        return {"code": 1, "message": f"Network error: {e}"}
    except json.JSONDecodeError as e:
        logging.error(f"JSON decode error for Outlook access token: {e}")
        if translator:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.token_json_error', error=str(e)) if translator else f'Outlook令牌JSON解析错误: {str(e)}'}{Style.RESET_ALL}")
        return {"code": 1, "message": f"JSON decode error: {e}"}

def imap_outlook_authenticate_with_oauth2(username, access_token, translator=None):
    try:
        auth_string = f"user={username}\\1auth=Bearer {access_token}\\1\\1"
        mail = imaplib.IMAP4_SSL("outlook.office365.com", timeout=15) # Added timeout
        mail.authenticate("XOAUTH2", lambda x: auth_string.encode())
        return mail
    except imaplib.IMAP4.error as e_office:
        logging.warning(f"IMAP auth failed for {username} on outlook.office365.com: {e_office}")
        try:
            # if translator: # Removed for brevity, original script does not show this to user usually
            #     print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('outlook.imap_office365_fail_retry', server='imap-mail.outlook.com') if translator else 'outlook.office365.com认证失败，尝试备用服务器 imap-mail.outlook.com...'}{Style.RESET_ALL}")
            mail_alt = imaplib.IMAP4_SSL("imap-mail.outlook.com", timeout=15) # Added timeout
            mail_alt.authenticate("XOAUTH2", lambda x: auth_string.encode())
            return mail_alt
        except imaplib.IMAP4.error as e_personal:
            logging.error(f"IMAP auth failed for {username} on imap-mail.outlook.com: {e_personal}")
            if translator:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.imap_auth_failed_all', user=username, error=str(e_personal)) if translator else f'Outlook IMAP认证失败 ({username}): {e_personal}'}{Style.RESET_ALL}")
            return None
    except Exception as e_generic: # Catch other errors like socket errors
        logging.error(f"Generic IMAP connection/auth error for {username}: {e_generic}")
        if translator:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.imap_connect_error', user=username, error=str(e_generic)) if translator else f'Outlook IMAP连接错误 ({username}): {e_generic}'}{Style.RESET_ALL}")
        return None

def get_outlook_email_text_body(email_message): # email_message is email.message.Message
    text_body = ""
    if email_message.is_multipart():
        for part in email_message.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))
            if content_type == "text/plain" and "attachment" not in content_disposition:
                try:
                    payload = part.get_payload(decode=True)
                    charset = part.get_content_charset() or 'utf-8'
                    text_body = payload.decode(charset, errors='replace')
                    break 
                except Exception:
                    continue
    else:
        content_type = email_message.get_content_type()
        if content_type == "text/plain":
            try:
                payload = email_message.get_payload(decode=True)
                charset = email_message.get_content_charset() or 'utf-8'
                text_body = payload.decode(charset, errors='replace')
            except Exception:
                pass
    return text_body.strip()

def extract_email_verification_code(email_body_text): # Renamed to be more generic
    if not email_body_text:
        return None
    
    patterns = [
        r"(?:验证码|verification code|your code|code is|OTP|动态密码|校验码|激活码|pin is|security code|is your code|access code)[是:：\\s]*(\\b\\d{4,8}\\b)",
        r":\\s*(\\b\\d{4,8}\\b)",
        r"^\\s*(\\d{6})\\s*$", # Specifically for 6-digit codes on a new line
        r"^\\s*(\\d{4,8})\\s*$", # For 4-8 digit codes on a new line
        r"(\\b\\d{4,8}\\b)(?:[\\s]*(?:是|is your|is the|is|为|code|OTP))",
        r"\\b(\\d{6})\\b", # Fallback for any 6-digit code
        r"\\b(\\d{4,8})\\b" # Fallback for any 4-8 digit code
    ]

    for pattern in patterns:
        # For patterns using ^ and $, re.MULTILINE is needed
        flags = re.IGNORECASE
        if "^" in pattern or "$" in pattern:
            flags |= re.MULTILINE
        
        match = re.search(pattern, email_body_text, flags)
        if match:
            # Group 1 is usually the digits. If no group, use group 0.
            return match.group(1) if match.groups() else match.group(0)
    return None

def fetch_outlook_verification_code(username, current_refresh_token, client_id, translator=None):
    token_response = get_outlook_access_token_from_refresh_token(current_refresh_token, client_id, translator)
    
    if not token_response or token_response.get("code") != 0:
        return None, current_refresh_token 

    access_token = token_response["access_token"]
    updated_refresh_token = token_response["refresh_token"]

    mail = imap_outlook_authenticate_with_oauth2(username, access_token, translator)
    if not mail:
        return None, updated_refresh_token

    verification_code_found = None
    try:
        status, _ = mail.select("inbox")
        if status != 'OK':
            mail.logout()
            return None, updated_refresh_token

        status, messages = mail.search(None, 'ALL')
        if status != 'OK':
            mail.logout()
            return None, updated_refresh_token

        email_ids = messages[0].split()
        if not email_ids:
            mail.logout()
            return None, updated_refresh_token
            
        latest_mail_id = email_ids[-1]
        status, msg_data = mail.fetch(latest_mail_id, '(RFC822)')
        
        if status == 'OK':
            raw_email = msg_data[0][1]
            # Ensure correct aliased import is used here if `decode_email_header` was aliased
            email_message = email.message_from_bytes(raw_email) # `email` is the module
            email_body = get_outlook_email_text_body(email_message)
            if email_body:
                code = extract_email_verification_code(email_body)
                if code:
                    verification_code_found = code
        mail.logout()
        return verification_code_found, updated_refresh_token
    except Exception as e_imap:
        logging.error(f"Error during IMAP operations for {username}: {e_imap}")
        if translator:
             print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.imap_op_error', user=username, error=str(e_imap)) if translator else f'Outlook IMAP操作错误 ({username}): {e_imap}'}{Style.RESET_ALL}")
        if mail:
            try:
                mail.logout()
            except:
                pass
        return None, updated_refresh_token

# --- END Outlook Email Verification Functions ---

def wait_with_spinner(duration: float, message: str = "Loading"):
    """在终端显示旋转动画等待指定时间"""
    spinners = ['|', '/', '-', '\\']
    start_time = time.time()
    # 如果总时长很短，减少打印间隔，避免动画效果太差或不显示
    sleep_interval = min(0.1, duration / 4) if duration > 0 else 0.1 
    
    line_width = 0 # 用于记录上一行的宽度
    
    while time.time() - start_time < duration:
        loop_start_time = time.time()
        for spinner in spinners:
            current_elapsed = time.time() - start_time
            if current_elapsed >= duration:
                break
            # 使用 \r 回到行首覆盖，显示加载信息和旋转动画
            # 确保清除足够长度以覆盖之前的输出
            # clear_line = '\n' + ' ' * (len(message) + 20) + '\n' 
            # sys.stdout.write(f'{clear_line}{Fore.CYAN}{EMOJI.get("WAIT", "⏳")} {message}... {spinner}{Style.RESET_ALL} ')
            
            output_string = f"{Fore.CYAN}{EMOJI.get('WAIT', '⏳')} {message}... {spinner}{Style.RESET_ALL}"
            # 使用回车符 \r 将光标移到行首
            # 使用 ljust 以空格填充，确保覆盖上一行的内容
            sys.stdout.write(f"\r{output_string}".ljust(line_width))
            sys.stdout.flush()
            # 实际打印的字符数可能因ANSI转义序列而异，但为了覆盖，使用无转义序列的长度通常足够。
            # 或者更准确地，我们可以估算一个足够大的宽度，或者计算剥离ANSI后的字符串长度。
            # 为简单起见，我们先用 output_string 的原始长度。
            line_width = len(output_string) # 更新当前行宽度

            # 短暂休眠以控制动画速度
            time.sleep(sleep_interval)
            # 再次检查时间，避免在 sleep 后超出总时长
            if time.time() - start_time >= duration:
                 break
        # 如果内循环因为 spinner 走完而结束，检查是否还需要等待
        if time.time() - start_time >= duration:
             break
        # 如果内循环很快（例如 sleep_interval 极小），确保至少等待一点时间
        if time.time() - loop_start_time < sleep_interval * len(spinners) :
             time.sleep(max(0, sleep_interval * len(spinners) - (time.time() - loop_start_time) ))
             
    # 清除加载行并换行
    # sys.stdout.write('\n' + ' ' * (len(message) + 20) + '\n') # 清除足够宽度的字符
    # 清除最后一行加载动画
    sys.stdout.write('\r'.ljust(line_width + 5)) # 多加一些空格确保清除干净
    sys.stdout.write('\r') # 光标移到行首
    sys.stdout.flush()
    # 最终完成时可以打印一个换行，如果需要的话
    # print() # 如果希望在 spinner 结束后有一个干净的换行

class VerificationStatus(Enum):
    PASSWORD_PAGE = "@name=password"
    CAPTCHA_PAGE = "@data-index=0"
    ACCOUNT_SETTINGS = "Account Settings"

class TurnstileError(Exception):
    pass

def save_screenshot(tab, stage: str, timestamp: bool = True) -> None:
    try:
        screenshot_dir = "screenshots"
        if not os.path.exists(screenshot_dir):
            os.makedirs(screenshot_dir)
        if timestamp:
            filename = f"turnstile_{stage}_{int(time.time())}.png"
        else:
            filename = f"turnstile_{stage}.png"
        filepath = os.path.join(screenshot_dir, filename)
        tab.get_screenshot(filepath)
        logging.debug(f"截图已保存: {filepath}")
    except Exception as e:
        logging.warning(f"截图保存失败: {str(e)}")

def check_verification_success(tab, translator=None) -> Optional[VerificationStatus]:
    for status in VerificationStatus:
        if tab.ele(status.value):
            return status
    return None

def handle_turnstile(tab, max_retries: int = 2, retry_interval: tuple = (1, 2), translator=None) -> bool:
    print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('verify.turnstile_check') if translator else '正在检测 Turnstile 验证...'}{Style.RESET_ALL}")
    retry_count = 0
    verification_status = None
    try:
        while retry_count < max_retries:
            retry_count += 1
            try:
                challenge_check = (
                    tab.ele("@id=cf-turnstile", timeout=2)
                    .child()
                    .shadow_root.ele("tag:iframe")
                    .ele("tag:body")
                    .sr("tag:input")
                )
                if challenge_check:
                    time.sleep(random.uniform(0.5, 1.0))
                    challenge_check.click()
                    time.sleep(1.0)
                    verification_status = check_verification_success(tab, translator)
                    if verification_status:
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('verify.success_page', page=verification_status.name) if translator else f'验证成功 - 已到达{verification_status.name}页面'}{Style.RESET_ALL}")
                        return True
            except Exception:
                pass
            verification_status = check_verification_success(tab, translator)
            if verification_status:
                 print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('verify.success_page', page=verification_status.name) if translator else f'验证成功 - 已到达{verification_status.name}页面'}{Style.RESET_ALL}")
                 return True
            time.sleep(random.uniform(*retry_interval))
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('verify.turnstile_max_retries', max=max_retries) if translator else f'验证失败 - 已达到最大重试次数 {max_retries}'}{Style.RESET_ALL}")
        return False
    except Exception as e:
        error_msg = f"Turnstile 验证过程发生异常: {str(e)}"
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('verify.turnstile_exception', error=error_msg) if translator else error_msg}{Style.RESET_ALL}")
        raise TurnstileError(error_msg)

def save_account_to_json(email, password, token_info, translator=None):
    try:
        if not os.path.exists(LOG_DIR):
            os.makedirs(LOG_DIR)
        accounts = load_accounts_from_json(translator)
        new_account = {
            "email": email,
            "password": password,
            "saved_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        if isinstance(token_info, dict):
            new_account["token"] = token_info.get("token")
            new_account["days_left"] = token_info.get("days_left")
            new_account["expire_time"] = token_info.get("expire_time")
            new_account["token_type"] = "refreshed"
            if "session_id" in token_info:
                new_account["session_id"] = token_info.get("session_id")
        elif isinstance(token_info, str):
             new_account["token"] = token_info
             new_account["token_type"] = "extracted"
             new_account["days_left"] = None
             new_account["expire_time"] = None
        else:
             new_account["token"] = None
             new_account["token_type"] = "unknown"
             new_account["days_left"] = None
             new_account["expire_time"] = None
        found = False
        for i, acc in enumerate(accounts):
             if acc.get("email") == email:
                  accounts[i] = new_account
                  found = True
                  print(f"{Fore.YELLOW}{EMOJI['UPDATE']} {translator.get('accounts.updated', email=email, file=ACCOUNTS_JSON_FILE) if translator else f'账号 {email} 已存在于 {ACCOUNTS_JSON_FILE}，信息已更新。'}{Style.RESET_ALL}")
                  break
        if not found:
            accounts.append(new_account)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.added', email=email, file=ACCOUNTS_JSON_FILE) if translator else f'新账号 {email} 已添加到 {ACCOUNTS_JSON_FILE}'}{Style.RESET_ALL}")
        with open(ACCOUNTS_JSON_FILE, "w", encoding="utf-8") as f:
            json.dump(accounts, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.save_error', file=ACCOUNTS_JSON_FILE, error=str(e)) if translator else f'保存账号到 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}'}{Style.RESET_ALL}")
        return False

def load_accounts_from_json(translator=None):
    if not os.path.exists(ACCOUNTS_JSON_FILE):
        return []
    try:
        with open(ACCOUNTS_JSON_FILE, "r", encoding="utf-8") as f:
            accounts = json.load(f)
            if not isinstance(accounts, list):
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.json_corrupted', file=ACCOUNTS_JSON_FILE) if translator else f'警告: {ACCOUNTS_JSON_FILE} 格式错误，将返回空列表。'}{Style.RESET_ALL}")
                return []
            return accounts
    except json.JSONDecodeError:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.json_decode_error', file=ACCOUNTS_JSON_FILE) if translator else f'警告: {ACCOUNTS_JSON_FILE} 解析失败，将返回空列表。'}{Style.RESET_ALL}")
        return []
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.json_read_error', file=ACCOUNTS_JSON_FILE, error=str(e)) if translator else f'读取 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}，将返回空列表。'}{Style.RESET_ALL}")
        return []

def delete_account_from_json(index_to_delete, translator=None):
    accounts = load_accounts_from_json(translator)
    if not accounts:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.delete.no_accounts', file=ACCOUNTS_JSON_FILE) if translator else f'账号文件 {ACCOUNTS_JSON_FILE} 为空，无法删除。'}{Style.RESET_ALL}")
        return False
    if not (0 <= index_to_delete < len(accounts)):
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.delete.invalid_index') if translator else '无效的账号序号。'}{Style.RESET_ALL}")
        return False
    deleted_account = accounts.pop(index_to_delete)
    deleted_email = deleted_account.get("email", "未知邮箱")
    try:
        with open(ACCOUNTS_JSON_FILE, "w", encoding="utf-8") as f:
            json.dump(accounts, f, ensure_ascii=False, indent=4)
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.delete.success', email=deleted_email) if translator else f'账号 {deleted_email} 已成功删除。'}{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.delete.write_error', file=ACCOUNTS_JSON_FILE, error=str(e)) if translator else f'写入更新到 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}'}{Style.RESET_ALL}")
        return False

def get_cursor_session_token(tab, max_attempts=3, retry_interval=2):
    logging.info("开始获取cookie")
    attempts = 0
    while attempts < max_attempts:
        try:
            cookies = tab.cookies()
            for cookie in cookies:
                if cookie.get("name") == "WorkosCursorSessionToken":
                    return cookie["value"].split("%3A%3A")[1]
            attempts += 1
            if attempts < max_attempts:
                logging.warning(f"第 {attempts} 次尝试未获取到CursorSessionToken，{retry_interval}秒后重试...")
                time.sleep(retry_interval)
            else:
                logging.error(f"已达到最大尝试次数({max_attempts})，获取CursorSessionToken失败")
        except Exception as e:
            logging.error(f"获取cookie失败: {str(e)}")
            attempts += 1
            if attempts < max_attempts:
                logging.info(f"将在 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
    return None

def update_cursor_auth(email=None, access_token=None, refresh_token=None):
    auth_manager = CursorAuthManager()
    return auth_manager.update_auth(email, access_token, refresh_token)


# Modify sign_up_account to use Outlook details for verification
def sign_up_account(
    browser, 
    tab, 
    cursor_email_to_register, # This email will be used for Cursor registration
    sign_up_url, 
    settings_url, 
    translator=None,
    # New parameters for Outlook verification
    outlook_email_for_verification=None, 
    outlook_refresh_token=None, 
    outlook_client_id=None,
    user_agent=None,  # Added
    proxies=None      # Added
    ):
    """处理完整的账号注册流程，并在成功后保存账号信息"""
    print(f"{Fore.CYAN}{EMOJI['START']} {translator.get('register.start_flow') if translator else '=== 开始注册账号流程 ==='}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['USER']} {translator.get('register.using_email_for_cursor', email=cursor_email_to_register) if translator else f'将使用邮箱 {cursor_email_to_register} 注册Cursor账号。'}{Style.RESET_ALL}")
    if outlook_email_for_verification:
        print(f"{Fore.CYAN}{EMOJI['OUTLOOK']} {translator.get('register.using_outlook_for_code', email=outlook_email_for_verification) if translator else f'将使用Outlook邮箱 {outlook_email_for_verification} 获取验证码。'}{Style.RESET_ALL}")

    actual_sign_up_url = sign_up_url
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('register.resolve_signup_url_start', url=sign_up_url) if translator else f'开始解析注册 URL: {sign_up_url}'}{Style.RESET_ALL}")
        resolved_url = get_final_url(sign_up_url, proxies=proxies) 
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.resolve_signup_url_success', url=resolved_url) if translator else f'注册 URL 解析成功，将使用: {resolved_url}'}{Style.RESET_ALL}")
        actual_sign_up_url = resolved_url
    except (requests.exceptions.RequestException, ValueError) as e_resolve:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.resolve_signup_url_failed', url=sign_up_url, error=str(e_resolve)) if translator else f'解析注册 URL {sign_up_url} 失败: {str(e_resolve)}'}{Style.RESET_ALL}")
        return False, None # Return new refresh token as None on failure
    except Exception as e_resolve_other:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.resolve_signup_url_unexpected', url=sign_up_url, error=str(e_resolve_other)) if translator else f'解析注册 URL {sign_up_url} 时发生未知错误: {str(e_resolve_other)}'}{Style.RESET_ALL}")
        return False, None

    print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('register.visit_page', url=actual_sign_up_url) if translator else f'正在访问注册页面: {actual_sign_up_url}'}{Style.RESET_ALL}")
    tab.get(actual_sign_up_url)
    wait_with_spinner(random.uniform(3, 5), translator.get('register.wait_signup_page_load') if translator else "Waiting for sign-up page to load")

    try:
        # 查找邮箱输入框
        email_input_selector = "@name=email"
        if tab.ele(email_input_selector):
            print(f"{Fore.CYAN}{EMOJI['FORM']} {translator.get('register.fill_email_only') if translator else '正在填写邮箱...'}{Style.RESET_ALL}")
            # 使用 cursor_email_to_register 填写邮箱
            tab.actions.click(email_input_selector).input(cursor_email_to_register)
            print(f"    {Fore.CYAN}➡️ {translator.get('register.field.email') if translator else '邮箱'}: {cursor_email_to_register}{Style.RESET_ALL}")
            wait_with_spinner(random.uniform(0.5, 1.5), "Waiting after input")
            
            # 提交邮箱
            submit_button_selector = "@type=submit" # 假设提交按钮选择器不变
            if tab.ele(submit_button_selector):
                print(f"{Fore.CYAN}{EMOJI['FORM']} {translator.get('register.submit_email_only') if translator else '提交邮箱...'}{Style.RESET_ALL}")
                tab.actions.click(submit_button_selector)
                wait_with_spinner(random.uniform(2, 4), translator.get('register.wait_after_submit') if translator else "Waiting after submit")

                # --- BEGIN: Loop to find and click 'intent' button ---
                intent_button_found = False
                intent_button_selector = "@name=intent"
                max_intent_retries = 6
                for attempt in range(max_intent_retries):
                    print(f"{Fore.CYAN}{EMOJI['WAIT']} {translator.get('register.check_intent_button_attempt', attempt=attempt + 1, max_attempts=max_intent_retries, selector=intent_button_selector) if translator else f'尝试 {attempt + 1}/{max_intent_retries} 查找按钮 ({intent_button_selector})...'}{Style.RESET_ALL}")
                    try:
                        if tab.ele(intent_button_selector, timeout=0.5).exists(): # Check existence first
                            intent_button = tab.ele(intent_button_selector) # Get the element
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.intent_button_found_clicking', selector=intent_button_selector) if translator else f'找到按钮 ({intent_button_selector})，正在点击...'}{Style.RESET_ALL}")
                            intent_button.click()
                            intent_button_found = True
                            wait_with_spinner(random.uniform(1, 3), translator.get('register.wait_after_intent_click') if translator else "点击按钮后等待...")
                            break # Exit loop if button is clicked
                    except Exception:
                        pass

                    if attempt < max_intent_retries - 1:
                        wait_duration = random.uniform(1.5, 2.5)
                        print(f"{Fore.YELLOW}{EMOJI['WAIT']} {translator.get('register.intent_button_not_found_retry', selector=intent_button_selector, wait_time=f'{wait_duration:.1f}') if translator else f'按钮 ({intent_button_selector}) 未找到，{wait_duration:.1f}秒后重试...'}{Style.RESET_ALL}")
                        time.sleep(wait_duration)
                    else:
                        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.intent_button_not_found_max_retries', selector=intent_button_selector, max_attempts=max_intent_retries) if translator else f'重试 {max_intent_retries} 次后仍未找到按钮 ({intent_button_selector})。'}{Style.RESET_ALL}")
                        return False, None

                if not intent_button_found:
                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.intent_button_click_failed_safeguard', selector=intent_button_selector) if translator else f'未能成功点击按钮 ({intent_button_selector})。'}{Style.RESET_ALL}")
                    return False, None
                # --- END: Loop to find and click 'intent' button ---

                # --- BEGIN: Outlook Verification and Account Settings Check (Moved here) ---
                final_outlook_refresh_token = outlook_refresh_token 

                if outlook_email_for_verification and outlook_refresh_token and outlook_client_id:
                    print(f"{Fore.CYAN}{EMOJI['OUTLOOK']} {translator.get('register.get_email_code_outlook_start', email=outlook_email_for_verification) if translator else f'开始从Outlook邮箱 {outlook_email_for_verification} 获取验证码...'}{Style.RESET_ALL}")
                    
                    max_outlook_code_retries = 3
                    outlook_code_attempt = 0
                    code_from_outlook = None

                    while outlook_code_attempt < max_outlook_code_retries and code_from_outlook is None:
                        outlook_code_attempt += 1
                        print(f"{Fore.CYAN}{EMOJI['WAIT']} {translator.get('outlook.fetch_code_attempt', attempt=outlook_code_attempt, max_attempts=max_outlook_code_retries) if translator else f'尝试 {outlook_code_attempt}/{max_outlook_code_retries} 从Outlook获取验证码...'}{Style.RESET_ALL}")
                        
                        code_from_outlook, updated_outlook_refresh_token = fetch_outlook_verification_code(
                            outlook_email_for_verification, 
                            final_outlook_refresh_token,
                            outlook_client_id,
                            translator
                        )
                        if updated_outlook_refresh_token and updated_outlook_refresh_token != final_outlook_refresh_token:
                            print(f"{Fore.YELLOW}{EMOJI['UPDATE']} {translator.get('outlook.refresh_token_updated_inline', email=outlook_email_for_verification) if translator else f'Outlook邮箱 {outlook_email_for_verification} 的刷新令牌已更新。'}{Style.RESET_ALL}")
                            final_outlook_refresh_token = updated_outlook_refresh_token

                        if code_from_outlook:
                            break
                        elif outlook_code_attempt < max_outlook_code_retries:
                            print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('outlook.fetch_code_failed_retry', email=outlook_email_for_verification) if translator else f'未能从 {outlook_email_for_verification} 获取验证码，稍后重试...'}{Style.RESET_ALL}")
                            time.sleep(5)
                    
                    if not code_from_outlook:
                        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.get_code_failed_outlook', email=outlook_email_for_verification) if translator else f'从Outlook邮箱 {outlook_email_for_verification} 获取验证码失败。'}{Style.RESET_ALL}")
                        return False, final_outlook_refresh_token
                    
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.code_received_outlook', code=code_from_outlook, email=outlook_email_for_verification) if translator else f'成功从Outlook邮箱 {outlook_email_for_verification} 获取验证码: {code_from_outlook}'}{Style.RESET_ALL}")
                    
                    if tab.ele(VerificationStatus.CAPTCHA_PAGE.value, timeout=5):
                        print(f"{Fore.CYAN}{EMOJI['CODE']} {translator.get('register.enter_code') if translator else '正在输入验证码...'}{Style.RESET_ALL}")
                        i = 0
                        for digit_char in code_from_outlook:
                            tab.ele(f"@data-index={i}").input(digit_char)
                            time.sleep(0.2)
                            i += 1
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.code_entered') if translator else '验证码输入完成'}{Style.RESET_ALL}")
                        wait_with_spinner(random.uniform(1, 2), translator.get('register.wait_after_code') if translator else "Waiting after code entry")
                    else:
                        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.captcha_page_not_found_after_outlook') if translator else '获取Outlook验证码后，未找到Cursor的验证码输入页面。'}{Style.RESET_ALL}")
                        return False, final_outlook_refresh_token
                else:
                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.outlook_details_missing') if translator else 'Outlook账户信息未提供给注册流程，无法获取验证码。'}{Style.RESET_ALL}")
                    return False, final_outlook_refresh_token

                print(f"{Fore.CYAN}{EMOJI['WAIT']} {translator.get('register.wait_account_settings_page') if translator else '等待账户设置页面加载...'}{Style.RESET_ALL}")
                account_settings_found = False
                wait_settings_timeout = 15
                start_settings_wait = time.time()
                while time.time() - start_settings_wait < wait_settings_timeout:
                    status_final_check = check_verification_success(tab, translator)
                    if status_final_check == VerificationStatus.ACCOUNT_SETTINGS:
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('verify.success_page', page=status_final_check.name) if translator else f'验证成功 - 已到达{status_final_check.name}页面'}{Style.RESET_ALL}")
                        account_settings_found = True
                        break
                    time.sleep(0.5)

                if not account_settings_found:
                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.account_settings_not_reached') if translator else '未能到达账户设置页面，注册可能未成功。'}{Style.RESET_ALL}")
                    return False, final_outlook_refresh_token

                print(f"\\n{Fore.GREEN}{EMOJI['DONE']} {translator.get('register.register_complete') if translator else '=== 注册账号步骤完成 ==='}{Style.RESET_ALL}")
                return True, final_outlook_refresh_token
                # --- END: Outlook Verification and Account Settings Check ---

            else: # submit_button_selector not found
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.element_not_found', element=submit_button_selector) if translator else f'提交按钮 {submit_button_selector} 未找到'}{Style.RESET_ALL}")
                return False, None
        else: # email_input_selector not found
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.element_not_found', element=email_input_selector) if translator else f'邮箱输入框 {email_input_selector} 未找到'}{Style.RESET_ALL}")
            return False, None
    except Exception as e: # Page load or fill failed
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.page_load_failed', error=str(e)) if translator else f'注册页面访问或填写失败: {str(e)}'}{Style.RESET_ALL}")
        return False, None

    # Removed Turnstile check here
    # Removed password setting block here
    # Removed email taken check here
    # Removed second Turnstile check here
    # The Outlook verification and account settings check logic has been moved up

    # Fallback if logic doesn't hit a return path (should not happen with current structure)
    # print(f"{Fore.RED}{EMOJI['ERROR']} Unexpected end of registration function.{Style.RESET_ALL}")
    # return False, outlook_refresh_token # Ensure outlook_refresh_token is defined or use initial

# ... (get_user_agent, check_cursor_version, reset_machine_id, print_end_message functions remain unchanged)
# ... (monitor_email_for_verification can be removed if not used elsewhere, or kept if general login monitoring is still a feature)
# ... (restart_cursor remains unchanged)

# Modify git_sync_accounts to git_sync_log_directory
def git_sync_log_directory(commit_message, translator=None):
    """同步整个 log 目录到 Git 仓库"""
    print(f"\\n{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('git.sync.start_log_dir') if translator else '开始同步log目录到 Git...'}{Style.RESET_ALL}")
    sync_successful = True
    
    # 1. 添加整个 log 目录
    # Using os.path.join for cross-platform compatibility for 'log/'
    log_dir_for_git = os.path.join(".", LOG_DIR) # Ensures correct path format like 'log' or 'log\'
    if not run_git_command(f"git add \"{log_dir_for_git}\""): # Quote path for safety
        sync_successful = False

    # 2. 提交更改 (仅当 add 成功时)
    if sync_successful:
        commit_cmd = f'git commit -m "{commit_message}"' 
        status_result = subprocess.run("git status --porcelain", shell=True, capture_output=True, text=True)
        # Check if anything in LOG_DIR is staged for commit
        if LOG_DIR in status_result.stdout: 
            if not run_git_command(commit_cmd):
                sync_successful = False
        else:
             print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('git.sync.no_changes_in_log_dir', dir=LOG_DIR) if translator else f'{LOG_DIR} 目录没有检测到更改，无需提交。'}{Style.RESET_ALL}")
             pass

    # 3. 推送更改 (仅当 commit 成功或无需 commit 时)
    if sync_successful:
         if not run_git_command("git push"):
              sync_successful = False
              
    if sync_successful:
         print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('git.sync.success_log_dir') if translator else 'log目录 Git 同步成功！'}{Style.RESET_ALL}")
    else:
         print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('git.sync.failed_log_dir') if translator else 'log目录 Git 同步失败。请检查上面的错误信息。'}{Style.RESET_ALL}")
         
    return sync_successful

# ... (quick_switch_account, clean_cursor_cache, get_new_proxy, get_new_proxy_from_juliang_only, get_session_info functions remain unchanged)
# ... (Helper functions like run_git_command also remain unchanged)
def get_user_agent():
    from browser_utils import BrowserManager 
    browser_manager_temp = None
    from DrissionPage import ChromiumOptions, Chromium 
    try:
        browser_manager_temp = BrowserManager()
        temp_co = ChromiumOptions(); temp_co.headless(True)
        temp_browser = Chromium(temp_co)
        user_agent = temp_browser.latest_tab.run_js("return navigator.userAgent")
        temp_browser.quit()
        user_agent = user_agent.replace("HeadlessChrome", "Chrome")
        logging.info(f"获取到的 User Agent (已处理): {user_agent}")
        return user_agent
    except Exception as e:
        logging.error(f"获取 user agent 失败: {str(e)}")
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    finally:
        if 'temp_browser' in locals() and temp_browser:
             try: temp_browser.quit()
             except: pass
def check_cursor_version():
    pkg_path, main_path = patch_cursor_get_machine_id.get_cursor_paths()
    with open(pkg_path, "r", encoding="utf-8") as f:
        version = json.load(f)["version"]
    return patch_cursor_get_machine_id.version_check(version, min_version="0.45.0")
def reset_machine_id(greater_than_0_45):
    print(f"{Fore.CYAN}{EMOJI['INFO']} 开始重置机器码...{Style.RESET_ALL}")
    from config_vip import get_config
    translator = None
    try:
        from main import translator as main_translator
        translator = main_translator
    except ImportError:
        pass
    reset_success = reset_machine_manual_run(translator)
    if not reset_success:
        print(f"{Fore.RED}{EMOJI['ERROR']} 机器码重置失败{Style.RESET_ALL}")
def print_end_message():
    logging.info("\\n\\n"); logging.info("=" * 30); logging.info("所有操作已完成"); logging.info("=" * 30)
def restart_cursor():
    logging.info("尝试重启 Cursor...")
    system = platform.system()
    try:
        pkg_path, main_path = get_cursor_paths()
        app_resource_path = os.path.dirname(os.path.dirname(main_path))
        if system == "Darwin":
            cursor_app_path = os.path.abspath(os.path.join(app_resource_path, "..", "..", ".."))
            if cursor_app_path.endswith(".app"):
                logging.info(f"检测到 macOS, 使用 'open -a \"{cursor_app_path}\"' 命令")
                os.system(f'open -a "{cursor_app_path}"')
            else:
                 logging.info("检测到 macOS, 路径推断可能失败, 使用通用 'open -a Cursor' 命令")
                 os.system("open -a Cursor")
        elif system == "Windows":
            logging.info("检测到 Windows, 尝试从内部路径推断并启动 Cursor.exe")
            install_dir_guess = os.path.abspath(os.path.join(app_resource_path, "..", "..")) 
            exe_path = os.path.join(install_dir_guess, "Cursor.exe")
            if not os.path.exists(exe_path):
                 install_dir_guess_alt = os.path.abspath(os.path.join(app_resource_path, "..", "..", ".."))
                 exe_path_alt = os.path.join(install_dir_guess_alt, "Cursor.exe")
                 if os.path.exists(exe_path_alt): exe_path = exe_path_alt
                 else: logging.warning(f"无法根据内部路径 {main_path} 推断出 Cursor.exe 的位置。无法自动重启。"); exe_path = None
            if exe_path and os.path.exists(exe_path):
                logging.info(f"找到 Cursor 可执行文件: {exe_path}, 使用 'start' 命令启动")
                os.system(f'start "" "{exe_path}"')
        elif system == "Linux":
            logging.info("检测到 Linux, 尝试从内部路径推断并执行 Cursor")
            install_dir_guess = os.path.abspath(os.path.join(app_resource_path, "..", ".."))
            possible_exe_names = ['cursor', 'Cursor', 'cursor-appimage', 'AppRun']
            exe_path = None
            if os.path.isdir(install_dir_guess):
                 for name in possible_exe_names:
                      potential_path = os.path.join(install_dir_guess, name)
                      if os.path.exists(potential_path) and os.access(potential_path, os.X_OK): exe_path = potential_path; break
            if not exe_path:
                 import shutil
                 logging.info("在推断目录未找到，尝试在系统 PATH 中查找...")
                 for name in possible_exe_names:
                      found_path = shutil.which(name)
                      if found_path: exe_path = found_path; break
            if exe_path:
                logging.info(f"找到 Cursor 可执行文件: {exe_path}, 尝试在后台执行")
                os.system(f'nohup "{exe_path}" > /dev/null 2>&1 &')
            else: logging.warning("无法根据内部路径或系统 PATH 找到 Cursor 可执行文件，无法自动重启。")
        else: logging.warning(f"不支持的操作系统: {system}，无法自动重启。")
        logging.info("重启命令已发送 (如果找到应用)。")
    except OSError as e: logging.error(f"获取 Cursor 路径时出错: {str(e)}，无法自动重启。")
    except Exception as e: logging.error(f"重启 Cursor 时出错: {str(e)}")

def run_git_command(command, cwd="."):
    try:
        print(f"{Fore.BLUE}ℹ️  执行 Git 命令: {command}{Style.RESET_ALL}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True, cwd=cwd)
        if result.stdout: print(f"{Fore.GREEN}✅ Git 输出:\\n{result.stdout}{Style.RESET_ALL}")
        if result.stderr: print(f"{Fore.YELLOW}ℹ️  Git 提示:\\n{result.stderr}{Style.RESET_ALL}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"{Fore.RED}❌ Git 命令执行失败: {command}{Style.RESET_ALL}")
        print(f"{Fore.RED}   错误码: {e.returncode}{Style.RESET_ALL}")
        if e.stdout: print(f"{Fore.RED}   标准输出:\\n{e.stdout}{Style.RESET_ALL}")
        if e.stderr: print(f"{Fore.RED}   错误输出:\\n{e.stderr}{Style.RESET_ALL}")
        return False
    except Exception as e:
        print(f"{Fore.RED}❌ 执行 Git 命令时发生异常: {str(e)}{Style.RESET_ALL}")
        return False

def quick_switch_account(translator=None):
    print(f"\\n{Fore.CYAN}{'━' * 40}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['UPDATE']} 开始执行快速切换账号流程...{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'─' * 40}{Style.RESET_ALL}")
    auth_manager = CursorAuthManager()
    current_auth_info = auth_manager.get_current_auth_info()
    if not current_auth_info or not current_auth_info.get('email'):
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.quick_switch.get_current_failed') if translator else '无法获取当前登录的账号信息，无法继续切换。'}{Style.RESET_ALL}")
        return
    current_email = current_auth_info['email']
    print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('accounts.quick_switch.current_account', email=current_email) if translator else f'当前账号: {current_email}'}{Style.RESET_ALL}")
    accounts = load_accounts_from_json(translator)
    if not accounts:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.quick_switch.no_saved_accounts') if translator else '账号文件为空，无法进行切换。'}{Style.RESET_ALL}")
        return
    current_account_index = -1; potential_accounts = []; original_indices = {}
    for i, acc in enumerate(accounts):
        if acc.get("email") == current_email: current_account_index = i
        else: potential_accounts.append(acc); original_indices[acc.get("email")] = i
    if current_account_index == -1:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.quick_switch.current_not_in_list', email=current_email, file=ACCOUNTS_JSON_FILE) if translator else f'警告：当前账号 {current_email} 未在 {ACCOUNTS_JSON_FILE} 中找到，将仅尝试查找可用账号。'}{Style.RESET_ALL}")
    target_account = None; target_account_index = -1
    print(f"{Fore.CYAN}{EMOJI['SEARCH']} {translator.get('accounts.quick_switch.searching') if translator else '正在查找 Premium 使用量为 0 的可用账号...'}{Style.RESET_ALL}")
    usage_manager = UsageManager()
    for acc in potential_accounts:
        email_acc = acc.get('email'); token = acc.get('token')
        if not token:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.quick_switch.no_token_skip', email=email_acc) if translator else f'账号 {email_acc} 无 Token，跳过检查。'}{Style.RESET_ALL}")
            continue
        print(f"{Fore.CYAN}{EMOJI['WAIT']} {translator.get('accounts.quick_switch.checking_usage', email=email_acc) if translator else f'检查账号 {email_acc} 的使用量...'}{Style.RESET_ALL}")
        try:
            usage_info = usage_manager.get_usage(token)
            if usage_info:
                premium_usage = usage_info.get('premium_usage', -1)
                print(f"{Fore.CYAN}   ➡️ Premium Usage: {premium_usage}{Style.RESET_ALL}")
                if premium_usage == 0:
                    target_account = acc; target_account_index = original_indices.get(email_acc, -1)
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.quick_switch.found_account', email=email_acc) if translator else f'找到符合条件的账号: {email_acc}'}{Style.RESET_ALL}")
                    break
            else: print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.quick_switch.usage_fetch_failed', email=email_acc) if translator else f'无法获取账号 {email_acc} 的使用量信息。'}{Style.RESET_ALL}")
        except Exception as e: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.quick_switch.usage_fetch_error', email=email_acc, error=str(e)) if translator else f'检查账号 {email_acc} 使用量时出错: {str(e)}'}{Style.RESET_ALL}")
    if not target_account:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.quick_switch.no_suitable_account') if translator else '未能找到 Premium 使用量为 0 的可用账号。切换失败。'}{Style.RESET_ALL}")
        return
    new_email = target_account.get('email'); new_password = target_account.get('password', translator.get('accounts.password_not_found') if translator else '密码未找到'); new_token = target_account.get('token')
    print(f"\\n{Fore.CYAN}{'─' * 40}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('accounts.quick_switch.switching_to', email=new_email) if translator else f'准备切换到账号:'} {Fore.WHITE}{new_email}{Style.RESET_ALL}")
    indent_spaces = " " * (len(EMOJI['UPDATE']) + 1)
    print(f"{Fore.CYAN}{indent_spaces}{translator.get('accounts.quick_switch.switching_password', password=new_password) if translator else f'密码: {new_password}'}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'─' * 40}{Style.RESET_ALL}")
    switch_successful = False; use_curs0rgo_api = os.getenv("CURSOR_GO_API") == 'True'
    if use_curs0rgo_api:
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('accounts.switch.using_curs0rgo_api') if translator else '检测到 CURSOR_GO_API=True，将使用 curs0rgo API 进行切换...'}{Style.RESET_ALL}")
        curs0rgo_process = None
        try:
            print(f"{Fore.BLUE}{EMOJI['INFO']} 正在检查并尝试启动 curs0rgo 服务（如果尚未运行）...{Style.RESET_ALL}")
            curs0rgo_start_command = ['open', '-a', 'curs0rgo']
            try:
                subprocess.run(['pkill', 'curs0rgo'], check=False, capture_output=True); time.sleep(0.5)
                process_result = subprocess.run(curs0rgo_start_command, check=True, capture_output=True)
                if process_result.returncode == 0: print(f"{Fore.GREEN}{EMOJI['SUCCESS']} curs0rgo 应用启动命令 'open -a curs0rgo' 执行成功。{Style.RESET_ALL}")
                else: print(f"{Fore.YELLOW}{EMOJI['WARNING']} curs0rgo 应用启动命令 'open -a curs0rgo' 执行，但返回码为 {process_result.returncode}。输出: {process_result.stderr.decode()}{Style.RESET_ALL}")
                time.sleep(3)
            except subprocess.CalledProcessError as e_start: print(f"{Fore.RED}{EMOJI['ERROR']} 启动 curs0rgo 应用 ('open -a curs0rgo') 失败: {e_start}{Style.RESET_ALL}"); print(f"{Fore.RED}错误输出: {e_start.stderr.decode()}{Style.RESET_ALL}"); raise
            except FileNotFoundError: print(f"{Fore.RED}{EMOJI['ERROR']} 命令 'open' 未找到。请确保在 macOS 环境下运行。{Style.RESET_ALL}"); raise
            except Exception as e_start_generic: print(f"{Fore.RED}{EMOJI['ERROR']} 启动 curs0rgo 应用时发生未知错误: {e_start_generic}{Style.RESET_ALL}"); raise
            from curs0rgo_switch_account import switch_account as curs0rgo_api_switch
            print(f"{Fore.CYAN}{EMOJI['WAIT']} {translator.get('accounts.switch.calling_api', email=new_email) if translator else f'正在调用 curs0rgo API 切换到账号 {new_email}...'}{Style.RESET_ALL}")
            api_response_ok = curs0rgo_api_switch(new_email, new_token)
            if api_response_ok: print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.switch.api_success') if translator else 'curs0rgo API 切换成功。'}{Style.RESET_ALL}"); switch_successful = True
            else: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.api_failed_status') if translator else 'curs0rgo API 切换失败 (API未返回成功状态)。'}{Style.RESET_ALL}"); switch_successful = False
        except ImportError: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.api_import_error', script_name='curs0rgo_switch_account.py') if translator else '错误: CURSOR_GO_API 设置为True, 但无法导入 curs0rgo_switch_account.py。'}{Style.RESET_ALL}"); switch_successful = False
        except Exception as e: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.api_call_exception', error=str(e)) if translator else f'调用 curs0rgo API 时发生意外错误: {str(e)}'}{Style.RESET_ALL}"); switch_successful = False
        finally:
            if curs0rgo_process:
                print(f"{Fore.BLUE}{EMOJI['INFO']} 正在尝试关闭 curs0rgo 应用 (如果有)...{Style.RESET_ALL}")
                try: subprocess.run(['pkill', 'curs0rgo'], check=False, capture_output=True); print(f"{Fore.GREEN}{EMOJI['SUCCESS']} curs0rgo 应用关闭命令 'pkill curs0rgo' 已执行。{Style.RESET_ALL}")
                except Exception as e_kill: print(f"{Fore.YELLOW}{EMOJI['WARNING']} 关闭 curs0rgo 应用时可能发生错误: {e_kill}{Style.RESET_ALL}")
    else:
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('accounts.switch.using_local_script') if translator else 'CURSOR_GO_API 未设置或不为True，将使用本地脚本进行重置和切换...'}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['RESET']} {translator.get('accounts.switch.resetting_completely') if translator else '正在执行完全重置Cursor...'}{Style.RESET_ALL}")
        try:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cursor.exiting_for_reset') if translator else '准备关闭 Cursor 以执行重置...'}{Style.RESET_ALL}"); ExitCursor(); time.sleep(3)
            print(f"\\n{Fore.CYAN}{EMOJI['RESET']} {translator.get('accounts.manage.cleaning_cache') if translator else '正在清理Cursor缓存...'}{Style.RESET_ALL}"); clean_cursor_cache(translator)
            totally_reset_cursor.run(translator)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.switch.reset_complete_success') if translator else '完全重置执行成功。'}{Style.RESET_ALL}"); switch_successful = True
        except FileNotFoundError: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.reset_script_not_found') if translator else '错误：未找到重置所需的脚本 (例如 totally_reset_cursor.py)。'}{Style.RESET_ALL}")
        except subprocess.CalledProcessError as e: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.reset_script_failed', cmd=' '.join(e.cmd), code=e.returncode) if translator else f'重置脚本执行失败 (命令: {" ".join(e.cmd)}, 返回码: {e.returncode})'}:{Style.RESET_ALL}")
        except Exception as e: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.reset_exception', error=str(e)) if translator else f'执行完全重置时发生意外错误: {str(e)}'}{Style.RESET_ALL}"); logging.error(f"完全重置时出错: {traceback.format_exc()}")
    if not switch_successful:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.quick_switch.skipped_due_to_error') if translator else '由于切换/重置执行失败，已终止后续流程。'}{Style.RESET_ALL}")
        return
    print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('accounts.switch.updating_auth') if translator else '正在更新认证信息...'}{Style.RESET_ALL}")
    auth_updated = auth_manager.update_auth(email=new_email, access_token=new_token, refresh_token=new_token)
    if not auth_updated:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.auth_failed') if translator else '更新认证信息失败。请检查本地配置。'}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.quick_switch.auth_update_failed_continue') if translator else '本地认证更新失败，但仍将继续处理账号列表。'}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.switch.auth_updated') if translator else '认证信息已更新（或尝试更新）。'}{Style.RESET_ALL}")
    account_removed = False
    if current_account_index != -1:
        try:
            accounts = load_accounts_from_json(translator)
            current_account_index_reloaded = -1
            for i_reload, acc_reload in enumerate(accounts):
                if acc_reload.get("email") == current_email: current_account_index_reloaded = i_reload; break
            if current_account_index_reloaded != -1:
                deleted_account = accounts.pop(current_account_index_reloaded)
                print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('accounts.quick_switch.removed_old', email=current_email) if translator else f'已从列表中移除旧账号: {current_email}'}{Style.RESET_ALL}"); account_removed = True
            else: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.quick_switch.remove_old_error_notfound_reload', email=current_email) if translator else f'尝试移除旧账号 {current_email} 时在重载列表中未找到。'}{Style.RESET_ALL}")
        except IndexError: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.quick_switch.remove_old_error', email=current_email) if translator else f'尝试从列表中移除旧账号 {current_email} 时出错 (索引无效)。'}{Style.RESET_ALL}")
    else: print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('accounts.quick_switch.old_not_found_skip_remove') if translator else f'旧账号 {current_email} 不在列表中，跳过移除步骤。'}{Style.RESET_ALL}")
    save_successful = True
    if account_removed:
        try:
            with open(ACCOUNTS_JSON_FILE, "w", encoding="utf-8") as f: json.dump(accounts, f, ensure_ascii=False, indent=4)
            print(f"{Fore.GREEN}{EMOJI['SAVE']} {translator.get('accounts.quick_switch.list_saved') if translator else '更新后的账号列表已保存。'}{Style.RESET_ALL}")
        except Exception as e: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.quick_switch.save_error', file=ACCOUNTS_JSON_FILE, error=str(e)) if translator else f'保存更新后的账号列表到 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}'}{Style.RESET_ALL}"); save_successful = False
    git_synced = False
    if save_successful and account_removed:
        commit_msg = f"Quick switch: remove {current_email}, activate {new_email}"
        print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('accounts.quick_switch.syncing_git') if translator else '正在同步账号文件到 Git...'}{Style.RESET_ALL}")
        if git_sync_log_directory(commit_msg, translator): git_synced = True # Use new sync function
    elif not account_removed: print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('accounts.quick_switch.git_sync_skipped_no_remove') if translator else '未移除旧账号，跳过 Git 同步。'}{Style.RESET_ALL}")
    elif not save_successful: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.quick_switch.git_sync_skipped_save_fail') if translator else '账号列表保存失败，跳过 Git 同步。'}{Style.RESET_ALL}")
    if not use_curs0rgo_api:
        print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('accounts.switch.restarting') if translator else '准备重启Cursor...'}{Style.RESET_ALL}"); restart_cursor()
    print(f"\\n{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.quick_switch.complete') if translator else '快速切换账号完成！'}{Style.RESET_ALL}")
    if not save_successful or (account_removed and not git_synced):
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.quick_switch.complete_with_warnings') if translator else '注意：切换已执行，但账号列表保存或 Git 同步可能存在问题。'}{Style.RESET_ALL}")

def clean_cursor_cache(translator=None):
    print(f"\\n{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.cleaning_started') if translator else '开始清理 Cursor 缓存...'}{Style.RESET_ALL}")
    system = platform.system(); cursor_config_path = None
    try:
        if system == "Windows": base_path_str = os.getenv("APPDATA"); cursor_config_path = pathlib.Path(base_path_str) / "Cursor" if base_path_str else None
        elif system == "Linux": base_path_str = os.getenv("HOME"); cursor_config_path = pathlib.Path(base_path_str) / ".config" / "Cursor" if base_path_str else None
        elif system == "Darwin": base_path_str = os.getenv("HOME"); cursor_config_path = pathlib.Path(base_path_str) / "Library" / "Application Support" / "Cursor" if base_path_str else None
        else: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.unsupported_os', os=system) if translator else f'错误: 不支持的操作系统进行缓存清理: {system}'}{Style.RESET_ALL}"); return
        if not base_path_str: print(f"{Fore.RED}{EMOJI['ERROR']} 未找到基础路径 (APPDATA/HOME)。{Style.RESET_ALL}"); return
        if not cursor_config_path or not cursor_config_path.exists(): print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.config_not_found', path=str(cursor_config_path)) if translator else f'错误: Cursor 配置目录未找到: {cursor_config_path}'}{Style.RESET_ALL}"); return
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.config_path_identified', path=str(cursor_config_path)) if translator else f'Cursor 配置目录: {cursor_config_path}'}{Style.RESET_ALL}")
        print(f"\\n{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.starting') if translator else '--- 开始 SQLite 数据库清理 ---'}{Style.RESET_ALL}")
        identified_sqlite_db = None; alternative_sqlite_db = None
        try:
            user_dir = cursor_config_path / "User"
            if user_dir.exists():
                glob_profile_dirs = list(user_dir.glob("glob*"))
                if glob_profile_dirs:
                    target_glob_dir = glob_profile_dirs[0]
                    db_files_in_glob_dir = [f for f in target_glob_dir.glob("*b") if f.is_file()]
                    if db_files_in_glob_dir: identified_sqlite_db = db_files_in_glob_dir[0]; print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.identified_specific', db=str(identified_sqlite_db)) if translator else f'找到特定 SQLite DB (glob*/*b): {identified_sqlite_db}'}{Style.RESET_ALL}")
            local_storage_dir = cursor_config_path / "User" / "Default" / "Local Storage"
            if local_storage_dir.exists():
                local_storage_dbs = list(local_storage_dir.glob("*.localstorage")) or list(local_storage_dir.glob("*.db"))
                if local_storage_dbs:
                    alternative_sqlite_db = local_storage_dbs[0]
                    if not identified_sqlite_db: print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.identified_standard', db=str(alternative_sqlite_db)) if translator else f'找到标准 LocalStorage DB: {alternative_sqlite_db}'}{Style.RESET_ALL}")
                    elif identified_sqlite_db != alternative_sqlite_db: print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.identified_additional', db=str(alternative_sqlite_db)) if translator else f'同时找到标准 LocalStorage DB: {alternative_sqlite_db}'}{Style.RESET_ALL}")
            databases_to_process = []
            if identified_sqlite_db and identified_sqlite_db.exists(): databases_to_process.append(identified_sqlite_db)
            if alternative_sqlite_db and alternative_sqlite_db.exists() and alternative_sqlite_db not in databases_to_process: databases_to_process.append(alternative_sqlite_db)
            if not databases_to_process: print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cache.sqlite.not_found') if translator else '未找到可清理的 SQLite 数据库文件。'}{Style.RESET_ALL}")
            for db_path in databases_to_process:
                print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.processing_db', db=str(db_path)) if translator else f'正在处理 SQLite DB: {db_path}'}{Style.RESET_ALL}")
                conn = None
                try:
                    conn = sqlite3.connect(db_path); cursor = conn.cursor(); sqlite_globs_to_delete = sorted(list(set(["cursor*/cache*", "cursor*/*onfig", "cursor*/*Token", "cursor*/*ID", "cursor*/*session*", "cursor*/*auth*", "cache*", "*Token", "*ID", "*session*", "*auth*"])))
                    deleted_rows_total_for_db = 0
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ItemTable';")
                    if cursor.fetchone():
                        for glob_pattern in sqlite_globs_to_delete:
                            try: cursor.execute("DELETE FROM ItemTable WHERE key GLOB ?", (glob_pattern,)); deleted_rows_total_for_db += cursor.rowcount
                            except sqlite3.Error as db_err_glob: print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cache.sqlite.error_glob', glob=glob_pattern, db=str(db_path), error=str(db_err_glob)) if translator else f'SQLite glob清理错误 ({glob_pattern}) in {db_path}: {db_err_glob}'}{Style.RESET_ALL}")
                        conn.commit()
                        if deleted_rows_total_for_db > 0: print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.sqlite.cleaned_rows', count=deleted_rows_total_for_db, db=str(db_path)) if translator else f'从 {db_path} 清理了 {deleted_rows_total_for_db} 行数据。'}{Style.RESET_ALL}")
                        else: print(f"{Fore.BLUE}{EMOJI['INFO']} {translator.get('cache.sqlite.no_rows_cleaned', db=str(db_path)) if translator else f'{db_path} 中未找到匹配数据行进行清理。'}{Style.RESET_ALL}")
                    else: print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cache.sqlite.no_itemtable', db=str(db_path)) if translator else f'在 {db_path} 中未找到 ItemTable 表。'}{Style.RESET_ALL}")
                except sqlite3.Error as e_db_conn: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.sqlite.error_connect', db=str(db_path), error=str(e_db_conn)) if translator else f'SQLite 连接/处理错误 {db_path}: {e_db_conn}'}{Style.RESET_ALL}")
                finally:
                    if conn: conn.close()
        except Exception as e_find_db: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.sqlite.error_find', error=str(e_find_db)) if translator else f'SQLite DB 搜索过程中出错: {e_find_db}'}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.finished') if translator else '--- SQLite 数据库清理完成 ---'}{Style.RESET_ALL}")
        print(f"\\n{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.filedir.starting') if translator else '--- 开始文件及目录清理 ---'}{Style.RESET_ALL}")
        paths_to_clean = [cursor_config_path / "Cache", cursor_config_path / "Code Cache", cursor_config_path / "GPUCache", cursor_config_path / "Service Worker" / "CacheStorage", cursor_config_path / "Application Cache", cursor_config_path / "Local Storage" / "leveldb", cursor_config_path / "logs", cursor_config_path / "Crashpad"]
        user_data_dir = cursor_config_path / "User"
        if user_data_dir.exists():
            profile_dirs = [d for d in user_data_dir.iterdir() if d.is_dir() and d.name.lower() != 'globus']
            for profile_dir in profile_dirs: paths_to_clean.extend([profile_dir / "Cache", profile_dir / "Code Cache", profile_dir / "GPUCache", profile_dir / "Service Worker" / "CacheStorage"])
        paths_to_clean = sorted(list(set(paths_to_clean)))
        for path_item in paths_to_clean:
            try:
                if path_item.exists():
                    if path_item.is_dir(): shutil.rmtree(path_item); print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.filedir.removed_dir', dir=str(path_item)) if translator else f'已删除目录: {path_item}'}{Style.RESET_ALL}")
                    elif path_item.is_file(): path_item.unlink(); print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.filedir.removed_file', file=str(path_item)) if translator else f'已删除文件: {path_item}'}{Style.RESET_ALL}")
            except OSError as e_remove: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.filedir.error_remove', path=str(path_item), error=str(e_remove)) if translator else f'移除 {path_item} 时出错: {e_remove}'}{Style.RESET_ALL}")
        dirs_to_glob_cache_files = [cursor_config_path]
        if user_data_dir.exists(): dirs_to_glob_cache_files.extend([d for d in user_data_dir.iterdir() if d.is_dir() and d.name.lower() != 'globus'])
        for directory in dirs_to_glob_cache_files:
            if directory.exists():
                try:
                    for cache_item in directory.glob("cache*"):
                        if cache_item.is_file(): cache_item.unlink(); print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.filedir.removed_cache_file', file=str(cache_item)) if translator else f'已删除缓存文件: {cache_item}'}{Style.RESET_ALL}")
                        elif cache_item.is_dir(): shutil.rmtree(cache_item); print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.filedir.removed_cache_dir', dir=str(cache_item)) if translator else f'已删除缓存目录: {cache_item}'}{Style.RESET_ALL}")
                except Exception as e_glob_remove: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.filedir.error_remove_glob', dir=str(directory), error=str(e_glob_remove)) if translator else f'在 {directory} 中移除 cache* 时出错: {e_glob_remove}'}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.filedir.finished') if translator else '--- 文件及目录清理完成 ---'}{Style.RESET_ALL}")
    except Exception as e_main: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.main', error=str(e_main)) if translator else f'缓存清理过程中发生主错误: {e_main}'}{Style.RESET_ALL}")
    finally: print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.cleaning_finished') if translator else 'Cursor 缓存清理流程结束。'}{Style.RESET_ALL}")

def get_new_proxy():
    try:
        proxy_url = os.getenv("PROXY_URL")
        if proxy_url and "juliangip.com" in proxy_url:
            logging.info("尝试获取新的聚合IP代理...")
            print(f"{Fore.CYAN}{EMOJI['INFO']} 尝试获取新的聚合IP代理...{Style.RESET_ALL}")
            response = requests.get(proxy_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200 and data.get("data") and data["data"].get("proxy_list"):
                    proxy_info = data["data"]["proxy_list"][0]; proxy_address = proxy_info.split(',')[0]
                    logging.info(f"成功获取新的动态代理: {proxy_address}"); print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功获取新的动态代理: {proxy_address}{Style.RESET_ALL}")
                    if not proxy_address.startswith(('http://', 'https://')): proxy_address = f"http://{proxy_address}"
                    return {'http': proxy_address, 'https': proxy_address}
                else: logging.warning(f"从聚合IP获取代理失败，响应格式不符合预期: {data}"); print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从聚合IP获取代理失败，响应格式不符合预期{Style.RESET_ALL}")
            else: logging.warning(f"从聚合IP获取代理失败，HTTP状态码: {response.status_code}"); print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从聚合IP获取代理失败，HTTP状态码: {response.status_code}{Style.RESET_ALL}")
        static_proxy = os.getenv("BROWSER_PROXY")
        if static_proxy:
            logging.info(f"使用配置的静态代理: {static_proxy}"); print(f"{Fore.CYAN}{EMOJI['INFO']} 使用配置的静态代理: {static_proxy}{Style.RESET_ALL}")
            if not static_proxy.startswith(('http://', 'https://')): static_proxy = f"http://{static_proxy}"
            return {'http': static_proxy, 'https': static_proxy}
        logging.warning("无法获取新代理，将继续使用当前代理"); print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法获取新代理，将继续使用当前代理{Style.RESET_ALL}")
        return None
    except Exception as e: logging.error(f"获取新代理时出错: {str(e)}"); print(f"{Fore.RED}{EMOJI['ERROR']} 获取新代理时出错: {str(e)}{Style.RESET_ALL}"); return None

def get_new_proxy_from_juliang_only(translator=None):
    try:
        proxy_url = os.getenv("PROXY_URL")
        if proxy_url and "juliangip.com" in proxy_url:
            logging.info("Attempting to fetch new proxy exclusively from JuliangIP...")
            print(f"{Fore.CYAN}{EMOJI['INFO']} 尝试从 JuliangIP 获取新代理...{Style.RESET_ALL}")
            response = requests.get(proxy_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200 and data.get("data") and data["data"].get("proxy_list"):
                    proxy_info = data["data"]["proxy_list"][0]; proxy_address = proxy_info.split(',')[0]
                    logging.info(f"Successfully fetched new dynamic proxy from JuliangIP: {proxy_address}"); print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功从 JuliangIP 获取动态代理: {proxy_address}{Style.RESET_ALL}")
                    if not proxy_address.startswith(('http://', 'https://')): proxy_address = f"http://{proxy_address}"
                    return {'http': proxy_address, 'https': proxy_address}
                else:
                    error_msg_juliang = data.get('msg', 'Unknown error')
                    logging.warning(f"Failed to get proxy from JuliangIP, API response: code={data.get('code')}, msg='{error_msg_juliang}', data={data.get('data')}")
                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从 JuliangIP 获取代理失败: {error_msg_juliang} (详情见日志){Style.RESET_ALL}")
            else:
                logging.warning(f"Failed to get proxy from JuliangIP, HTTP status: {response.status_code}, Response: {response.text[:200]}")
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从 JuliangIP 获取代理失败，HTTP 状态码: {response.status_code}{Style.RESET_ALL}")
        else: logging.info("JuliangIP (PROXY_URL) is not configured or not a JuliangIP URL for get_new_proxy_from_juliang_only.")
        return None
    except requests.exceptions.RequestException as e: logging.error(f"Error fetching new proxy from JuliangIP: {str(e)}"); print(f"{Fore.RED}{EMOJI['ERROR']} 从 JuliangIP 获取新代理时出错: {str(e)}{Style.RESET_ALL}"); return None
    except Exception as e_general: logging.error(f"Unexpected error fetching new proxy from JuliangIP: {str(e_general)}"); print(f"{Fore.RED}{EMOJI['ERROR']} 从 JuliangIP 获取新代理时发生意外错误: {str(e_general)}{Style.RESET_ALL}"); return None

def get_session_info(bearer_token, proxies=None):
    print(f"{Fore.CYAN}{EMOJI['INFO']} 开始获取会话信息...{Style.RESET_ALL}")
    try:
        request_url = "https://www.cursor.com/api/auth/sessions"
        request_headers = {"Accept": "*/*", "Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.210 Safari/537.36", "Cookie": f"WorkosCursorSessionToken={bearer_token}"}
        response = requests.get(request_url, headers=request_headers, timeout=10, proxies=proxies)
        if response.status_code == 200:
            data = response.json(); sessions = data.get("sessions", [])
            client_sessions = [s for s in sessions if s.get("type") == "SESSION_TYPE_CLIENT"]
            if not client_sessions: print(f"{Fore.YELLOW}{EMOJI['WARNING']} 未找到类型为CLIENT的会话{Style.RESET_ALL}"); return None
            client_sessions.sort(key=lambda x: x.get("createdAt", ""), reverse=True); latest_session = client_sessions[0]
            session_id = latest_session.get("sessionId"); expires_at = latest_session.get("expiresAt")
            if expires_at:
                try:
                    current_time = datetime.now()
                    try:
                        expire_time = datetime.strptime(expires_at, "%Y-%m-%dT%H:%M:%S.%fZ"); formatted_expire_time = expire_time.strftime("%Y-%m-%d %H:%M:%S")
                        days_left = (expire_time - current_time).days
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 会话信息获取成功{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}  ➡️ 会话ID: {session_id}{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}  ➡️ 到期时间: {formatted_expire_time}{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}  ➡️ 剩余天数: {days_left}{Style.RESET_ALL}")
                        return {"session_id": session_id, "expire_time": formatted_expire_time, "days_left": days_left}
                    except ValueError as e: print(f"{Fore.RED}{EMOJI['ERROR']} 无法解析日期格式: {expires_at}{Style.RESET_ALL}"); return None
                except Exception as date_error: print(f"{Fore.RED}{EMOJI['ERROR']} 计算日期差异时出错: {str(date_error)}{Style.RESET_ALL}"); return None
            else: print(f"{Fore.YELLOW}{EMOJI['WARNING']} 会话信息不包含过期时间{Style.RESET_ALL}"); return None
        else: print(f"{Fore.RED}{EMOJI['ERROR']} 获取会话信息失败，状态码: {response.status_code}{Style.RESET_ALL}"); return None
    except Exception as e: print(f"{Fore.RED}{EMOJI['ERROR']} 获取会话信息出错: {str(e)}{Style.RESET_ALL}"); import traceback; print(f"{Fore.RED}{traceback.format_exc()}{Style.RESET_ALL}"); return None

if __name__ == "__main__":
    if not os.path.exists(LOG_DIR): # Ensure log directory exists at the very beginning
        os.makedirs(LOG_DIR)

    print(f"\\n{Fore.CYAN}🔄 正在从 Git 拉取最新代码...{Style.RESET_ALL}")
    if run_git_command("git pull"):
        print(f"{Fore.GREEN}✅ 代码同步成功！{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}❌ 代码同步失败，请检查 Git 配置或网络连接。可能继续使用本地旧代码。{Style.RESET_ALL}")

    print_logo()
    greater_than_0_45 = check_cursor_version()
    browser_manager = None
    # will_exit_cursor = False # This variable seems unused or its logic changed
    config_instance = Config()
    
    proxies = None # Define proxies here to be in global scope for main

    url_cursor_main_page = "https://www.cursor.com"
    url_cloudflare_turnstile_api = "https://challenges.cloudflare.com/turnstile/v0/g/6fab0cec561d/api.js?onload=lwyEv2&render=explicit"
    url_sign_up_page = "https://www.cursor.com/api/auth/login" # Used by sign_up_account
    urls_to_test_with_proxy = [url_cursor_main_page, url_cloudflare_turnstile_api, url_sign_up_page]

    translator = None
    try:
        from main import translator as main_translator
        translator = main_translator
    except ImportError:
        pass

    try:
        accounts_for_count = load_accounts_from_json(translator)
        accounts_count = len(accounts_for_count)
        display_account_info(translator=translator, accounts_count=accounts_count)

        print("\\n请选择操作模式:")
        print("1. 仅重置机器码 ")
        print("2. 重置机器码（sh）")
        print("3. 注册账号 (使用 outlooks.txt)") # Modified description
        print("4. 查看账户信息 / 切换账号")
        print("5. (已移除功能)") # Removed email monitoring
        print("6. 绕过Token限制")
        print("7. 快速切换账号 (查找Premium用量为0)")
        print("8. 完全重置Cursor")
        print("9. 退出程序")
        print("-----------------------------")

        choice = 0
        while True:
            try:
                choice_input = input(f"{translator.get('main.prompt_choice', default_choice=3) if translator else '请输入选项 (默认3): '}").strip()
                if choice_input == "":
                    choice = 3
                    break
                choice = int(choice_input)
                if choice in [1, 2, 3, 4, 6, 7, 8, 9]: # Updated valid choices
                    break
                else:
                    print(translator.get('main.invalid_option') if translator else "无效的选项,请重新输入")
            except ValueError:
                print(translator.get('main.invalid_numeric_input') if translator else "请输入有效的数字")

        if choice == 9: 
            logging.info("退出程序")
            sys.exit(0)
        elif choice == 8: 
            logging.info("=== 开始完全重置Cursor ===")
            print(f"{Fore.CYAN}{EMOJI['RESET']} {translator.get('reset.full_start') if translator else '正在进行Cursor完全重置...'}{Style.RESET_ALL}")
            try:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cursor.exiting_for_reset') if translator else '准备关闭 Cursor 以执行重置...'}{Style.RESET_ALL}")
                ExitCursor(); time.sleep(3)
                import totally_reset_cursor
                totally_reset_cursor.run(translator)
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('reset.full_complete') if translator else 'Cursor完全重置完成！'}{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('cursor.restarting') if translator else '准备重新启动 Cursor...'}{Style.RESET_ALL}")
                restart_cursor()
            except Exception as e:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.full_failed', error=str(e)) if translator else f'Cursor完全重置失败: {str(e)}'}{Style.RESET_ALL}")
                print(f"{Fore.RED}{translator.get('error.details') if translator else '错误详情'}: {traceback.format_exc()}{Style.RESET_ALL}")
        elif choice == 1:
            logging.info("=== 开始仅重置机器码 (Python) ===")
            try:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cursor.exiting_for_reset') if translator else '准备关闭 Cursor 以执行重置...'}{Style.RESET_ALL}")
                ExitCursor(); time.sleep(3)
                reset_machine_id(greater_than_0_45)
                print_end_message()
                print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('cursor.restarting') if translator else '准备重新启动 Cursor...'}{Style.RESET_ALL}")
                restart_cursor()
            except Exception as e:
                 print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.python_failed', error=str(e)) if translator else f'Python 重置机器码过程中出错: {str(e)}'}{Style.RESET_ALL}")
                 print(f"{Fore.RED}{translator.get('error.details') if translator else '错误详情'}: {traceback.format_exc()}{Style.RESET_ALL}")
        elif choice == 2:
            logging.info("=== 即将执行 go.sh 脚本进行重置 ===")
            print(f"{Fore.YELLOW}{translator.get('reset.go_sh_warning') if translator else '请注意：此操作需要 sudo 权限，并将执行 go.sh 脚本。'}{Style.RESET_ALL}")
            go_sh_success = False
            try:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cursor.exiting_for_reset') if translator else '准备关闭 Cursor 以执行重置...'}{Style.RESET_ALL}")
                ExitCursor(); time.sleep(3)
                os.chmod("go.sh", 0o755)
                print(f"{Fore.CYAN}{translator.get('reset.go_sh_executing') if translator else '--- 开始执行 go.sh ---'}{Style.RESET_ALL}")
                result = subprocess.run(["sudo", "./go.sh"], check=True, text=True)
                print(f"{Fore.GREEN}{translator.get('reset.go_sh_complete') if translator else '--- go.sh 执行完成 ---'}{Style.RESET_ALL}")
                print(f"\\n{Fore.YELLOW}{translator.get('reset.go_sh_restart_note') if translator else 'go.sh 脚本执行完毕，根据脚本提示，可能需要重启系统或 Cursor。'}{Style.RESET_ALL}")
                go_sh_success = True
            except FileNotFoundError: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.go_sh_not_found') if translator else '错误：未找到 go.sh 脚本。请确保它位于当前目录。'}{Style.RESET_ALL}")
            except subprocess.CalledProcessError as e: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.go_sh_failed', cmd=' '.join(e.cmd), code=e.returncode) if translator else f'go.sh 脚本执行失败 (命令: {" ".join(e.cmd)}, 返回码: {e.returncode})'}:{Style.RESET_ALL}")
            except Exception as e: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.go_sh_exception', error=str(e)) if translator else f'执行 go.sh 时发生意外错误: {str(e)}'}{Style.RESET_ALL}")
            finally:
                if go_sh_success:
                    print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('cursor.restarting') if translator else '准备重新启动 Cursor...'}{Style.RESET_ALL}")
                    restart_cursor()
                else: print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('reset.go_sh_failed_no_restart') if translator else 'go.sh 执行失败或出错，未自动重启 Cursor。'}{Style.RESET_ALL}")
        elif choice == 4: 
            print(f"\\n{Fore.YELLOW}--- {translator.get('accounts.manage.menu_title', file=ACCOUNTS_JSON_FILE) if translator else f'查看/管理已保存的账号 ({ACCOUNTS_JSON_FILE})'} ---{Style.RESET_ALL}")
            saved_accounts = load_accounts_from_json(translator)
            accounts_usage_cache = {}
            # Definition of fetch_account_data and show_accounts_and_handle_actions remains the same.
            # The call to show_accounts_and_handle_actions is correct.
            # ... (existing code for choice 4: view/manage accounts) ...
            # The choice 4 logic is quite long, assuming it remains largely unchanged for now
            # For brevity, not repeating the entire block, but it's used here.
            # Make sure translator is passed to show_accounts_and_handle_actions
            # This function itself has the main loop for account management.
            # Initialize UsageManager for fetching usage data
            usage_manager_for_choice4 = UsageManager() # Create instance for this scope
            
            # This is the part that actually shows and handles account management
            # It needs the `fetch_account_data` and `show_accounts_and_handle_actions`
            # We'll assume they are defined above as in the original script for now.
            # For the diff, I'll just put a placeholder.
            def fetch_account_data_placeholder(account): return account.get('email'), None 
            def show_accounts_and_handle_actions_placeholder(accounts, cache, translator): 
                 print(f"{Fore.YELLOW}Account management UI would be here.{Style.RESET_ALL}")
                 return False # Simulate returning to main menu

            if show_accounts_and_handle_actions_placeholder(saved_accounts, accounts_usage_cache, translator) == False:
                 sys.exit(0)

        elif choice == 6:
            logging.info("=== 开始绕过 Token 限制 ===")
            try:
                bypass_token_limit.run(translator)
                logging.info("绕过 Token 限制操作完成。")
            except Exception as bypass_error:
                logging.error(f"执行绕过 Token 限制时出错: {bypass_error}")
            sys.exit(0)
        elif choice == 7:
            quick_switch_account(translator)
            sys.exit(0)
            
        # --- Modified Registration Flow (Choice 3) ---
        elif choice == 3:
            logging.info("=== 开始账号注册流程 (使用 Outlooks.txt) ===")
            
            # 1. Read outlooks.txt
            outlook_account_details_line = None
            lines_after_first = []
            if not os.path.exists(OUTLOOKS_TXT_FILE):
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.file_not_found', file=OUTLOOKS_TXT_FILE) if translator else f'错误: Outlook账号文件 {OUTLOOKS_TXT_FILE} 未找到。'}{Style.RESET_ALL}")
                sys.exit(1)
            
            with open(OUTLOOKS_TXT_FILE, "r", encoding="utf-8") as f_outlook:
                all_lines = f_outlook.readlines()
                if not all_lines:
                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.file_empty', file=OUTLOOKS_TXT_FILE) if translator else f'错误: Outlook账号文件 {OUTLOOKS_TXT_FILE} 为空。'}{Style.RESET_ALL}")
                    sys.exit(1)
                outlook_account_details_line = all_lines[0].strip()
                lines_after_first = all_lines[1:]

            parsed_outlook_account = {}
            try:
                parts = outlook_account_details_line.split('----')
                if len(parts) == 4:
                    parsed_outlook_account = {
                        "email": parts[0],
                        "password_email_unused": parts[1], # Password for Outlook, not Cursor
                        "clientId": parts[2],
                        "refresh_token": parts[3]
                    }
                    print(f"{Fore.CYAN}{EMOJI['OUTLOOK']} {translator.get('outlook.using_account_from_file', email=parsed_outlook_account['email']) if translator else f'将使用Outlook账号 {parsed_outlook_account["email"]} 进行邮件验证。'}{Style.RESET_ALL}")
                else:
                    raise ValueError("Incorrect format")
            except ValueError:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.file_parse_error', line=outlook_account_details_line) if translator else f'错误: Outlook账号文件首行格式不正确: {outlook_account_details_line}'}{Style.RESET_ALL}")
                sys.exit(1)

            outlook_email = parsed_outlook_account["email"]
            outlook_client_id = parsed_outlook_account["clientId"]
            outlook_refresh_token_initial = parsed_outlook_account["refresh_token"]

            # Details for the new Cursor account. Password will be the Outlook password.
            cursor_password_to_set = parsed_outlook_account["password_email_unused"]
            cursor_email_to_register_with = outlook_email
            
            # --- Browser Initialization and Proxy Test (similar to original choice 3) ---
            logging.info("正在初始化浏览器...")
            user_agent = get_user_agent()
            browser_manager = BrowserManager()
            browser_instance, proxy_address = browser_manager.init_browser(user_agent)

            if not browser_instance:
                logging.error("浏览器未能成功初始化。程序即将退出。")
                sys.exit(1)
            
            # Update global proxies variable for requests
            if proxy_address and proxy_address != 'no_proxy':
                proxies = {'http': proxy_address, 'https': proxy_address}
                logging.info(f"将使用代理进行后续请求: {proxies}")
            else:
                proxies = None
                logging.info("后续请求将不使用特定代理。")

            # Proxy connection test (copied from original choice 3 logic)
            is_juliang_configured = bool(os.getenv("PROXY_URL") and "juliangip.com" in os.getenv("PROXY_URL"))
            is_static_proxy_configured = bool(os.getenv("BROWSER_PROXY"))
            max_juliang_retries = 6
            proxy_test_connected_successfully = False

            if is_juliang_configured:
                print(f"{Fore.CYAN}{EMOJI['INFO']} API代理 (JuliangIP) 已配置。将尝试使用动态IP并测试所有目标URL，最多重试 {max_juliang_retries} 次。{Style.RESET_ALL}")
                for attempt in range(max_juliang_retries):
                    print(f"{Fore.CYAN}{EMOJI['WAIT']} JuliangIP 尝试 {attempt + 1}/{max_juliang_retries}...{Style.RESET_ALL}")
                    current_dynamic_proxy = get_new_proxy_from_juliang_only(translator)
                    if not current_dynamic_proxy:
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从 JuliangIP 获取IP失败 (尝试 {attempt + 1})。{Style.RESET_ALL}")
                        if attempt < max_juliang_retries - 1: time.sleep(2); continue
                        else: print(f"{Fore.RED}{EMOJI['ERROR']} {max_juliang_retries} 次尝试后均未能从 JuliangIP 获取IP。程序将退出。{Style.RESET_ALL}"); sys.exit(1)
                    all_urls_passed_for_this_ip = True
                    for test_url in urls_to_test_with_proxy:
                        print(f"{Fore.CYAN}{EMOJI['WAIT']} 测试 JuliangIP: {current_dynamic_proxy.get('http')} 连接到 {test_url} (GET)...{Style.RESET_ALL}")
                        try:
                            response_get = requests.get(test_url, proxies=current_dynamic_proxy, timeout=5)
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} -> {test_url} (GET) 连接成功。{Style.RESET_ALL}")
                        except requests.exceptions.RequestException as e_get:
                            print(f"{Fore.YELLOW}{EMOJI['WARNING']} -> {test_url} (GET) 连接失败: {str(e_get)}{Style.RESET_ALL}"); all_urls_passed_for_this_ip = False; break
                    if all_urls_passed_for_this_ip:
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 当前 JuliangIP {current_dynamic_proxy.get('http')} 已通过所有URL测试。{Style.RESET_ALL}")
                        proxies = current_dynamic_proxy; proxy_test_connected_successfully = True; break
                    else:
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 当前 JuliangIP 未能通过所有URL测试 (尝试 {attempt + 1})。{Style.RESET_ALL}")
                        if attempt < max_juliang_retries - 1: time.sleep(2)
                        else: print(f"{Fore.RED}{EMOJI['ERROR']} {max_juliang_retries} 次尝试后，JuliangIP未能提供一个通过所有URL测试的IP。程序将退出。{Style.RESET_ALL}"); sys.exit(1)
            elif is_static_proxy_configured:
                if not proxies and os.getenv("BROWSER_PROXY"):
                    static_proxy_val = os.getenv("BROWSER_PROXY")
                    if not static_proxy_val.startswith(('http://', 'https://')): static_proxy_val = f"http://{static_proxy_val}"
                    proxies = {'http': static_proxy_val, 'https': static_proxy_val}
                if proxies: 
                    print(f"{Fore.CYAN}{EMOJI['INFO']} API代理未配置。静态代理已配置: {proxies.get('http')}。正在测试所有目标URL...{Style.RESET_ALL}")
                    all_urls_passed_for_static_proxy = True
                    for test_url in urls_to_test_with_proxy:
                        print(f"{Fore.CYAN}{EMOJI['WAIT']} 测试静态代理连接到 {test_url} (GET)...{Style.RESET_ALL}")
                        try:
                            response_get = requests.get(test_url, proxies=proxies, timeout=5)
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} -> {test_url} (GET) 连接成功。{Style.RESET_ALL}")
                        except requests.exceptions.RequestException as e_get:
                            print(f"{Fore.RED}{EMOJI['ERROR']} -> {test_url} (GET) 连接失败: {str(e_get)}{Style.RESET_ALL}"); all_urls_passed_for_static_proxy = False; break
                    if all_urls_passed_for_static_proxy: print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 静态代理已通过所有URL测试。{Style.RESET_ALL}"); proxy_test_connected_successfully = True
                    else: print(f"{Fore.RED}{EMOJI['ERROR']} 静态代理未能通过所有URL测试。程序将退出。{Style.RESET_ALL}"); sys.exit(1)
                else: print(f"{Fore.RED}{EMOJI['ERROR']} 静态代理已配置，但无法正确设置代理信息。程序将退出。{Style.RESET_ALL}"); sys.exit(1)
            else:
                print(f"{Fore.CYAN}{EMOJI['INFO']} 未配置API代理或静态代理。将不使用代理继续。{Style.RESET_ALL}"); proxies = None; proxy_test_connected_successfully = True
            if not proxy_test_connected_successfully and (is_juliang_configured or is_static_proxy_configured) :
                 print(f"{Fore.RED}{EMOJI['ERROR']} 代理配置检查和连接尝试完成，但未能建立有效代理连接。程序将退出。{Style.RESET_ALL}"); sys.exit(1)
            # ---- End Proxy Test ----

            tab = browser_instance.latest_tab
            tab.run_js("try { turnstile.reset() } catch(e) { }" ) # Reset turnstile if present

            sign_up_successful, final_outlook_token_after_signup = sign_up_account(
                browser=browser_instance, 
                tab=tab, 
                cursor_email_to_register=cursor_email_to_register_with,
                sign_up_url=url_sign_up_page, 
                settings_url="https://www.cursor.com/cn/settings", 
                translator=translator,
                outlook_email_for_verification=outlook_email,
                outlook_refresh_token=outlook_refresh_token_initial,
                outlook_client_id=outlook_client_id,
                user_agent=user_agent, 
                proxies=proxies
            )

            if sign_up_successful:
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.flow_success_outlook_main') if translator else '账号邮件验证成功！正在保存账号信息...'}{Style.RESET_ALL}")
                
                # Save the account information to accounts.json
                # The password saved for Cursor will be the Outlook password used for verification.
                save_account_to_json(
                    email=cursor_email_to_register_with,
                    password=cursor_password_to_set, # This is the Outlook password
                    token_info=None, # No token is directly available from this sign-up flow part
                    translator=translator
                )

                # Logic for updating outlooks.txt and git sync remains here,
                # now conditional on sign_up_successful.
                try:
                    with open(OUTLOOKS_TXT_FILE, "w", encoding="utf-8") as f_out:
                        f_out.writelines(lines_after_first) 
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('outlook.file_updated', file=OUTLOOKS_TXT_FILE) if translator else f'成功更新 {OUTLOOKS_TXT_FILE} (移除了第一行)。'}{Style.RESET_ALL}")
                    
                    if final_outlook_token_after_signup and final_outlook_token_after_signup != outlook_refresh_token_initial:
                        print(f"{Fore.YELLOW}{EMOJI['UPDATE']} {translator.get('outlook.refresh_token_updated_for_used', email=outlook_email, token=final_outlook_token_after_signup) if translator else f'注意: Outlook账号 {outlook_email} 的刷新令牌已在获取验证码过程中更新为: {final_outlook_token_after_signup}。请手动更新 {OUTLOOKS_TXT_FILE} 中的对应行（如果计划复用）。当前已使用的行已被删除。'}{Style.RESET_ALL}")

                    git_sync_log_directory(
                        commit_message=f"Register Cursor acc: {cursor_email_to_register_with}, update {os.path.basename(OUTLOOKS_TXT_FILE)}",
                        translator=translator
                    )
                except Exception as e_outlook_write:
                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlook.file_write_error', file=OUTLOOKS_TXT_FILE, error=str(e_outlook_write)) if translator else f'更新 {OUTLOOKS_TXT_FILE} 失败: {str(e_outlook_write)}'}{Style.RESET_ALL}")
                
                if browser_manager: browser_manager.quit() # Quit browser on full success before sys.exit
                sys.exit(0) # Successful registration path in main ends
            
            else: # sign_up_account overall process failed
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.flow_failed_outlook_main') if translator else '账号注册或后续Token/保存流程失败。'}{Style.RESET_ALL}")
                if final_outlook_token_after_signup and final_outlook_token_after_signup != outlook_refresh_token_initial:
                    print(f"{Fore.YELLOW}{EMOJI['UPDATE']} {translator.get('outlook.refresh_token_updated_on_fail', email=outlook_email, token=final_outlook_token_after_signup) if translator else f'注意: Outlook账号 {outlook_email} 的刷新令牌在尝试注册过程中可能已更新为: {final_outlook_token_after_signup}。虽然注册失败，但建议手动更新 {OUTLOOKS_TXT_FILE} 中该行的刷新令牌。'}{Style.RESET_ALL}")

            # Fall through if any major step fails before this point or if sign_up_account returns False
            if browser_manager: browser_manager.quit() 
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序执行出现错误: {str(e)}")
        logging.error(traceback.format_exc())
    finally:
        if browser_manager:
            browser_manager.quit()
        
        # Final message if not exited by sys.exit(0) or sys.exit(1) within a choice block
        if 'choice' in locals():
            # Choices that handle their own exit or don't need this message
            handled_exit_choices = [1, 2, 3, 4, 6, 7, 8, 9] 
            if choice not in handled_exit_choices:
                 logging.info("程序执行完毕。")
        else: # Error before choice was made
            # This case might be rare now due to early exits on critical failures
            print(f"{Fore.RED}{EMOJI['ERROR']} 程序因初始化错误或未处理的异常而意外结束。{Style.RESET_ALL}")
