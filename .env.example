# 你的CF路由填写的域名, 多个域名用英文逗号分隔，注册时会随机选择其中之一
DOMAIN=domain1.com,domain2.net,sub.domain3.org
# 邮件服务地址
# 注册临时邮件服务 https://tempmail.plus
TEMP_MAIL=xxxxxx
# 设置的PIN码
TEMP_MAIL_EPIN=xxxxxx
# 使用的后缀
TEMP_MAIL_EXT=@mailto.plus
BROWSER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.92 Safari/537.36

# 代理
# BROWSER_PROXY='http://127.0.0.1:2080' 

# 动态代理API地址，如果配置了此项，将忽略 BROWSER_PROXY
# PROXY_URL=http://your-proxy-provider.com/api/getProxy
# 返回格式示例: {"code":200,"msg":"成功","data":{"proxy_list":["ip:port,ttl"]}}

# 无头模式 默认开启
# BROWSER_HEADLESS='True'
