import os
import uuid
import time
import hashlib
import base64
import requests
import json
from datetime import datetime

# Added: Constants for account persistence
LOG_DIR = "log"
ACCOUNTS_JSON_FILE = os.path.join(LOG_DIR, "accounts.json")

def generate_pkce_pair():
    verifier = base64.urlsafe_b64encode(os.urandom(43)).decode('utf-8').rstrip('=')
    challenge_bytes = hashlib.sha256(verifier.encode('utf-8')).digest()
    challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
    return {"verifier": verifier, "challenge": challenge}

def get_login_url(uuid_str, challenge):
    login_url = f"https://www.cursor.com/loginDeepControl?challenge={challenge}&uuid={uuid_str}&mode=login"
    return login_url

def query_auth_poll(uuid_str, verifier, proxies=None):
    auth_poll_url = f"https://api2.cursor.sh/auth/poll?uuid={uuid_str}&verifier={verifier}"
    try:
        response = requests.get(
            auth_poll_url,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cursor/0.48.6 Chrome/132.0.6834.210 Electron/34.3.4 Safari/537.36",
                "Accept": "*/*"
            },
            timeout=5,
            proxies=proxies
        )

        if response.status_code == 200:
            data = response.json()
            return data

        return None
    except:
        return None

# Added: Function to load accounts from JSON
def load_accounts_from_json():
    """从JSON文件加载账号信息列表"""
    if not os.path.exists(ACCOUNTS_JSON_FILE):
        return [] 

    try:
        with open(ACCOUNTS_JSON_FILE, "r", encoding="utf-8") as f:
            accounts = json.load(f)
            if not isinstance(accounts, list):
                print(f"[Warning] {ACCOUNTS_JSON_FILE} 格式错误，将返回空列表。")
                return []
            return accounts
    except json.JSONDecodeError:
        print(f"[Warning] {ACCOUNTS_JSON_FILE} 解析失败，将返回空列表。")
        return []
    except Exception as e:
        print(f"[Error] 读取 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}，将返回空列表。")
        return []

# Added: Function to save account to JSON
def save_account_to_json(email, password, token_info):
    """将账号信息（包括token详情）保存到JSON文件（列表形式）"""
    try:
        if not os.path.exists(LOG_DIR):
            os.makedirs(LOG_DIR, exist_ok=True) 
        
        accounts = load_accounts_from_json() 

        new_account = {
            "email": email,
            "password": password, 
            "saved_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        if isinstance(token_info, dict):
            new_account["token"] = token_info.get("token")
            new_account["days_left"] = token_info.get("days_left")
            new_account["expire_time"] = token_info.get("expire_time")
            new_account["token_type"] = "refreshed" 
            if "session_id" in token_info:
                new_account["session_id"] = token_info.get("session_id")
        elif isinstance(token_info, str):
             new_account["token"] = token_info
             new_account["token_type"] = "extracted" 
             new_account["days_left"] = None
             new_account["expire_time"] = None
             new_account["session_id"] = None
        else:
             new_account["token"] = None
             new_account["token_type"] = "unknown"
             new_account["days_left"] = None
             new_account["expire_time"] = None
             new_account["session_id"] = None

        found = False
        for i, acc in enumerate(accounts):
             if acc.get("email") == email:
                  accounts[i] = new_account
                  found = True
                  print(f"[Log] 账号 {email} 已存在于 {ACCOUNTS_JSON_FILE}，信息已更新。")
                  break
        if not found:
            accounts.append(new_account)
            print(f"[Log] 新账号 {email} 已添加到 {ACCOUNTS_JSON_FILE}")

        with open(ACCOUNTS_JSON_FILE, "w", encoding="utf-8") as f:
            json.dump(accounts, f, ensure_ascii=False, indent=4)
        
        print(f"[Log] 账号信息已成功保存到 {ACCOUNTS_JSON_FILE}")
        return True
    except Exception as e:
        print(f"[Error] 保存账号到 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}")
        return False

# Added: Function to get session information
def get_session_info(bearer_token, proxies=None):
    """
    获取会话信息
    """
    print("[Log] 正在尝试获取会话详细信息...")
    try:
        request_url = "https://www.cursor.com/api/auth/sessions"
        request_headers = {
            "Accept": "*/*",
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cursor/0.48.6 Chrome/132.0.6834.210 Electron/34.3.4 Safari/537.36",
            "Cookie": f"WorkosCursorSessionToken={bearer_token}"
        }
        
        response = requests.get(
            request_url,
            headers=request_headers,
            timeout=10,
            proxies=proxies
        )
        
        if response.status_code == 200:
            data = response.json()
            sessions = data.get("sessions", [])
            client_sessions = [s for s in sessions if s.get("type") == "SESSION_TYPE_CLIENT"]
            
            if not client_sessions:
                print("[Log] 未找到类型为CLIENT的会话。")
                return None
                
            client_sessions.sort(key=lambda x: x.get("createdAt", ""), reverse=True)
            latest_session = client_sessions[0]
            
            session_id = latest_session.get("sessionId")
            expires_at = latest_session.get("expiresAt")
            
            details = {"session_id": session_id}

            if expires_at:
                try:
                    expire_time_dt = datetime.strptime(expires_at.split('.')[0], "%Y-%m-%dT%H:%M:%S") 
                    current_time_utc = datetime.utcnow()
                    formatted_expire_time = expire_time_dt.strftime("%Y-%m-%d %H:%M:%S")
                    days_left = (expire_time_dt - current_time_utc).days
                    
                    details["expire_time"] = formatted_expire_time
                    details["days_left"] = days_left
                    
                    print("[Log] 会话详细信息获取成功。")
                    print(f"  [Log] 会话ID: {session_id}")
                    print(f"  [Log] 到期时间: {formatted_expire_time}")
                    print(f"  [Log] 剩余天数: {days_left}")
                except ValueError as e:
                    print(f"[Error] 无法解析会话日期格式: {expires_at} - {str(e)}")
                except Exception as date_error:
                    print(f"[Error] 计算会话日期差异时出错: {str(date_error)}")
            else:
                print("[Log] 会话信息不包含有效的过期时间。")
            return details
        else:
            print(f"[Error] 获取会话详细信息失败，状态码: {response.status_code}。响应: {response.text[:200]}")
            return None
            
    except Exception as e:
        print(f"[Error] 获取会话详细信息时发生错误: {str(e)}")
        return None

# Added: Function to get user email from /api/auth/me
def get_user_email_from_token(access_token, proxies=None):
    """
    Fetches user email from /api/auth/me using the access token.
    """
    print("[Log] 尝试通过API获取邮箱地址...")
    try:
        me_url = "https://www.cursor.com/api/auth/me"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cursor/0.48.6 Chrome/132.0.6834.210 Electron/34.3.4 Safari/537.36",
            "Accept": "application/json"
        }
        response = requests.get(me_url, headers=headers, proxies=proxies, timeout=10)

        if response.status_code == 200:
            data = response.json()
            email = data.get("email")
            if email:
                print(f"[Log] 通过API成功获取到邮箱地址: {email}")
                return email
            else:
                print("[Warning] API响应中未找到邮箱地址。")
                return None
        else:
            print(f"[Error] 调用 /api/auth/me 失败。状态码: {response.status_code}, 响应: {response.text[:200]}")
            return None
    except Exception as e:
        print(f"[Error] 通过API获取邮箱地址时发生错误: {str(e)}")
        return None

def main():
    pkce_pair = generate_pkce_pair()
    verifier = pkce_pair["verifier"]
    challenge = pkce_pair["challenge"]
    uuid_str = str(uuid.uuid4())
    login_url = get_login_url(uuid_str, challenge)
    print("[Log] 请在浏览器中打开以下URL进行登录:")
    print(login_url)

    retry_attempts = 60

    for i in range(retry_attempts):
        print(f"[Log] 等待登录中... ({i + 1}/{retry_attempts})")
        data = query_auth_poll(uuid_str, verifier)
        if data:
            print(data)
            access_token = data.get("accessToken")
            print("[Log] 登录成功。你的Cursor token:")
            print(access_token)

            # Added: Prompt for email and save account info
            email_to_save = None
            current_proxies = None # Define current_proxies, assuming None if not set up elsewhere

            # Attempt to fetch email via API
            if access_token:
                email_from_api = get_user_email_from_token(access_token, proxies=current_proxies)
                if email_from_api:
                    email_to_save = email_from_api
                else:
                    print("[Log] 未能通过API自动获取邮箱地址。")
            
            # If email not fetched from API, prompt user
            if not email_to_save:
                email_input = input("[Log] 请手动输入与此登录关联的邮箱地址 (按Enter跳过保存): ").strip()
                if email_input:
                    email_to_save = email_input
            
            if email_to_save: # Check if email_to_save has a value (either from API or input)
                password = None # Password is not available in this script's flow
                
                # Proxies are not explicitly set up in main for query_auth_poll, so passing None.
                # If main were to use proxies, that variable should be passed here.
                # current_proxies is already defined above
                
                session_details = get_session_info(bearer_token=access_token, proxies=current_proxies)

                token_info_to_save = {}
                if session_details:
                    token_info_to_save = {
                        "token": access_token, # The main token from login
                        "session_id": session_details.get("session_id"),
                        "expire_time": session_details.get("expire_time"),
                        "days_left": session_details.get("days_left")
                    }
                else:
                    # If session_details could not be fetched, save only the access_token string
                    token_info_to_save = access_token 

                save_account_to_json(email_to_save, password, token_info_to_save)
            else:
                print("[Log] 未提供邮箱地址 (自动或手动)，跳过保存账户信息步骤。")
            
            break
        time.sleep(5)

        if i == retry_attempts - 1:
            print("登录超时，请重试。")

if __name__ == "__main__":
    try:
        main()
    except Exception as error:
        print("错误:", error) 