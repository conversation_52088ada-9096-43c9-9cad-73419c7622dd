#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享的邮箱监听模块
- 提供token缓存机制
- IMAP连接管理
- 邮件解析和验证码提取
- 用于菜单4和菜单5的邮箱监听功能
"""

import time
import requests
import imaplib
import email as email_module
from email.header import decode_header
import re

# 尝试导入colorama，如果不存在则使用空的Style和Fore
try:
    from colorama import Fore, Style
except ImportError:
    class MockColor:
        def __getattr__(self, name):
            return ""
    Fore = MockColor()
    Style = MockColor()

# EMOJI 字典
EMOJI = {
    "START": "🚀",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "WARNING": "⚠️",
    "INFO": "ℹ️",
    "WAIT": "⏳",
    "MAIL": "📧",
    "MONEY": "💰",
    "SETTINGS": "⚙️",
    "MONITOR": "📡",
    "STOP": "🛑"
}

class SharedEmailMonitor:
    """共享的邮箱监听类"""
    
    def __init__(self, username, client_id, refresh_token, proxies=None):
        self.username = username
        self.client_id = client_id
        self.refresh_token = refresh_token
        self.proxies = proxies
        self.seen_email_ids = set()
        
        # Token缓存机制
        self.cached_access_token = None
        self.token_cache_time = None
        self.token_cache_duration = 3600  # Token缓存1小时
        
    def get_access_token(self):
        """获取访问令牌 - 使用缓存机制避免重复获取"""
        # 检查是否有缓存的Token且未过期
        if self.cached_access_token and self.token_cache_time:
            current_time = time.time()
            if current_time - self.token_cache_time < self.token_cache_duration:
                return self.cached_access_token
        
        # 缓存过期或不存在，获取新Token
        print(f"{Fore.CYAN}{EMOJI['WAIT']} 正在获取新的访问令牌...{Style.RESET_ALL}")
        try:
            headers = {
                'Host': 'login.microsoftonline.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
            }
            data = {
                "client_id": self.client_id,
                "refresh_token": self.refresh_token,
                "grant_type": "refresh_token",
            }

            token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"

            response = requests.post(token_url, headers=headers, data=data, timeout=10, proxies=self.proxies)
            response.raise_for_status()
            token_data = response.json()

            if "access_token" in token_data:
                access_token = token_data["access_token"]
                new_refresh_token = token_data.get("refresh_token")
                
                # 缓存新Token
                self.cached_access_token = access_token
                self.token_cache_time = time.time()
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 新Token获取成功并已缓存{Style.RESET_ALL}")
                
                # 更新refresh_token
                if new_refresh_token and new_refresh_token != self.refresh_token:
                    self.refresh_token = new_refresh_token
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Refresh Token已更新{Style.RESET_ALL}")
                    
                return access_token
            else:
                error_msg = token_data.get("error_description", "获取access_token失败")
                print(f"{Fore.RED}{EMOJI['ERROR']} Token错误: {error_msg}{Style.RESET_ALL}")
                return None

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 获取访问令牌失败: {str(e)}{Style.RESET_ALL}")
            return None
    
    def connect_imap(self):
        """连接IMAP服务器"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return None, "无法获取访问令牌"

            # IMAP OAuth2认证
            auth_string = f"user={self.username}\1auth=Bearer {access_token}\1\1"

            # 首先尝试商业版IMAP服务器
            try:
                mail = imaplib.IMAP4_SSL("outlook.office365.com", timeout=15)
                mail.authenticate("XOAUTH2", lambda x: auth_string.encode())
                return mail, None
            except imaplib.IMAP4.error as e_office:
                # 尝试个人版IMAP服务器
                try:
                    mail_alt = imaplib.IMAP4_SSL("imap-mail.outlook.com", timeout=15)
                    mail_alt.authenticate("XOAUTH2", lambda x: auth_string.encode())
                    return mail_alt, None
                except imaplib.IMAP4.error as e_personal:
                    error_msg = f"IMAP认证失败: {e_office}"
                    return None, error_msg

        except Exception as e:
            error_msg = f"IMAP连接异常: {str(e)}"
            return None, error_msg
    
    def parse_email_details(self, mail, email_id):
        """解析邮件详情并提取验证码"""
        try:
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            if status != 'OK':
                return None
            
            msg = email_module.message_from_bytes(msg_data[0][1])
            
            # 解析主题
            subject = msg['subject']
            if subject:
                decoded_subject = decode_header(subject)[0]
                if decoded_subject[1]:
                    subject = decoded_subject[0].decode(decoded_subject[1])
                else:
                    subject = decoded_subject[0]
            
            # 解析发件人
            from_addr = msg['from']
            
            # 解析日期
            date_str = msg['date']
            
            # 获取邮件正文
            body = ""
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        try:
                            body = part.get_payload(decode=True).decode('utf-8')
                            break
                        except:
                            try:
                                body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                                break
                            except:
                                continue
            else:
                try:
                    body = msg.get_payload(decode=True).decode('utf-8')
                except:
                    try:
                        body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                    except:
                        body = ""
            
            # 尝试提取验证码
            verification_code = None
            if body:
                # 匹配6位数字验证码
                code_patterns = [
                    r'\b(\d{6})\b',  # 6位数字
                    r'code[:\s]*(\d{6})',  # "code: 123456"
                    r'verification[:\s]*(\d{6})',  # "verification: 123456"
                    r'verify[:\s]*(\d{6})',  # "verify: 123456"
                ]
                
                for pattern in code_patterns:
                    matches = re.findall(pattern, body, re.IGNORECASE)
                    if matches:
                        verification_code = matches[0]
                        break
            
            return {
                'email_id': email_id.decode(),
                'subject': subject,
                'from': from_addr,
                'date': date_str,
                'body': body[:200] + '...' if len(body) > 200 else body,
                'verification_code': verification_code
            }
            
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 解析邮件失败: {str(e)}{Style.RESET_ALL}")
            return None
    
    def initialize_seen_emails(self):
        """初始化已见过的邮件ID列表，并显示最新邮件"""
        print(f"{Fore.YELLOW}{EMOJI['SETTINGS']} 正在初始化已有邮件列表...{Style.RESET_ALL}")
        
        mail, error = self.connect_imap()
        if error:
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化失败: {error}{Style.RESET_ALL}")
            return False
        
        try:
            # 选择收件箱
            status, _ = mail.select('inbox')
            if status != 'OK':
                print(f"{Fore.RED}{EMOJI['ERROR']} 无法选择收件箱{Style.RESET_ALL}")
                mail.logout()
                return False

            status, messages = mail.search(None, 'ALL')
            if status == 'OK':
                email_ids = messages[0].split()
                
                if email_ids:
                    # 检查最新邮件是否包含验证码
                    latest_email_id = email_ids[-1]
                    latest_email_details = self.parse_email_details(mail, latest_email_id)

                    if latest_email_details and latest_email_details['verification_code']:
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 🎉 发现现有验证码: {latest_email_details['verification_code']}{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}📧 来自: {latest_email_details['subject']}{Style.RESET_ALL}")

                    # 标记所有现有邮件为已见
                    for email_id in email_ids:
                        self.seen_email_ids.add(email_id.decode())

                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 已标记 {len(email_ids)} 封现有邮件{Style.RESET_ALL}")
                else:
                    print(f"{Fore.BLUE}{EMOJI['INFO']} 收件箱为空{Style.RESET_ALL}")
            
            mail.logout()
            return True
            
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化过程中出错: {str(e)}{Style.RESET_ALL}")
            mail.logout()
            return False
    
    def check_for_new_emails(self):
        """检查新邮件并返回验证码（如果找到）"""
        mail, error = self.connect_imap()
        if error:
            print(f"{Fore.RED}{EMOJI['ERROR']} 连接失败: {error}{Style.RESET_ALL}")
            return None
        
        try:
            # 选择收件箱
            status, _ = mail.select('inbox')
            if status != 'OK':
                print(f"{Fore.RED}{EMOJI['ERROR']} 无法选择收件箱{Style.RESET_ALL}")
                mail.logout()
                return None

            status, messages = mail.search(None, 'ALL')
            if status == 'OK':
                email_ids = messages[0].split()
                new_emails = []
                
                for email_id in email_ids:
                    email_id_str = email_id.decode()
                    if email_id_str not in self.seen_email_ids:
                        email_details = self.parse_email_details(mail, email_id)
                        if email_details:
                            new_emails.append(email_details)
                            self.seen_email_ids.add(email_id_str)
                
                mail.logout()
                
                if new_emails:
                    for email_details in new_emails:
                        print(f"\n{Fore.GREEN}{'='*50}{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 收到新邮件!{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}📧 主题: {email_details['subject']}{Style.RESET_ALL}")
                        print(f"{Fore.BLUE}📤 发件人: {email_details['from']}{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}📅 日期: {email_details['date']}{Style.RESET_ALL}")
                        if email_details['verification_code']:
                            print(f"{Fore.GREEN}🎉 找到验证码: {email_details['verification_code']}{Style.RESET_ALL}")
                            return email_details['verification_code']
                        print(f"{Fore.CYAN}📝 内容预览: {email_details['body']}{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}{'='*50}{Style.RESET_ALL}")
                
                return None
            else:
                mail.logout()
                return None
            
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 检查邮件时出错: {str(e)}{Style.RESET_ALL}")
            mail.logout()
            return None

    def monitor_for_verification_code(self, max_attempts=60, interval=5, continuous_mode=False):
        """持续监听验证码邮件
        
        Args:
            max_attempts: 最大监听次数 (continuous_mode=True时无效)
            interval: 监听间隔(秒)
            continuous_mode: 是否持续监听模式，即使找到验证码也不停止
        """
        print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['MAIL']} 开始监听 {self.username} 的验证码邮件{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        if continuous_mode:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 监听模式: 持续监听 (找到验证码后继续监听){Style.RESET_ALL}")
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 检查间隔: {interval} 秒 | 按 Ctrl+C 停止监听{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 监听设置: 最多 {max_attempts} 次，间隔 {interval} 秒{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 按 Ctrl+C 可以随时停止监听{Style.RESET_ALL}")
        
        print(f"{Fore.CYAN}{'-'*60}{Style.RESET_ALL}")
        
        # 初始化已有邮件
        if not self.initialize_seen_emails():
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化失败，无法继续监听{Style.RESET_ALL}")
            return None
        
        print(f"\n{Fore.CYAN}{EMOJI['MONITOR']} 开始监听新邮件...{Style.RESET_ALL}")
        
        try:
            attempt = 0
            monitoring = True
            first_verification_code = None  # 存储第一个找到的验证码
            
            while monitoring:
                if not continuous_mode and attempt >= max_attempts:
                    break
                    
                attempt += 1
                start_time = time.time()
                
                current_time = time.strftime("%H:%M:%S")
                if continuous_mode:
                    print(f"{Fore.CYAN}🔍 [{current_time}] 检查新邮件... (持续监听中){Style.RESET_ALL}", end='\r')
                else:
                    print(f"{Fore.CYAN}🔍 [{current_time}] 检查新邮件... ({attempt}/{max_attempts}){Style.RESET_ALL}", end='\r')
                
                verification_code_found = self.check_for_new_emails()
                
                if verification_code_found:
                    print(f"\n{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 🎉 成功获取验证码: {verification_code_found}{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
                    
                    if continuous_mode:
                        if first_verification_code is None:
                            first_verification_code = verification_code_found
                        print(f"{Fore.CYAN}{EMOJI['INFO']} 持续监听模式: 验证码已显示，继续监听新邮件...{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}{EMOJI['INFO']} 验证码已获取，请复制使用{Style.RESET_ALL}")
                        return verification_code_found
                
                # 精确控制检查间隔
                elapsed_time = time.time() - start_time
                sleep_time = max(0, interval - elapsed_time)
                
                if sleep_time > 0:
                    try:
                        time.sleep(sleep_time)
                    except KeyboardInterrupt:
                        print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 用户中断监听{Style.RESET_ALL}")
                        monitoring = False
                        break
            
            # 监听结束的提示
            if not monitoring:
                print(f"\n{Fore.YELLOW}{EMOJI['INFO']} 监听已被用户中断{Style.RESET_ALL}")
                if continuous_mode and first_verification_code:
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 本次监听期间获取到的第一个验证码: {first_verification_code}{Style.RESET_ALL}")
                    return first_verification_code
            elif not continuous_mode:
                print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 监听结束，已达到最大尝试次数 ({max_attempts})，未收到验证码{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{EMOJI['INFO']} 建议检查:{Style.RESET_ALL}")
                print(f"{Fore.CYAN}  1. 是否已发送验证码到该邮箱{Style.RESET_ALL}")
                print(f"{Fore.CYAN}  2. 邮箱是否正常接收邮件{Style.RESET_ALL}")
                print(f"{Fore.CYAN}  3. 网络连接是否正常{Style.RESET_ALL}")
            
            return first_verification_code if continuous_mode else None
            
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 监听被中断{Style.RESET_ALL}")
            if continuous_mode and first_verification_code:
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 本次监听期间获取到的第一个验证码: {first_verification_code}{Style.RESET_ALL}")
                return first_verification_code
            return None