import os
import platform
import json
import sys
import string
from colorama import Fore, Style
from enum import Enum
from typing import Optional
import threading
import subprocess # 用于执行 Git 命令
import totally_reset_cursor # 确保导入
import pathlib
import shutil
import sqlite3
import tempfile # +++ 新增导入 +++

# 定义自定义异常类
class TurnstileError(Exception):
    """Turnstile验证过程中的错误"""
    pass

from exit_cursor import ExitCursor
import go_cursor_help
import patch_cursor_get_machine_id
from reset_machine import MachineIDResetter

from cursor_acc_info import display_account_info, UsageManager, format_subscription_type, get_display_width # Import UsageManager and helper functions
from config_vip import get_config  # 导入config_vip中的get_config函数
from get_user_token import get_token_from_cookie # 导入新的 token 获取函数
from patch_cursor_get_machine_id import get_cursor_paths 
import requests
import concurrent.futures
import time # 确保导入 time
from read_outlook_inbox import fetch_latest_verification_code # +++ 新增导入 +++

os.environ["PYTHONVERBOSE"] = "0"
os.environ["PYINSTALLER_VERBOSE"] = "0"

import time
import random
from cursor_auth_manager import CursorAuthManager
import os
from logger import logging
from browser_utils import BrowserManager
from get_email_code import EmailVerificationHandler
from logo import print_logo
from config import Config
import traceback # 添加traceback模块导入
from datetime import datetime
import uuid
from redirect_resolver import get_final_url # +++ 新增导入 +++

# 定义 EMOJI 字典
EMOJI = {
    "ERROR": "❌",
    "WARNING": "⚠️",
    "INFO": "ℹ️",
    "SUCCESS": "✅",
    # 添加 register manual 中的 emoji
    'START': '🚀',
    'FORM': '📝',
    'VERIFY': '🔄',
    'PASSWORD': '🔑',
    'CODE': '📱',
    'DONE': '✨',
    'WAIT': '⏳',
    'MAIL': '📧',
    'KEY': '🔐',
    'UPDATE': '🔄',
    'SAVE': '💾',
    'SUBSCRIPTION': '📅',
    'TIME': '⏳',
    'USAGE': '📊',
    'PREMIUM': '⭐',
    'BASIC': '📝',
    'USER': '👤',
    'SEARCH': '🔍',
    'RESET': '🔄',
    'SETTINGS': '⚙️',
    'MONITOR': '📡'
}

# 定义日志和账号文件常量
LOG_DIR = "log"
# ACCOUNT_FILE = os.path.join(LOG_DIR, "account_info.json") # 旧的单账号文件
ACCOUNTS_JSON_FILE = os.path.join(LOG_DIR, "accounts.json") # 新的多账号文件


def wait_with_spinner(duration: float, message: str = "Loading"):
    """在终端显示旋转动画等待指定时间"""
    spinners = ['|', '/', '-', '\\']
    start_time = time.time()
    # 如果总时长很短，减少打印间隔，避免动画效果太差或不显示
    sleep_interval = min(0.1, duration / 4) if duration > 0 else 0.1 
    
    line_width = 0 # 用于记录上一行的宽度
    
    while time.time() - start_time < duration:
        loop_start_time = time.time()
        for spinner in spinners:
            current_elapsed = time.time() - start_time
            if current_elapsed >= duration:
                break
            # 使用 \r 回到行首覆盖，显示加载信息和旋转动画
            # 确保清除足够长度以覆盖之前的输出
            # clear_line = '\n' + ' ' * (len(message) + 20) + '\n' 
            # sys.stdout.write(f'{clear_line}{Fore.CYAN}{EMOJI.get("WAIT", "⏳")} {message}... {spinner}{Style.RESET_ALL} ')
            
            output_string = f"{Fore.CYAN}{EMOJI.get('WAIT', '⏳')} {message}... {spinner}{Style.RESET_ALL}"
            # 使用回车符 \r 将光标移到行首
            # 使用 ljust 以空格填充，确保覆盖上一行的内容
            sys.stdout.write(f"\r{output_string}".ljust(line_width))
            sys.stdout.flush()
            # 实际打印的字符数可能因ANSI转义序列而异，但为了覆盖，使用无转义序列的长度通常足够。
            # 或者更准确地，我们可以估算一个足够大的宽度，或者计算剥离ANSI后的字符串长度。
            # 为简单起见，我们先用 output_string 的原始长度。
            line_width = len(output_string) # 更新当前行宽度

            # 短暂休眠以控制动画速度
            time.sleep(sleep_interval)
            # 再次检查时间，避免在 sleep 后超出总时长
            if time.time() - start_time >= duration:
                 break
        # 如果内循环因为 spinner 走完而结束，检查是否还需要等待
        if time.time() - start_time >= duration:
             break
        # 如果内循环很快（例如 sleep_interval 极小），确保至少等待一点时间
        if time.time() - loop_start_time < sleep_interval * len(spinners) :
             time.sleep(max(0, sleep_interval * len(spinners) - (time.time() - loop_start_time) ))
             
    # 清除加载行并换行
    # sys.stdout.write('\n' + ' ' * (len(message) + 20) + '\n') # 清除足够宽度的字符
    # 清除最后一行加载动画
    sys.stdout.write('\r'.ljust(line_width + 5)) # 多加一些空格确保清除干净
    sys.stdout.write('\r') # 光标移到行首
    sys.stdout.flush()
    # 最终完成时可以打印一个换行，如果需要的话
    # print() # 如果希望在 spinner 结束后有一个干净的换行


class VerificationStatus(Enum):
    """验证状态枚举"""

    PASSWORD_PAGE = "@name=password"
    CAPTCHA_PAGE = "@data-index=0"
    ACCOUNT_SETTINGS = "Account Settings"


def save_screenshot(tab, stage: str, timestamp: bool = True) -> None:
    """
    保存页面截图

    Args:
        tab: 浏览器标签页对象
        stage: 截图阶段标识
        timestamp: 是否添加时间戳
    """
    try:
        # 创建 screenshots 目录
        screenshot_dir = "screenshots"
        if not os.path.exists(screenshot_dir):
            os.makedirs(screenshot_dir)

        # 生成文件名
        if timestamp:
            filename = f"turnstile_{stage}_{int(time.time())}.png"
        else:
            filename = f"turnstile_{stage}.png"

        filepath = os.path.join(screenshot_dir, filename)

        # 保存截图
        tab.get_screenshot(filepath)
        logging.debug(f"截图已保存: {filepath}")
    except Exception as e:
        logging.warning(f"截图保存失败: {str(e)}")


def check_verification_success(tab, translator=None) -> Optional[VerificationStatus]:
    """
    检查验证是否成功

    Returns:
        VerificationStatus: 验证成功时返回对应状态，失败返回 None
    """
    for status in VerificationStatus:
        if tab.ele(status.value):
            return status
    return None


def handle_turnstile(tab, max_retries: int = 2, retry_interval: tuple = (1, 2), translator=None) -> bool:
    """
    处理 Turnstile 验证

    Args:
        tab: 浏览器标签页对象
        max_retries: 最大重试次数
        retry_interval: 重试间隔时间范围(最小值, 最大值)

    Returns:
        bool: 验证是否成功

    Raises:
        TurnstileError: 验证过程中出现异常
    """
    # 使用 print 替代 logging
    print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('verify.turnstile_check') if translator else '正在检测 Turnstile 验证...'}{Style.RESET_ALL}")
    # save_screenshot(tab, "start")

    retry_count = 0
    verification_status = None # 用于记录最后一次检查到的状态

    try:
        while retry_count < max_retries:
            retry_count += 1
            # print(f"{Fore.CYAN}第 {retry_count} 次尝试验证{Style.RESET_ALL}") # 调试日志，可注释掉

            try:
                # 定位验证框元素
                challenge_check = (
                    tab.ele("@id=cf-turnstile", timeout=2)
                    .child()
                    .shadow_root.ele("tag:iframe")
                    .ele("tag:body")
                    .sr("tag:input")
                )

                if challenge_check:
                    # print(f"{Fore.YELLOW}{EMOJI['INFO']} 检测到 Turnstile 验证框，开始处理...{Style.RESET_ALL}")
                    # 随机延时后点击验证
                    time.sleep(random.uniform(0.5, 1.0)) # MODIFIED: Reduced delay
                    challenge_check.click()
                    time.sleep(1.0) # MODIFIED: Reduced delay

                    # 保存验证后的截图
                    # save_screenshot(tab, "clicked")

                    # 检查验证结果 (传递 translator)
                    verification_status = check_verification_success(tab, translator)
                    if verification_status:
                        # print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Turnstile 验证通过{Style.RESET_ALL}") # 移动打印逻辑
                        # save_screenshot(tab, "success")
                        # 在这里打印成功消息，因为已经确认验证成功了
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('verify.success_page', page=verification_status.name) if translator else f'验证成功 - 已到达{verification_status.name}页面'}{Style.RESET_ALL}")
                        return True

            except Exception as e:
                # print(f"{Fore.YELLOW}当前尝试未成功: {str(e)}{Style.RESET_ALL}") # 调试日志
                pass

            # 再次检查是否已经验证成功 (传递 translator)
            verification_status = check_verification_success(tab, translator)
            if verification_status:
                 # 在这里打印成功消息
                 print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('verify.success_page', page=verification_status.name) if translator else f'验证成功 - 已到达{verification_status.name}页面'}{Style.RESET_ALL}")
                 return True

            # 随机延时后继续下一次尝试
            time.sleep(random.uniform(*retry_interval))

        # 超出最大重试次数
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('verify.turnstile_max_retries', max=max_retries) if translator else f'验证失败 - 已达到最大重试次数 {max_retries}'}{Style.RESET_ALL}")
        # save_screenshot(tab, "failed")
        return False

    except Exception as e:
        error_msg = f"Turnstile 验证过程发生异常: {str(e)}"
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('verify.turnstile_exception', error=error_msg) if translator else error_msg}{Style.RESET_ALL}")
        # save_screenshot(tab, "error")
        raise TurnstileError(error_msg)


def save_account_to_json(email, password, token_info, translator=None):
    """将账号信息（包括token详情）保存到JSON文件（列表形式）"""
    try:
        if not os.path.exists(LOG_DIR):
            os.makedirs(LOG_DIR) # 确保log目录存在
        
        accounts = load_accounts_from_json(translator) # 使用新函数加载账号

        # 准备新的账户数据
        new_account = {
            "email": email,
            "password": password,
            "saved_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # 根据 token_info 类型填充 token 相关字段
        if isinstance(token_info, dict):
            new_account["token"] = token_info.get("token")
            new_account["days_left"] = token_info.get("days_left")
            new_account["expire_time"] = token_info.get("expire_time")
            new_account["token_type"] = "refreshed"
            # 添加会话信息字段
            if "session_id" in token_info:
                new_account["session_id"] = token_info.get("session_id")
        elif isinstance(token_info, str):
             new_account["token"] = token_info
             new_account["token_type"] = "extracted"
             # 其他字段可以设为 None 或不包含
             new_account["days_left"] = None
             new_account["expire_time"] = None
        else:
             new_account["token"] = None
             new_account["token_type"] = "unknown"
             new_account["days_left"] = None
             new_account["expire_time"] = None

        # 检查邮箱是否已存在，如果存在则更新，否则添加
        found = False
        for i, acc in enumerate(accounts):
             if acc.get("email") == email:
                  accounts[i] = new_account
                  found = True
                  print(f"{Fore.YELLOW}{EMOJI['UPDATE']} {translator.get('accounts.updated', email=email, file=ACCOUNTS_JSON_FILE) if translator else f'账号 {email} 已存在于 {ACCOUNTS_JSON_FILE}，信息已更新。'}{Style.RESET_ALL}")
                  break
        if not found:
            accounts.append(new_account)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.added', email=email, file=ACCOUNTS_JSON_FILE) if translator else f'新账号 {email} 已添加到 {ACCOUNTS_JSON_FILE}'}{Style.RESET_ALL}")

        # 写回文件
        with open(ACCOUNTS_JSON_FILE, "w", encoding="utf-8") as f:
            json.dump(accounts, f, ensure_ascii=False, indent=4)
        
        return True
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.save_error', file=ACCOUNTS_JSON_FILE, error=str(e)) if translator else f'保存账号到 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}'}{Style.RESET_ALL}")
        return False


def load_accounts_from_json(translator=None):
    """从JSON文件加载账号信息列表"""
    if not os.path.exists(ACCOUNTS_JSON_FILE):
        return [] # 文件不存在，返回空列表

    try:
        with open(ACCOUNTS_JSON_FILE, "r", encoding="utf-8") as f:
            accounts = json.load(f)
            if not isinstance(accounts, list):
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.json_corrupted', file=ACCOUNTS_JSON_FILE) if translator else f'警告: {ACCOUNTS_JSON_FILE} 格式错误，将返回空列表。'}{Style.RESET_ALL}")
                return []
            return accounts
    except json.JSONDecodeError:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.json_decode_error', file=ACCOUNTS_JSON_FILE) if translator else f'警告: {ACCOUNTS_JSON_FILE} 解析失败，将返回空列表。'}{Style.RESET_ALL}")
        return []
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.json_read_error', file=ACCOUNTS_JSON_FILE, error=str(e)) if translator else f'读取 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}，将返回空列表。'}{Style.RESET_ALL}")
        return []


def delete_account_from_json(index_to_delete, translator=None):
    """从JSON文件中删除指定索引的账号"""
    accounts = load_accounts_from_json(translator)

    if not accounts:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.delete.no_accounts', file=ACCOUNTS_JSON_FILE) if translator else f'账号文件 {ACCOUNTS_JSON_FILE} 为空，无法删除。'}{Style.RESET_ALL}")
        return False

    if not (0 <= index_to_delete < len(accounts)):
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.delete.invalid_index') if translator else '无效的账号序号。'}{Style.RESET_ALL}")
        return False

    deleted_account = accounts.pop(index_to_delete)
    deleted_email = deleted_account.get("email", "未知邮箱")

    try:
        with open(ACCOUNTS_JSON_FILE, "w", encoding="utf-8") as f:
            json.dump(accounts, f, ensure_ascii=False, indent=4)
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.delete.success', email=deleted_email) if translator else f'账号 {deleted_email} 已成功删除。'}{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.delete.write_error', file=ACCOUNTS_JSON_FILE, error=str(e)) if translator else f'写入更新到 {ACCOUNTS_JSON_FILE} 时出错: {str(e)}'}{Style.RESET_ALL}")
        # 如果写入失败，尝试恢复原状（可选，但更安全）
        # accounts.insert(index_to_delete, deleted_account) # 尝试恢复
        return False


def get_cursor_session_token(tab, max_attempts=3, retry_interval=2):
    """
    获取Cursor会话token，带有重试机制
    :param tab: 浏览器标签页
    :param max_attempts: 最大尝试次数
    :param retry_interval: 重试间隔(秒)
    :return: session token 或 None
    """
    logging.info("开始获取cookie")
    attempts = 0

    while attempts < max_attempts:
        try:
            cookies = tab.cookies()
            for cookie in cookies:
                if cookie.get("name") == "WorkosCursorSessionToken":
                    return cookie["value"].split("%3A%3A")[1]

            attempts += 1
            if attempts < max_attempts:
                logging.warning(
                    f"第 {attempts} 次尝试未获取到CursorSessionToken，{retry_interval}秒后重试..."
                )
                time.sleep(retry_interval)
            else:
                logging.error(
                    f"已达到最大尝试次数({max_attempts})，获取CursorSessionToken失败"
                )

        except Exception as e:
            logging.error(f"获取cookie失败: {str(e)}")
            attempts += 1
            if attempts < max_attempts:
                logging.info(f"将在 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)

    return None


def update_cursor_auth(email=None, access_token=None, refresh_token=None):
    """
    更新Cursor的认证信息的便捷函数
    """
    auth_manager = CursorAuthManager()
    return auth_manager.update_auth(email, access_token, refresh_token)


def sign_up_account(browser, tab, account, password, resolved_sign_up_url, settings_url, translator=None, proxies=None, account_from_file=None, client_id_from_file=None, token_from_file=None):
    """处理完整的账号注册流程，并在成功后保存账号信息。期望 resolved_sign_up_url 已经是最终的 URL。"""
    # 初始化token缓存变量，避免重复获取OAuth2 token
    cached_refresh_token = token_from_file
    cached_client_id = client_id_from_file
    
    # 使用 print 替代 logging
    print(f"{Fore.CYAN}{EMOJI['START']} {translator.get('register.start_flow') if translator else '=== 开始注册账号流程 ==='}{Style.RESET_ALL}")

    # --- resolved_sign_up_url 现在是直接传入的，不再在此处解析 ---
    # actual_sign_up_url = sign_up_url # 默认为原始 URL
    # try:
    #     print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('register.resolve_signup_url_start', url=sign_up_url) if translator else f'开始解析注册 URL: {sign_up_url}'}{Style.RESET_ALL}")
    #     # get_final_url 会打印其详细步骤
    #     # --- 将 proxies 变量传递给 get_final_url ---
    #     resolved_url = get_final_url(sign_up_url, proxies=proxies) 
    #     print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.resolve_signup_url_success', url=resolved_url) if translator else f'注册 URL 解析成功，将使用: {resolved_url}'}{Style.RESET_ALL}")
    #     actual_sign_up_url = resolved_url
    # except (requests.exceptions.RequestException, ValueError) as e_resolve: # 捕获来自 get_final_url 的特定异常
    #     print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.resolve_signup_url_failed', url=sign_up_url, error=str(e_resolve)) if translator else f'解析注册 URL {sign_up_url} 失败: {str(e_resolve)}'}{Style.RESET_ALL}")
    #     # import traceback # 可选的调试
    #     # print(f"{Fore.RED}{traceback.format_exc()}{Style.RESET_ALL}") # 可选的调试
    #     return False # 关键步骤失败，无法继续注册
    # except Exception as e_resolve_other: # 捕获任何其他意外错误
    #     print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.resolve_signup_url_unexpected', url=sign_up_url, error=str(e_resolve_other)) if translator else f'解析注册 URL {sign_up_url} 时发生未知错误: {str(e_resolve_other)}'}{Style.RESET_ALL}")
    #     # import traceback # 可选的调试
    #     # print(f"{Fore.RED}{traceback.format_exc()}{Style.RESET_ALL}") # 可选的调试
    #     return False # 关键步骤失败
    # --- 结束解析注册 URL ---

    print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('register.visit_page', url=resolved_sign_up_url) if translator else f'正在访问注册页面: {resolved_sign_up_url}'}{Style.RESET_ALL}")
    tab.get(resolved_sign_up_url) # 使用传入的已解析 URL
    # Add a wait here to ensure the page loads fully
    wait_with_spinner(random.uniform(3, 5), translator.get('register.wait_signup_page_load') if translator else "Waiting for sign-up page to load")

    try:
        # Check for email field first as it's more consistently present if other fields are gone.
        if tab.ele("@name=email"):
            print(f"{Fore.CYAN}{EMOJI['FORM']} {translator.get('register.fill_info') if translator else '正在填写注册信息...'}{Style.RESET_ALL}")
            
            # Attempt to fill first_name and last_name if they exist
            # first_name_field = tab.ele("@name=first_name", timeout=0.5) # Short timeout to check existence
            # if first_name_field:
            #     first_name_field.actions.click().input(first_name) # first_name is no longer passed
            #     print(f"    {Fore.CYAN}➡️ {translator.get('register.field.first_name') if translator else '名字'}: {first_name}{Style.RESET_ALL}")
            #     wait_with_spinner(random.uniform(0.5, 1.5), "Waiting after input")

            # last_name_field = tab.ele("@name=last_name", timeout=0.5)
            # if last_name_field:
            #     last_name_field.actions.click().input(last_name) # last_name is no longer passed
            #     print(f"    {Fore.CYAN}➡️ {translator.get('register.field.last_name') if translator else '姓氏'}: {last_name}{Style.RESET_ALL}")
            #     wait_with_spinner(random.uniform(0.5, 1.5), "Waiting after input")

            tab.actions.click("@name=email").input(account)
            print(f"    {Fore.CYAN}➡️ {translator.get('register.field.email') if translator else '邮箱'}: {account}{Style.RESET_ALL}")
            wait_with_spinner(random.uniform(0.5, 1.5), "Waiting after input")

            print(f"{Fore.CYAN}{EMOJI['FORM']} {translator.get('register.submit_info') if translator else '提交注册信息...'}{Style.RESET_ALL}")
            tab.actions.click("@type=submit")
            wait_with_spinner(random.uniform(2, 4), translator.get('register.wait_after_submit') if translator else "Waiting after submit")
        else:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.element_not_found', element='@name=email') if translator else '注册页面元素 @name=email 未找到'}{Style.RESET_ALL}")
            return False

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.page_load_failed', error=str(e)) if translator else f'注册页面访问或填写失败: {str(e)}'}{Style.RESET_ALL}")
        return False

    # Removed Turnstile checks here

    # New logic: Click XPath to proceed to email verification, replacing password input
    try:
        xpath_to_click = "/html/body/div[1]/div/div/div[2]/div/form/div/div[4]"
        print(f"{Fore.CYAN}{EMOJI['FORM']} {translator.get('register.click_xpath_to_verify', xpath=xpath_to_click) if translator else f'正在点击元素 ({xpath_to_click}) 以进入邮箱验证页面...'}{Style.RESET_ALL}")
        
        # Ensure the element is present before clicking, with a reasonable timeout
        # Using s_ele to find the element, which might wait briefly based on default tab timeout.
        # Add an explicit wait or check if needed, but often .click() on an s_ele implies waiting.
        # For robustness, a short explicit wait for the element to be clickable could be added if issues arise.
        # tab.wait_ele(f"xpath:{xpath_to_click}", timeout=5) # Optional: wait for element to be present
        
        element_to_click = tab.ele(f"xpath:{xpath_to_click}", timeout=10) # Increased timeout for element finding
        if element_to_click:
            element_to_click.click()
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.xpath_clicked_success') if translator else '指定元素已点击。'}{Style.RESET_ALL}")
            wait_with_spinner(random.uniform(3, 5), translator.get('register.wait_after_xpath_click') if translator else "等待邮箱验证页面加载...") # Increased wait
        else:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.click_xpath_failed_not_found', xpath=xpath_to_click) if translator else f'无法找到用于点击的 XPath 元素: {xpath_to_click}'}{Style.RESET_ALL}")
            return False

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.click_xpath_failed', xpath=xpath_to_click, error=str(e)) if translator else f'点击 XPath {xpath_to_click} 时发生错误: {str(e)}'}{Style.RESET_ALL}")
        # import traceback # For debugging if needed
        # print(f"{Fore.RED}{traceback.format_exc()}{Style.RESET_ALL}") # For debugging if needed
        return False

    if tab.ele("This email is not available."):
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.email_taken') if translator else '注册失败：邮箱已被使用'}{Style.RESET_ALL}")
        return False

    # Removed Turnstile check after password here

    # This flag will determine if we need to enter the generic while True loop
    # for waiting for CAPTCHA page or Account Settings page.
    # Renaming for clarity: stage_after_initial_submit_and_xpath_click_completed
    stage_after_initial_submit_and_xpath_click_completed = False


    # Directly attempt to process the CAPTCHA page.
    if tab.ele(VerificationStatus.CAPTCHA_PAGE.value, timeout=3): # Slightly increased timeout for CAPTCHA page
        print(f"{Fore.CYAN}{EMOJI['MAIL']} {translator.get('register.get_email_code') if translator else '正在获取邮箱验证码...'}{Style.RESET_ALL}")
        
        code = None
        if account_from_file and cached_client_id and cached_refresh_token:
            print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('register.fetch_code_via_outlook_module') if translator else '尝试使用 read_outlook_inbox.py 获取验证码...'}{Style.RESET_ALL}")
            fetched_code, new_refresh_token, error_details = fetch_latest_verification_code(
                username=account_from_file, 
                current_refresh_token=cached_refresh_token, 
                client_id=cached_client_id, 
                proxies=proxies
            )
            if error_details:
                # 邮件过期相关的错误信息已被禁用显示
                if "邮件验证码可能已过期" not in error_details:
                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.fetch_code_outlook_error', error=error_details) if translator else f'使用 read_outlook_inbox.py 获取验证码失败: {error_details}'}{Style.RESET_ALL}")
                code = None # Ensure code is None if there was an error
            elif not fetched_code:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.fetch_code_outlook_no_code') if translator else 'read_outlook_inbox.py 未能返回验证码。'}{Style.RESET_ALL}")
                code = None
            else:
                code = fetched_code
            # 自动更新缓存的token，避免第二次调用时重复获取
            if new_refresh_token and new_refresh_token != cached_refresh_token:
                cached_refresh_token = new_refresh_token
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Token已更新并缓存到内存中，后续调用将使用新token{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.fetch_code_missing_outlook_data') if translator else '无法从 outlooks.txt 获取必要的 clientId 和 refreshToken 来获取验证码。'}{Style.RESET_ALL}")
            # code remains None

        if not code:
            # 获取验证码失败的日志已被禁用
            # print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.get_code_failed') if translator else '获取验证码失败'}{Style.RESET_ALL}")
            return False

        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.code_received', code=code) if translator else f'成功获取验证码: {code}'}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['CODE']} {translator.get('register.enter_code') if translator else '正在输入验证码...'}{Style.RESET_ALL}")
        i = 0
        for digit_char in code:
            tab.ele(f"@data-index={i}").input(digit_char)
            time.sleep(0.2)
            i += 1
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.code_entered') if translator else '验证码输入完成'}{Style.RESET_ALL}")
        wait_with_spinner(random.uniform(1, 2), translator.get('register.wait_after_code') if translator else "Waiting after code entry")
        stage_after_initial_submit_and_xpath_click_completed = True # CAPTCHA processing done via fast path
    else:
        # CAPTCHA page not immediately found after XPath click.
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('register.captcha_page_not_found_promptly_after_xpath_click') if translator else '点击XPath后，未立即检测到验证码页面。将进入标准等待逻辑。'}{Style.RESET_ALL}")
        # stage_after_initial_submit_and_xpath_click_completed remains False, generic loop will run.

    # Generic waiting loop if the stage wasn't completed by a direct path.
    if not stage_after_initial_submit_and_xpath_click_completed:
        max_wait_cycles = 20 # Approx 10 seconds (20 * 0.5s)
        cycles = 0
        while cycles < max_wait_cycles:
            cycles += 1
            try:
                status = check_verification_success(tab, translator)
                if status == VerificationStatus.ACCOUNT_SETTINGS:
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('verify.success_page', page=status.name) if translator else f'验证成功 - 已到达{status.name}页面'}{Style.RESET_ALL}")
                    stage_after_initial_submit_and_xpath_click_completed = True
                    break # Stage completed

                if status == VerificationStatus.CAPTCHA_PAGE:
                    print(f"{Fore.CYAN}{EMOJI['MAIL']} {translator.get('register.get_email_code') if translator else '正在获取邮箱验证码...'}{Style.RESET_ALL}")
                    
                    code_loop = None
                    if account_from_file and cached_client_id and cached_refresh_token:
                        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('register.fetch_code_via_outlook_module') if translator else '尝试使用 read_outlook_inbox.py 获取验证码 (循环中)..'}{Style.RESET_ALL}")
                        fetched_code_loop, new_refresh_token_loop, error_details_loop = fetch_latest_verification_code(
                            username=account_from_file, 
                            current_refresh_token=cached_refresh_token, 
                            client_id=cached_client_id, 
                            proxies=proxies
                        )
                        if error_details_loop:
                            # 邮件过期相关的错误信息已被禁用显示
                            if "邮件验证码可能已过期" not in error_details_loop:
                                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.fetch_code_outlook_error', error=error_details_loop) if translator else f'使用 read_outlook_inbox.py 获取验证码失败 (循环中): {error_details_loop}'}{Style.RESET_ALL}")
                        elif not fetched_code_loop:
                            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.fetch_code_outlook_no_code') if translator else 'read_outlook_inbox.py 未能返回验证码 (循环中)。'}{Style.RESET_ALL}")
                        else:
                            code_loop = fetched_code_loop
                        # 继续更新缓存的token，确保后续调用使用最新token
                        if new_refresh_token_loop and new_refresh_token_loop != cached_refresh_token:
                            cached_refresh_token = new_refresh_token_loop
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Token在第二次调用中再次更新并缓存{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.fetch_code_missing_outlook_data') if translator else '无法从 outlooks.txt 获取必要的 clientId 和 refreshToken 来获取验证码 (循环中)。'}{Style.RESET_ALL}")
                        # code_loop remains None

                    if not code_loop:
                        # 获取验证码失败的日志已被禁用
                        # print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.get_code_failed') if translator else '获取验证码失败'}{Style.RESET_ALL}")
                        return False

                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.code_received', code=code_loop) if translator else f'成功获取验证码: {code_loop}'}{Style.RESET_ALL}")
                    print(f"{Fore.CYAN}{EMOJI['CODE']} {translator.get('register.enter_code') if translator else '正在输入验证码...'}{Style.RESET_ALL}")
                    i = 0
                    for digit_char in code_loop:
                        tab.ele(f"@data-index={i}").input(digit_char)
                        time.sleep(0.2)
                        i += 1
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.code_entered') if translator else '验证码输入完成'}{Style.RESET_ALL}")
                    wait_with_spinner(random.uniform(1, 2), translator.get('register.wait_after_code') if translator else "Waiting after code entry")
                    stage_after_initial_submit_and_xpath_click_completed = True 
                    break 

                time.sleep(0.5)
            except Exception as e:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.code_process_error', error=str(e)) if translator else f'验证码处理过程出错: {str(e)}'}{Style.RESET_ALL}")
                return False

        if not stage_after_initial_submit_and_xpath_click_completed:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.stage_after_xpath_click_timeout') if translator else '点击XPath后阶段超时，未能到达账户设置或处理验证码。'}{Style.RESET_ALL}")
            return False
    
    wait_with_spinner(random.randint(1, 3), translator.get('register.wait_processing') if translator else 'Waiting for system processing')

    print(f"\n{Fore.GREEN}{EMOJI['DONE']} {translator.get('register.register_complete') if translator else '=== 注册账号步骤完成 ==='}{Style.RESET_ALL}")

    return True


class EmailGenerator:
    def __init__(
        self,
        password="".join(
            random.choices(
                "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*",
                k=12,
            )
        ),
    ):
        configInstance = Config()
        configInstance.print_config()
        # 从配置中随机获取一个域名
        self.domain = configInstance.get_random_domain()
        self.default_password = password

    def generate_email(self, length=8):
        """生成随机邮箱地址，数字和字母随机交叉"""
        timestamp_digits = str(int(time.time()))[-6:]
        
        # 随机总长度 (8-12)
        total_length = random.randint(8, 12)
        alpha_length = total_length - len(timestamp_digits)
        
        # 生成字母部分，确保至少有一个字母且首位是字母
        if alpha_length <= 0: # 保证至少有字母
             alpha_length = 1
             total_length = len(timestamp_digits) + 1 # 调整总长

        first_alpha = random.choice(string.ascii_letters)
        remaining_alpha = "".join(random.choices(string.ascii_letters, k=alpha_length - 1))
        alpha_part = first_alpha + remaining_alpha

        # 合并字母和数字
        combined_chars = list(alpha_part + timestamp_digits)
        
        # 随机打乱
        random.shuffle(combined_chars)
        
        # 确保首位是字母
        if not combined_chars[0].isalpha():
            # 找到第一个字母的位置
            first_letter_index = -1
            for i, char in enumerate(combined_chars):
                if char.isalpha():
                    first_letter_index = i
                    break
            # 如果找到了字母 (理论上一定有)，就交换
            if first_letter_index != -1:
                 combined_chars[0], combined_chars[first_letter_index] = combined_chars[first_letter_index], combined_chars[0]

        local_part = "".join(combined_chars)
        
        # 使用 __init__ 中获取的随机域名
        return f"{local_part}@{self.domain}"

    def get_account_info(self):
        """获取完整的账号信息"""
        return {
            "email": self.generate_email(),
            "password": self.default_password,
        }


def get_user_agent():
    """获取user_agent (恢复原始逻辑)"""
    browser_manager_temp = None
    temp_user_data_dir = None # +++ 新增变量 +++
    # 导入 DrissionPage 相关类
    from DrissionPage import ChromiumOptions, Chromium 
    try:
        # 使用JavaScript获取user agent
        # from browser_utils import BrowserManager # 局部导入避免循环 # No longer needed here
        # browser_manager_temp = BrowserManager() # No longer needed here
        
        # +++ 创建临时用户数据目录以确保干净的会话 +++
        temp_user_data_dir = tempfile.mkdtemp()
        logging.info(f"为 User Agent 获取创建临时用户数据目录: {temp_user_data_dir}")

        # 创建一个临时、最小配置的浏览器实例来获取UA
        # 避免在获取UA时应用代理等复杂设置
        temp_co = ChromiumOptions()
        temp_co.headless(True)
        temp_co.set_paths(user_data_path=temp_user_data_dir) # +++ 使用临时目录 +++

        # +++ 随机化视口 +++
        base_width = 1920
        base_height = 1080
        random_width_offset = random.randint(-50, 50)
        random_height_offset = random.randint(-30, 30)
        viewport_width = base_width + random_width_offset
        viewport_height = base_height + random_height_offset
        temp_co.set_argument("--window-size", f'{viewport_width},{viewport_height}')
        logging.info(f"为 User Agent 获取设置随机视口: {viewport_width}x{viewport_height}")
        
        temp_browser = Chromium(temp_co)
        # 确保浏览器已初始化并获取最新标签页
        temp_tab = temp_browser.latest_tab
        if temp_tab:
            user_agent = temp_tab.run_js("return navigator.userAgent")
        else:
            raise Exception("无法获取浏览器标签页")
        temp_browser.quit()
        # 剔除user_agent中的"HeadlessChrome"
        user_agent = user_agent.replace("HeadlessChrome", "Chrome")
        logging.info(f"获取到的 User Agent (已处理): {user_agent}")
        return user_agent
    except Exception as e:
        logging.error(f"获取 user agent 失败: {str(e)}")
        # 返回默认值
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    finally:
        # 确保临时实例被关闭 (已在try块中关闭)
        # if 'temp_browser' in locals() and temp_browser:
        #      try:
        #           temp_browser.quit()
        #      except: pass
        # +++ 清理临时用户数据目录 +++
        if temp_user_data_dir and os.path.exists(temp_user_data_dir):
            try:
                shutil.rmtree(temp_user_data_dir)
                logging.info(f"已清理临时用户数据目录: {temp_user_data_dir}")
            except Exception as e_clean:
                logging.warning(f"清理临时用户数据目录 {temp_user_data_dir} 失败: {e_clean}")
             

def check_cursor_version():
    """检查cursor版本"""
    pkg_path, main_path = patch_cursor_get_machine_id.get_cursor_paths()
    with open(pkg_path, "r", encoding="utf-8") as f:
        version = json.load(f)["version"]
    return patch_cursor_get_machine_id.version_check(version, min_version="0.45.0")





def print_end_message():
    logging.info("\n\n")
    logging.info("=" * 30)
    logging.info("所有操作已完成")
    logging.info("=" * 30)


def monitor_email_for_verification(email_handler, account, max_attempts=30, interval=3):
    """
    监听邮箱是否收到新的验证码邮件
    
    Args:
        email_handler: 邮箱验证处理器
        account: 邮箱账号
        max_attempts: 最大监听次数
        interval: 监听间隔(秒)
    
    Returns:
        str: 如果收到验证码则返回验证码，否则返回None
    """
    import threading
    import sys
    
    logging.info("\n=== 开始监听登录验证邮件 ===")
    logging.info("等待Cursor发送登录验证码...(按回车键结束监听)")
    
    # 标志变量，用于指示是否应该停止监听
    stop_monitoring = False
    verification_code = [None]  # 使用列表存储，以便在线程中修改
    
    # 用于监听用户输入的线程
    def input_thread():
        nonlocal stop_monitoring
        input("\n按回车键停止监听...")
        stop_monitoring = True
    
    # 启动输入监听线程
    t = threading.Thread(target=input_thread)
    t.daemon = True
    t.start()
    
    # 开始监听邮箱
    attempt = 0
    while attempt < max_attempts and not stop_monitoring:
        attempt += 1
        try:
            logging.info(f"监听中... ({attempt}/{max_attempts})")
            
            # 尝试获取验证码
            if not email_handler.imap:
                code, first_id = email_handler._get_latest_mail_code()
                if code:
                    verification_code[0] = code
                    email_handler._cleanup_mail(first_id)
                    logging.info(f"收到验证码: {code}")
                    break
            else:
                code = email_handler._get_mail_code_by_imap(retry=0)
                if code:
                    verification_code[0] = code
                    logging.info(f"收到验证码: {code}")
                    break
                    
        except Exception as e:
            logging.error(f"监听过程发生错误: {str(e)}")
        
        # 如果未收到验证码，等待一段时间后重试
        if not stop_monitoring and attempt < max_attempts:
            time.sleep(interval)
    
    if verification_code[0]:
        logging.info("成功获取登录验证码")
    else:
        logging.info("监听结束，未收到验证码")
    
    return verification_code[0]


def restart_cursor():
    """尝试根据操作系统重启 Cursor 应用"""
    logging.info("尝试重启 Cursor...")
    system = platform.system()
    try:
        # 先获取内部路径
        pkg_path, main_path = get_cursor_paths()
        app_resource_path = os.path.dirname(os.path.dirname(main_path)) # 通常是 'resources/app' 目录
        
        if system == "Darwin": # macOS
            # 从 '.../Cursor.app/Contents/Resources/app' 推断 '.../Cursor.app'
            cursor_app_path = os.path.abspath(os.path.join(app_resource_path, "..", "..", ".."))
            if cursor_app_path.endswith(".app"):
                logging.info(f"检测到 macOS, 使用 'open -a \"{cursor_app_path}\"' 命令")
                os.system(f'open -a "{cursor_app_path}"')
            else:
                 # 如果路径推断失败，回退到通用命令
                 logging.info("检测到 macOS, 路径推断可能失败, 使用通用 'open -a Cursor' 命令")
                 os.system("open -a Cursor")
        elif system == "Windows":
            logging.info("检测到 Windows, 尝试从内部路径推断并启动 Cursor.exe")
            # 从 '...\Programs\Cursor\nesources\app' 推断 '...\Programs\Cursor'
            install_dir_guess = os.path.abspath(os.path.join(app_resource_path, "..", "..")) 
            exe_path = os.path.join(install_dir_guess, "Cursor.exe")
            
            if not os.path.exists(exe_path):
                 # 如果第一次猜测失败，尝试去掉 resources/app 再上一层
                 install_dir_guess_alt = os.path.abspath(os.path.join(app_resource_path, "..", "..", ".."))
                 exe_path_alt = os.path.join(install_dir_guess_alt, "Cursor.exe")
                 if os.path.exists(exe_path_alt):
                      exe_path = exe_path_alt
                 else:
                      # 如果还找不到，记录警告，无法确定路径
                      logging.warning(f"无法根据内部路径 {main_path} 推断出 Cursor.exe 的位置。无法自动重启。")
                      exe_path = None # 明确设置为 None

            if exe_path and os.path.exists(exe_path):
                logging.info(f"找到 Cursor 可执行文件: {exe_path}, 使用 'start' 命令启动")
                os.system(f'start "" "{exe_path}"')
            # else: # 警告已在上面记录
            #    logging.warning(f"无法在预期位置找到 Cursor.exe: {exe_path}，无法自动重启。")

        elif system == "Linux":
            logging.info("检测到 Linux, 尝试从内部路径推断并执行 Cursor")
            # 从 '/opt/Cursor/resources/app' 推断 '/opt/Cursor'
            install_dir_guess = os.path.abspath(os.path.join(app_resource_path, "..", ".."))
            possible_exe_names = ['cursor', 'Cursor', 'cursor-appimage', 'AppRun'] # 添加 AppRun
            exe_path = None
            
            if os.path.isdir(install_dir_guess):
                 for name in possible_exe_names:
                      potential_path = os.path.join(install_dir_guess, name)
                      if os.path.exists(potential_path) and os.access(potential_path, os.X_OK):
                           exe_path = potential_path
                           break
            
            # 如果在推断的安装目录没找到，尝试在系统 PATH 中查找
            if not exe_path:
                 import shutil
                 logging.info("在推断目录未找到，尝试在系统 PATH 中查找...")
                 for name in possible_exe_names:
                      found_path = shutil.which(name)
                      if found_path:
                           exe_path = found_path
                           break
                           
            if exe_path:
                logging.info(f"找到 Cursor 可执行文件: {exe_path}, 尝试在后台执行")
                os.system(f'nohup "{exe_path}" > /dev/null 2>&1 &')
            else:
                logging.warning("无法根据内部路径或系统 PATH 找到 Cursor 可执行文件，无法自动重启。")
        else:
            logging.warning(f"不支持的操作系统: {system}，无法自动重启。")
        logging.info("重启命令已发送 (如果找到应用)。")
    except OSError as e:
        logging.error(f"获取 Cursor 路径时出错: {str(e)}，无法自动重启。")
    except Exception as e:
        logging.error(f"重启 Cursor 时出错: {str(e)}")


def run_git_command(command, cwd="."):
    """执行 Git 命令并打印输出"""
    try:
        print(f"{Fore.BLUE}ℹ️  执行 Git 命令: {command}{Style.RESET_ALL}")
        # 使用 subprocess.run 来执行命令并捕获输出
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True, cwd=cwd)
        if result.stdout:
            print(f"{Fore.GREEN}✅ Git 输出:\n{result.stdout}{Style.RESET_ALL}")
        if result.stderr:
             # Git 经常在 stderr 上输出非错误信息（例如，关于分支跟踪），所以用 INFO 级别打印
             print(f"{Fore.YELLOW}ℹ️  Git 提示:\n{result.stderr}{Style.RESET_ALL}")
        return True
    except subprocess.CalledProcessError as e:
        # 如果命令执行失败 (非零退出码)
        print(f"{Fore.RED}❌ Git 命令执行失败: {command}{Style.RESET_ALL}")
        print(f"{Fore.RED}   错误码: {e.returncode}{Style.RESET_ALL}")
        if e.stdout:
            print(f"{Fore.RED}   标准输出:\n{e.stdout}{Style.RESET_ALL}")
        if e.stderr:
            print(f"{Fore.RED}   错误输出:\n{e.stderr}{Style.RESET_ALL}")
        return False
    except Exception as e:
        # 其他可能的错误 (例如，找不到 git)
        print(f"{Fore.RED}❌ 执行 Git 命令时发生异常: {str(e)}{Style.RESET_ALL}")
        return False

def git_sync_accounts(commit_message):
    """同步 accounts.json 文件到 Git 仓库"""
    print(f"\n{Fore.CYAN}🔄 开始同步账号文件到 Git...{Style.RESET_ALL}")
    sync_successful = True
    
    # 1. 添加文件
    if not run_git_command(f"git add {ACCOUNTS_JSON_FILE}"):
        sync_successful = False

    # 2. 提交更改 (仅当 add 成功时)
    if sync_successful:
        # 使用 f-string 安全地插入 commit message
        commit_cmd = f'git commit -m "{commit_message}"' 
        # 检查是否有更改需要提交
        status_result = subprocess.run("git status --porcelain", shell=True, capture_output=True, text=True)
        if ACCOUNTS_JSON_FILE in status_result.stdout:
            if not run_git_command(commit_cmd):
                sync_successful = False
        else:
             print(f"{Fore.YELLOW}ℹ️  {ACCOUNTS_JSON_FILE} 没有检测到更改，无需提交。{Style.RESET_ALL}")
             # 不需要提交，但也不算失败
             pass # sync_successful 保持 True

    # 3. 推送更改 (仅当 commit 成功或无需 commit 时)
    if sync_successful:
         if not run_git_command("git push"):
              sync_successful = False
              
    if sync_successful:
         print(f"{Fore.GREEN}✅ Git 同步成功！{Style.RESET_ALL}")
    else:
         print(f"{Fore.RED}❌ Git 同步失败。请检查上面的错误信息。{Style.RESET_ALL}")
         
    return sync_successful

def git_sync_specific_file(file_to_sync, commit_message, translator=None):
    """同步指定文件到 Git 仓库"""
    print(f"\n{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('git.sync.start_specific', file=file_to_sync) if translator else f'开始同步文件 {file_to_sync} 到 Git...'}{Style.RESET_ALL}")
    sync_successful = True
    
    # 1. 添加文件
    # Quote file_to_sync to handle paths with spaces, though not expected for OUTLOOKS_FILE_PATH
    if not run_git_command(f"git add \"{file_to_sync}\""):
        sync_successful = False

    # 2. 提交更改 (仅当 add 成功时)
    if sync_successful:
        commit_cmd = f'git commit -m "{commit_message}"'
        # Check status of the specific file.
        # Pass file_to_sync to Popen correctly, ensuring it's part of the command if shell=True is used, or as a separate arg.
        # Using f-string with shell=True is okay here as file_to_sync is internally controlled.
        status_result_process = subprocess.run(f"git status --porcelain \"{file_to_sync}\"", shell=True, capture_output=True, text=True)
        
        if status_result_process.stdout.strip(): # If there's any output, means file has changes listed by `git status --porcelain`
            if not run_git_command(commit_cmd):
                sync_successful = False
        else:
             print(f"{Fore.YELLOW}{EMOJI['INFO']} {translator.get('git.sync.no_changes_specific', file=file_to_sync) if translator else f'{file_to_sync} 没有检测到需要提交的更改。'}{Style.RESET_ALL}")
             # No changes to commit for this specific file, but the overall sync process up to add might be considered successful.
             # If 'add' succeeded but there was nothing to stage for this file, it's not a failure.
             pass

    # 3. 推送更改 (仅当 add 和 commit (if needed) 成功时)
    if sync_successful:
         if not run_git_command("git push"):
              sync_successful = False
              
    if sync_successful:
         print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('git.sync.success_specific', file=file_to_sync) if translator else f'Git 同步成功 ({file_to_sync})！'}{Style.RESET_ALL}")
    else:
         print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('git.sync.fail_specific', file=file_to_sync) if translator else f'Git 同步失败 ({file_to_sync})。请检查上面的错误信息。'}{Style.RESET_ALL}")
         
    return sync_successful




# 新增：清理Cursor缓存的函数
def clean_cursor_cache(translator=None):
    """清理Cursor的缓存文件和SQLite数据库条目"""
    print(f"\n{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.cleaning_started') if translator else '开始清理 Cursor 缓存...'}{Style.RESET_ALL}")
    
    system = platform.system()
    cursor_config_path = None

    try:
        if system == "Windows":
            base_path_str = os.getenv("APPDATA")
            if not base_path_str:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.no_appdata') if translator else '错误: 未找到 APPDATA 环境变量。'}{Style.RESET_ALL}")
                return
            cursor_config_path = pathlib.Path(base_path_str) / "Cursor"
        elif system == "Linux":
            base_path_str = os.getenv("HOME")
            if not base_path_str:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.no_home') if translator else '错误: 未找到 HOME 环境变量。'}{Style.RESET_ALL}")
                return
            cursor_config_path = pathlib.Path(base_path_str) / ".config" / "Cursor"
        elif system == "Darwin":  # macOS
            base_path_str = os.getenv("HOME")
            if not base_path_str:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.no_home') if translator else '错误: 未找到 HOME 环境变量。'}{Style.RESET_ALL}")
                return
            cursor_config_path = pathlib.Path(base_path_str) / "Library" / "Application Support" / "Cursor"
        else:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.unsupported_os', os=system) if translator else f'错误: 不支持的操作系统进行缓存清理: {system}'}{Style.RESET_ALL}")
            return

        if not cursor_config_path or not cursor_config_path.exists():
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.config_not_found', path=str(cursor_config_path)) if translator else f'错误: Cursor 配置目录未找到: {cursor_config_path}'}{Style.RESET_ALL}")
            return

        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.config_path_identified', path=str(cursor_config_path)) if translator else f'Cursor 配置目录: {cursor_config_path}'}{Style.RESET_ALL}")

        # 1. SQLite数据库清理
        print(f"\n{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.starting') if translator else '--- 开始 SQLite 数据库清理 ---'}{Style.RESET_ALL}")
        identified_sqlite_db = None
        alternative_sqlite_db = None
        try:
            user_dir = cursor_config_path / "User"
            if user_dir.exists():
                glob_profile_dirs = list(user_dir.glob("glob*"))
                if glob_profile_dirs:
                    target_glob_dir = glob_profile_dirs[0]
                    db_files_in_glob_dir = [f for f in target_glob_dir.glob("*b") if f.is_file()]
                    if db_files_in_glob_dir:
                        identified_sqlite_db = db_files_in_glob_dir[0]
                        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.identified_specific', db=str(identified_sqlite_db)) if translator else f'找到特定 SQLite DB (glob*/*b): {identified_sqlite_db}'}{Style.RESET_ALL}")
            
            local_storage_dir = cursor_config_path / "User" / "Default" / "Local Storage"
            if local_storage_dir.exists():
                local_storage_dbs = list(local_storage_dir.glob("*.localstorage"))
                if not local_storage_dbs:
                    local_storage_dbs = list(local_storage_dir.glob("*.db"))
                if local_storage_dbs:
                    alternative_sqlite_db = local_storage_dbs[0]
                    if not identified_sqlite_db:
                        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.identified_standard', db=str(alternative_sqlite_db)) if translator else f'找到标准 LocalStorage DB: {alternative_sqlite_db}'}{Style.RESET_ALL}")
                    elif identified_sqlite_db != alternative_sqlite_db:
                         print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.identified_additional', db=str(alternative_sqlite_db)) if translator else f'同时找到标准 LocalStorage DB: {alternative_sqlite_db}'}{Style.RESET_ALL}")

            databases_to_process = []
            if identified_sqlite_db and identified_sqlite_db.exists():
                databases_to_process.append(identified_sqlite_db)
            if alternative_sqlite_db and alternative_sqlite_db.exists() and alternative_sqlite_db not in databases_to_process:
                databases_to_process.append(alternative_sqlite_db)

            if not databases_to_process:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cache.sqlite.not_found') if translator else '未找到可清理的 SQLite 数据库文件。'}{Style.RESET_ALL}")
            
            for db_path in databases_to_process:
                print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.processing_db', db=str(db_path)) if translator else f'正在处理 SQLite DB: {db_path}'}{Style.RESET_ALL}")
                conn = None
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    sqlite_globs_to_delete = sorted(list(set([
                        "cursor*/cache*", "cursor*/*onfig", "cursor*/*Token", "cursor*/*ID",
                        "cursor*/*session*", "cursor*/*auth*",
                        "cache*", "*Token", "*ID", "*session*", "*auth*"
                    ])))
                    deleted_rows_total_for_db = 0
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ItemTable';")
                    if cursor.fetchone():
                        for glob_pattern in sqlite_globs_to_delete:
                            try:
                                cursor.execute("DELETE FROM ItemTable WHERE key GLOB ?", (glob_pattern,))
                                deleted_rows_total_for_db += cursor.rowcount
                            except sqlite3.Error as db_err_glob:
                                print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cache.sqlite.error_glob', glob=glob_pattern, db=str(db_path), error=str(db_err_glob)) if translator else f'SQLite glob清理错误 ({glob_pattern}) in {db_path}: {db_err_glob}'}{Style.RESET_ALL}")
                        conn.commit()
                        if deleted_rows_total_for_db > 0:
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.sqlite.cleaned_rows', count=deleted_rows_total_for_db, db=str(db_path)) if translator else f'从 {db_path} 清理了 {deleted_rows_total_for_db} 行数据。'}{Style.RESET_ALL}")
                        else:
                            print(f"{Fore.BLUE}{EMOJI['INFO']} {translator.get('cache.sqlite.no_rows_cleaned', db=str(db_path)) if translator else f'{db_path} 中未找到匹配数据行进行清理。'}{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cache.sqlite.no_itemtable', db=str(db_path)) if translator else f'在 {db_path} 中未找到 ItemTable 表。'}{Style.RESET_ALL}")
                except sqlite3.Error as e_db_conn:
                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.sqlite.error_connect', db=str(db_path), error=str(e_db_conn)) if translator else f'SQLite 连接/处理错误 {db_path}: {e_db_conn}'}{Style.RESET_ALL}")
                finally:
                    if conn:
                        conn.close()
        except Exception as e_find_db:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.sqlite.error_find', error=str(e_find_db)) if translator else f'SQLite DB 搜索过程中出错: {e_find_db}'}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.sqlite.finished') if translator else '--- SQLite 数据库清理完成 ---'}{Style.RESET_ALL}")

        # 2. 文件和目录清理
        print(f"\n{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.filedir.starting') if translator else '--- 开始文件及目录清理 ---'}{Style.RESET_ALL}")
        paths_to_clean = [
            cursor_config_path / "Cache",
            cursor_config_path / "Code Cache",
            cursor_config_path / "GPUCache",
            cursor_config_path / "Service Worker" / "CacheStorage",
            cursor_config_path / "Application Cache",
            cursor_config_path / "Local Storage" / "leveldb", # LevelDB files
            cursor_config_path / "logs",
            cursor_config_path / "Crashpad",
        ]
        user_data_dir = cursor_config_path / "User"
        if user_data_dir.exists():
            profile_dirs = [d for d in user_data_dir.iterdir() if d.is_dir() and d.name.lower() != 'globus'] # Exclude globus like dirs
            for profile_dir in profile_dirs:
                paths_to_clean.extend([
                    profile_dir / "Cache",
                    profile_dir / "Code Cache",
                    profile_dir / "GPUCache",
                    profile_dir / "Service Worker" / "CacheStorage",
                ])
        paths_to_clean = sorted(list(set(paths_to_clean)))

        for path_item in paths_to_clean:
            try:
                if path_item.exists():
                    if path_item.is_dir():
                        shutil.rmtree(path_item)
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.filedir.removed_dir', dir=str(path_item)) if translator else f'已删除目录: {path_item}'}{Style.RESET_ALL}")
                    elif path_item.is_file():
                        path_item.unlink()
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.filedir.removed_file', file=str(path_item)) if translator else f'已删除文件: {path_item}'}{Style.RESET_ALL}")
            except OSError as e_remove:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.filedir.error_remove', path=str(path_item), error=str(e_remove)) if translator else f'移除 {path_item} 时出错: {e_remove}'}{Style.RESET_ALL}")
        
        dirs_to_glob_cache_files = [cursor_config_path]
        if user_data_dir.exists():
            dirs_to_glob_cache_files.extend([d for d in user_data_dir.iterdir() if d.is_dir() and d.name.lower() != 'globus'])

        for directory in dirs_to_glob_cache_files:
            if directory.exists():
                try:
                    for cache_item in directory.glob("cache*"):
                        if cache_item.is_file():
                            cache_item.unlink()
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.filedir.removed_cache_file', file=str(cache_item)) if translator else f'已删除缓存文件: {cache_item}'}{Style.RESET_ALL}")
                        elif cache_item.is_dir():
                            shutil.rmtree(cache_item)
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.filedir.removed_cache_dir', dir=str(cache_item)) if translator else f'已删除缓存目录: {cache_item}'}{Style.RESET_ALL}")
                except Exception as e_glob_remove:
                     print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.filedir.error_remove_glob', dir=str(directory), error=str(e_glob_remove)) if translator else f'在 {directory} 中移除 cache* 时出错: {e_glob_remove}'}{Style.RESET_ALL}")

        print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cache.filedir.finished') if translator else '--- 文件及目录清理完成 ---'}{Style.RESET_ALL}")
    
    except Exception as e_main:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('cache.error.main', error=str(e_main)) if translator else f'缓存清理过程中发生主错误: {e_main}'}{Style.RESET_ALL}")
        # import traceback
        # traceback.print_exc() # For detailed debugging if needed
    finally:
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cache.cleaning_finished') if translator else 'Cursor 缓存清理流程结束。'}{Style.RESET_ALL}")


# 在文件顶部导入部分之后添加获取代理的函数
def get_new_proxy():
    """获取新的代理，直接使用请求库调用JuliangIP接口"""
    try:
        # 获取代理URL（聚合IP接口）
        proxy_url = os.getenv("PROXY_URL")
        if proxy_url and "juliangip.com" in proxy_url:
            logging.info("尝试获取新的聚合IP代理...")
            print(f"{Fore.CYAN}{EMOJI['INFO']} 尝试获取新的聚合IP代理...{Style.RESET_ALL}")
            response = requests.get(proxy_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200 and data.get("data") and data["data"].get("proxy_list"):
                    proxy_info = data["data"]["proxy_list"][0]
                    proxy_address = proxy_info.split(',')[0]  # 提取 IP:端口 部分
                    logging.info(f"成功获取新的动态代理: {proxy_address}")
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功获取新的动态代理: {proxy_address}{Style.RESET_ALL}")
                    
                    # 确保代理地址有正确的格式
                    if not proxy_address.startswith(('http://', 'https://')):
                        proxy_address = f"http://{proxy_address}"
                    
                    # 创建代理字典
                    proxy_dict = {
                        'http': proxy_address,
                        'https': proxy_address
                    }
                    return proxy_dict
                else:
                    logging.warning(f"从聚合IP获取代理失败，响应格式不符合预期: {data}")
                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从聚合IP获取代理失败，响应格式不符合预期{Style.RESET_ALL}")
            else:
                logging.warning(f"从聚合IP获取代理失败，HTTP状态码: {response.status_code}")
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从聚合IP获取代理失败，HTTP状态码: {response.status_code}{Style.RESET_ALL}")
        
        # 尝试使用静态代理
        static_proxy = os.getenv("BROWSER_PROXY")
        if static_proxy:
            logging.info(f"使用配置的静态代理: {static_proxy}")
            print(f"{Fore.CYAN}{EMOJI['INFO']} 使用配置的静态代理: {static_proxy}{Style.RESET_ALL}")
            
            # 确保代理地址有正确的格式
            if not static_proxy.startswith(('http://', 'https://')):
                static_proxy = f"http://{static_proxy}"
                
            proxy_dict = {
                'http': static_proxy,
                'https': static_proxy
            }
            return proxy_dict
        
        logging.warning("无法获取新代理，将继续使用当前代理")
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法获取新代理，将继续使用当前代理{Style.RESET_ALL}")
        return None
    except Exception as e:
        logging.error(f"获取新代理时出错: {str(e)}")
        print(f"{Fore.RED}{EMOJI['ERROR']} 获取新代理时出错: {str(e)}{Style.RESET_ALL}")
        return None


def get_new_proxy_from_juliang_only(translator=None): # Added translator for consistency
    """获取新的动态代理，仅尝试从JuliangIP接口获取"""
    try:
        proxy_url = os.getenv("PROXY_URL")
        if proxy_url and "juliangip.com" in proxy_url:
            logging.info("Attempting to fetch new proxy exclusively from JuliangIP...")
            # Use print for user visibility, as in get_new_proxy
            print(f"{Fore.CYAN}{EMOJI['INFO']} 尝试从 JuliangIP 获取新代理...{Style.RESET_ALL}")
            response = requests.get(proxy_url, timeout=10) # Standard timeout
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200 and data.get("data") and data["data"].get("proxy_list"):
                    proxy_info = data["data"]["proxy_list"][0]
                    proxy_address = proxy_info.split(',')[0]
                    logging.info(f"Successfully fetched new dynamic proxy from JuliangIP: {proxy_address}")
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功从 JuliangIP 获取动态代理: {proxy_address}{Style.RESET_ALL}")
                    
                    if not proxy_address.startswith(('http://', 'https://')):
                        proxy_address = f"http://{proxy_address}"
                    
                    return {'http': proxy_address, 'https': proxy_address}
                else:
                    # Log detailed error from Juliang response
                    error_msg_juliang = data.get('msg', 'Unknown error')
                    logging.warning(f"Failed to get proxy from JuliangIP, API response: code={data.get('code')}, msg='{error_msg_juliang}', data={data.get('data')}")
                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从 JuliangIP 获取代理失败: {error_msg_juliang} (详情见日志){Style.RESET_ALL}")
            else:
                logging.warning(f"Failed to get proxy from JuliangIP, HTTP status: {response.status_code}, Response: {response.text[:200]}") # Log part of response text
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从 JuliangIP 获取代理失败，HTTP 状态码: {response.status_code}{Style.RESET_ALL}")
        else:
            # This case should ideally be caught by the caller checking is_juliang_configured
            logging.info("JuliangIP (PROXY_URL) is not configured or not a JuliangIP URL for get_new_proxy_from_juliang_only.")
        
        return None # Explicitly return None if JuliangIP fails or not applicable
    except requests.exceptions.RequestException as e: # More specific exception
        logging.error(f"Error fetching new proxy from JuliangIP: {str(e)}")
        print(f"{Fore.RED}{EMOJI['ERROR']} 从 JuliangIP 获取新代理时出错: {str(e)}{Style.RESET_ALL}")
        return None
    except Exception as e_general: # Catch other potential errors like JSONDecodeError
        logging.error(f"Unexpected error fetching new proxy from JuliangIP: {str(e_general)}")
        print(f"{Fore.RED}{EMOJI['ERROR']} 从 JuliangIP 获取新代理时发生意外错误: {str(e_general)}{Style.RESET_ALL}")
        return None


# 添加获取会话信息的函数
def get_session_info(bearer_token, proxies=None):
    """
    获取会话信息
    
    Args:
        bearer_token: 认证token
        proxies: 代理设置
        
    Returns:
        dict: 包含会话ID、过期时间和剩余天数的字典，获取失败则返回None
    """
    print(f"{Fore.CYAN}{EMOJI['INFO']} 开始获取会话信息...{Style.RESET_ALL}")
    try:
        # 打印请求信息用于调试
        request_url = "https://www.cursor.com/api/auth/sessions"
        request_headers = {
            "Accept": "*/*",
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.210 Safari/537.36",
            "Cookie": f"WorkosCursorSessionToken={bearer_token}"
        }
        # print(f"{Fore.CYAN}{EMOJI['INFO']} 调试信息 - 请求URL: {request_url}{Style.RESET_ALL}")
        # print(f"{Fore.CYAN}{EMOJI['INFO']} 调试信息 - 请求头: {json.dumps(request_headers, indent=2, ensure_ascii=False)}{Style.RESET_ALL}")
        # print(f"{Fore.CYAN}{EMOJI['INFO']} 调试信息 - 代理设置: {proxies}{Style.RESET_ALL}")
        
        # 执行原始请求
        response = requests.get(
            request_url,
            headers=request_headers,
            timeout=10,
            proxies=proxies
        )
        
        # 打印响应信息用于调试
        #print(f"{Fore.CYAN}{EMOJI['INFO']} 调试信息 - 响应状态码: {response.status_code}{Style.RESET_ALL}")
        
        if response.status_code == 200:
            data = response.json()
            # 打印响应数据
            #print(f"{Fore.CYAN}{EMOJI['INFO']} 调试信息 - 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}{Style.RESET_ALL}")
            sessions = data.get("sessions", [])
            
            # 过滤出类型为SESSION_TYPE_CLIENT的会话
            client_sessions = [s for s in sessions if s.get("type") == "SESSION_TYPE_CLIENT"]
            
            if not client_sessions:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 未找到类型为CLIENT的会话{Style.RESET_ALL}")
                return None
                
            # 按创建时间排序，找到最新的会话
            client_sessions.sort(key=lambda x: x.get("createdAt", ""), reverse=True)
            latest_session = client_sessions[0]
            
            session_id = latest_session.get("sessionId")
            expires_at = latest_session.get("expiresAt")
            
            # 计算剩余天数
            if expires_at:
                try:
                    from datetime import datetime
                    current_time = datetime.now()
                    
                    # 解析日期格式 2025-07-15T18:16:03.000Z
                    try:
                        expire_time = datetime.strptime(expires_at, "%Y-%m-%dT%H:%M:%S.%fZ")
                        
                        # 格式化成 2025-07-15 18:16:03 的格式
                        formatted_expire_time = expire_time.strftime("%Y-%m-%d %H:%M:%S")
                        
                        days_left = (expire_time - current_time).days
                        
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 会话信息获取成功{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}  ➡️ 会话ID: {session_id}{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}  ➡️ 到期时间: {formatted_expire_time}{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}  ➡️ 剩余天数: {days_left}{Style.RESET_ALL}")
                        
                        return {
                            "session_id": session_id,
                            "expire_time": formatted_expire_time,
                            "days_left": days_left
                        }
                    except ValueError as e:
                        print(f"{Fore.RED}{EMOJI['ERROR']} 无法解析日期格式: {expires_at}{Style.RESET_ALL}")
                        return None
                except Exception as date_error:
                    print(f"{Fore.RED}{EMOJI['ERROR']} 计算日期差异时出错: {str(date_error)}{Style.RESET_ALL}")
                    return None
            else:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 会话信息不包含过期时间{Style.RESET_ALL}")
                return None
        else:
            print(f"{Fore.RED}{EMOJI['ERROR']} 获取会话信息失败，状态码: {response.status_code}{Style.RESET_ALL}")
            return None
            
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 获取会话信息出错: {str(e)}{Style.RESET_ALL}")
        import traceback
        print(f"{Fore.RED}{traceback.format_exc()}{Style.RESET_ALL}")
        return None

def monitor_outlook_verification_code(email, refresh_token, client_id, max_attempts=60, interval=5, translator=None):
    """
    持续监听Outlook邮箱的验证码邮件 - 使用共享的监听模块（持续监听模式）
    
    Args:
        email: 邮箱地址
        refresh_token: OAuth2刷新令牌
        client_id: OAuth2客户端ID
        max_attempts: 最大监听次数（持续模式下无效）
        interval: 监听间隔(秒)
        translator: 翻译器对象
    """
    try:
        # 导入共享的邮箱监听模块
        from shared_email_monitor import SharedEmailMonitor
        
        # 创建监听器实例
        monitor = SharedEmailMonitor(
            username=email,
            client_id=client_id,
            refresh_token=refresh_token,
            proxies=None  # 不使用代理
        )
        
        # 开始持续监听（continuous_mode=True）
        return monitor.monitor_for_verification_code(
            max_attempts=max_attempts, 
            interval=interval, 
            continuous_mode=True  # 启用持续监听模式
        )
        
    except ImportError:
        print(f"{Fore.RED}{EMOJI['ERROR']} 无法导入共享邮箱监听模块{Style.RESET_ALL}")
        return None
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 监听过程中出错: {str(e)}{Style.RESET_ALL}")
        return None


def get_outlook_verification_code(translator=None):
    """
    获取指定outlook邮箱验证码功能
    通过77kami API查询最近订单中是否包含指定邮箱号码
    """
    print(f"\n{Fore.CYAN}{EMOJI['START']} 获取指定Outlook邮箱验证码{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")

    # 让用户输入邮箱号码
    email_to_search = input(f"{Fore.YELLOW}请输入要查询验证码的邮箱地址 (直接回车使用第一个Outlook订单): {Style.RESET_ALL}").strip()

    # 如果用户直接按回车，设置标志使用第一个找到的订单
    use_first_order = False
    if not email_to_search:
        use_first_order = True
        print(f"{Fore.CYAN}{EMOJI['INFO']} 将自动使用第一个找到的Outlook订单{Style.RESET_ALL}")
    else:
        # 验证邮箱格式
        if "@" not in email_to_search or "." not in email_to_search:
            print(f"{Fore.RED}{EMOJI['ERROR']} 邮箱地址格式不正确{Style.RESET_ALL}")
            return
        print(f"{Fore.CYAN}{EMOJI['SEARCH']} 正在查询邮箱 {email_to_search} 的验证码...{Style.RESET_ALL}")

    # 77kami API配置
    api_key = "e25c40f5-96d8-46ae-8f2c-58492788a3a6"
    headers = {
        "Authorization": api_key,
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    }

    try:
        # 1. 获取最近的订单列表
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在获取最近订单列表...{Style.RESET_ALL}")
        orders_url = "https://www.777kami.com/api/v1/orders?page=1&pageSize=50&oid=&pid=&pay_id=&data=&exact=&start_time=&end_time=&status="

        response = requests.get(orders_url, headers=headers, timeout=30)

        if response.status_code != 200:
            print(f"{Fore.RED}{EMOJI['ERROR']} 获取订单列表失败，状态码: {response.status_code}{Style.RESET_ALL}")
            return

        orders_data = response.json()
        if orders_data.get('code') != 200:
            print(f"{Fore.RED}{EMOJI['ERROR']} API返回错误: {orders_data.get('msg', 'Unknown error')}{Style.RESET_ALL}")
            return

        orders = orders_data.get('data', {}).get('data', [])
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功获取 {len(orders)} 个订单{Style.RESET_ALL}")

        # 2. 遍历订单，查找包含指定邮箱的订单或使用第一个Outlook订单
        for order in orders:
            oid = order.get('oid')
            order_name = order.get('name', '')

            # 只查询Outlook相关的订单
            if 'outlook' in order_name.lower():
                print(f"{Fore.CYAN}{EMOJI['SEARCH']} 检查订单 {oid}: {order_name}{Style.RESET_ALL}")

                # 获取订单详情
                detail_url = f"https://www.777kami.com/api/v1/order/data?oid={oid}"
                detail_response = requests.get(detail_url, headers=headers, timeout=30)

                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    if detail_data.get('code') == 200:
                        card_data = detail_data.get('data', '')

                        # 如果用户选择使用第一个订单，或者卡密数据包含指定邮箱
                        should_process_order = use_first_order or (email_to_search and email_to_search in card_data)
                        
                        if should_process_order:
                            if use_first_order:
                                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 使用第一个Outlook订单: {oid}{Style.RESET_ALL}")
                                # 从卡密数据中提取邮箱地址用于显示
                                if '----' in card_data:
                                    parts = card_data.strip().split('----')
                                    if len(parts) >= 1:
                                        email_from_order = parts[0]
                                        print(f"{Fore.CYAN}{EMOJI['INFO']} 订单中的邮箱: {email_from_order}{Style.RESET_ALL}")
                            else:
                                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 找到匹配订单: {oid}{Style.RESET_ALL}")
                            
                            # 立即处理找到的订单，不再继续遍历
                            display_email = email_to_search if email_to_search else "第一个Outlook订单"
                            print(f"\n{Fore.GREEN}{EMOJI['SUCCESS']} 找到{display_email}的订单:{Style.RESET_ALL}")
                            print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
                            
                            print(f"\n{Fore.YELLOW}订单详情:{Style.RESET_ALL}")
                            print(f"{Fore.CYAN}  订单ID: {oid}{Style.RESET_ALL}")
                            print(f"{Fore.CYAN}  订单名称: {order_name}{Style.RESET_ALL}")
                            print(f"{Fore.CYAN}  创建时间: {order.get('createdAt', '')}{Style.RESET_ALL}")
                            print(f"{Fore.CYAN}  更新时间: {order.get('updatedAt', '')}{Style.RESET_ALL}")
                            
                            # 解析卡密信息
                            card_data_stripped = card_data.strip()
                            print(f"{Fore.GREEN}  原始卡密: {card_data_stripped}{Style.RESET_ALL}")
                            
                            if '----' in card_data_stripped:
                                parts = card_data_stripped.split('----')
                                
                                if len(parts) >= 4:
                                    # 包含token的格式：邮箱----密码----clientId----授权令牌
                                    email = parts[0]
                                    password = parts[1]
                                    client_id = parts[2]
                                    refresh_token = parts[3]

                                    print(f"{Fore.MAGENTA}  📧 邮箱: {email}{Style.RESET_ALL}")
                                    print(f"{Fore.MAGENTA}  🔑 密码: {password}{Style.RESET_ALL}")
                                    print(f"{Fore.MAGENTA}  🆔 Client ID: {client_id}{Style.RESET_ALL}")
                                    print(f"{Fore.MAGENTA}  🔐 授权令牌: {refresh_token[:50]}...{Style.RESET_ALL}")

                                    # 对于所有支持OAuth2的邮箱，直接启动持续监听
                                    print(f"\n{Fore.CYAN}{EMOJI['MAIL']} 检测到支持OAuth2的邮箱，开始持续监听验证码...{Style.RESET_ALL}")

                                    # 直接启动持续监听，不进行单独的获取操作
                                    monitor_outlook_verification_code(
                                        email, refresh_token, client_id,
                                        max_attempts=60, interval=5, translator=translator
                                    )
                                    return

                                elif len(parts) == 2:
                                    # 只有账号密码的格式：邮箱----密码
                                    email = parts[0]
                                    password = parts[1]

                                    print(f"{Fore.MAGENTA}  📧 邮箱: {email}{Style.RESET_ALL}")
                                    print(f"{Fore.MAGENTA}  🔑 密码: {password}{Style.RESET_ALL}")

                                    print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 此邮箱不支持OAuth2自动获取验证码{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}{EMOJI['INFO']} 请手动登录邮箱查看验证码:{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}  邮箱: {email}{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}  密码: {password}{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}  登录地址: https://outlook.live.com{Style.RESET_ALL}")
                                    
                                    # 对于不支持OAuth2的邮箱，提供手动验证选项
                                    print(f"\n{Fore.BLUE}{EMOJI['INFO']} 此邮箱只有账号密码信息，无法自动监听验证码{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}{EMOJI['INFO']} 请按以下步骤手动获取验证码:{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}  1. 使用上述账号密码登录邮箱{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}  2. 查看收件箱中的验证码邮件{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}  3. 复制验证码使用{Style.RESET_ALL}")
                                    
                                    return

                                else:
                                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 卡密格式不识别，部分数量: {len(parts)}{Style.RESET_ALL}")
                            
                            # 找到匹配的订单后立即返回，不再继续遍历
                            return

                # 短暂延迟避免请求过快
                time.sleep(0.5)

        # 3. 如果循环结束都没有找到匹配的订单，显示未找到的消息
        if use_first_order:
            print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 未找到任何Outlook订单{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 未找到包含邮箱 {email_to_search} 的订单{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['INFO']} 建议检查:{Style.RESET_ALL}")
        print(f"{Fore.CYAN}  1. 邮箱地址是否正确{Style.RESET_ALL}")
        print(f"{Fore.CYAN}  2. 是否在最近50个订单中{Style.RESET_ALL}")
        print(f"{Fore.CYAN}  3. 订单是否为Outlook类型{Style.RESET_ALL}")

    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 网络请求失败: {str(e)}{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 处理过程中出错: {str(e)}{Style.RESET_ALL}")
        import traceback
        print(f"{Fore.RED}{traceback.format_exc()}{Style.RESET_ALL}")


def batch_refresh_tokens(translator=None):
    """
    批量刷新Token功能，读取accounts.json文件中的所有账号token，
    调用API获取长效token并更新文件
    """
    print(f"\n{Fore.CYAN}{EMOJI['START']} 开始批量刷新Token...{Style.RESET_ALL}")
    
    # 读取账号文件
    accounts = load_accounts_from_json(translator)
    if not accounts:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 账号文件为空或不存在，无法进行批量刷新。{Style.RESET_ALL}")
        return
    
    # 过滤出有token的账号
    valid_accounts = [acc for acc in accounts if acc.get('token')]
    if not valid_accounts:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 没有找到包含token的账号，无法进行批量刷新。{Style.RESET_ALL}")
        return
    
    print(f"{Fore.CYAN}{EMOJI['INFO']} 找到 {len(valid_accounts)} 个包含token的账号，开始批量刷新...{Style.RESET_ALL}")
    
    success_count = 0
    failed_count = 0
    
    for i, account in enumerate(valid_accounts):
        email = account.get('email', 'Unknown')
        current_token = account.get('token')
        
        print(f"\n{Fore.CYAN}{EMOJI['UPDATE']} 正在处理账号 {i+1}/{len(valid_accounts)}: {email}{Style.RESET_ALL}")
        
        # 获取新的代理
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在获取新的代理...{Style.RESET_ALL}")
        proxy_dict = get_new_proxy()
        if proxy_dict:
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功获取代理: {proxy_dict.get('http')}{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 未获取到代理，将使用直连方式{Style.RESET_ALL}")
        
        # 调用API获取长效token
        try:
            print(f"{Fore.CYAN}{EMOJI['WAIT']} 正在调用API获取长效token...{Style.RESET_ALL}")
            
            # 准备请求数据
            api_url = "https://cursor.meteormail.me/api/get_long_token"
            headers = {
                "Accept": "*/*",
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.210 Safari/537.36"
            }
            data = {"token": current_token}
            
            # 发送请求
            response = requests.post(
                api_url,
                headers=headers,
                json=data,
                proxies=proxy_dict,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    # API调用成功
                    token_data = result.get('data', {})
                    new_token = token_data.get('token')
                    expires_at = token_data.get('expires_at')
                    expires_at_human = token_data.get('expires_at_human')
                    
                    if new_token:
                        # 更新账号信息
                        for acc in accounts:
                            if acc.get('email') == email:
                                acc['token'] = new_token
                                acc['token_type'] = 'long_token'
                                acc['expire_time'] = expires_at_human
                                acc['saved_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                
                                # 计算剩余天数
                                if expires_at:
                                    try:
                                        expire_timestamp = int(expires_at)
                                        current_timestamp = int(time.time())
                                        days_left = (expire_timestamp - current_timestamp) // (24 * 3600)
                                        acc['days_left'] = days_left
                                    except:
                                        acc['days_left'] = None
                                break
                        
                        success_count += 1
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 账号 {email} Token刷新成功{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}  ➡️ 新Token: {new_token[:50]}...{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}  ➡️ 过期时间: {expires_at_human}{Style.RESET_ALL}")
                        if 'days_left' in locals():
                            print(f"{Fore.CYAN}  ➡️ 剩余天数: {days_left}{Style.RESET_ALL}")
                    else:
                        failed_count += 1
                        print(f"{Fore.RED}{EMOJI['ERROR']} 账号 {email} API返回数据中未包含token{Style.RESET_ALL}")
                else:
                    failed_count += 1
                    api_msg = result.get('msg', 'Unknown error')
                    print(f"{Fore.RED}{EMOJI['ERROR']} 账号 {email} API返回错误: {api_msg}{Style.RESET_ALL}")
            else:
                failed_count += 1
                print(f"{Fore.RED}{EMOJI['ERROR']} 账号 {email} API请求失败，状态码: {response.status_code}{Style.RESET_ALL}")
                
        except requests.exceptions.RequestException as e:
            failed_count += 1
            print(f"{Fore.RED}{EMOJI['ERROR']} 账号 {email} 请求异常: {str(e)}{Style.RESET_ALL}")
        except Exception as e:
            failed_count += 1
            print(f"{Fore.RED}{EMOJI['ERROR']} 账号 {email} 处理异常: {str(e)}{Style.RESET_ALL}")
        
        # 添加延时避免请求过快
        if i < len(valid_accounts) - 1:  # 最后一个账号不需要延时
            print(f"{Fore.CYAN}{EMOJI['WAIT']} 等待2秒后处理下一个账号...{Style.RESET_ALL}")
            time.sleep(2)
    
    # 保存更新后的账号信息
    if success_count > 0:
        try:
            print(f"\n{Fore.CYAN}{EMOJI['SAVE']} 正在保存更新后的账号信息...{Style.RESET_ALL}")
            with open(ACCOUNTS_JSON_FILE, "w", encoding="utf-8") as f:
                json.dump(accounts, f, ensure_ascii=False, indent=4)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 账号信息已保存到 {ACCOUNTS_JSON_FILE}{Style.RESET_ALL}")
            
            # 同步到Git
            commit_msg = f"Batch refresh tokens: {success_count} succeeded, {failed_count} failed"
            print(f"{Fore.CYAN}{EMOJI['UPDATE']} 正在同步到Git...{Style.RESET_ALL}")
            git_sync_accounts(commit_msg)
            
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 保存账号信息时出错: {str(e)}{Style.RESET_ALL}")
    
    # 显示总结
    print(f"\n{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['INFO']} 批量刷新Token完成{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功: {success_count} 个账号{Style.RESET_ALL}")
    print(f"{Fore.RED}{EMOJI['ERROR']} 失败: {failed_count} 个账号{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['INFO']} 总计: {len(valid_accounts)} 个账号{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")


def cursor_login_function(translator=None):
    """
    调用cursor_login.py的main函数功能
    """
    import cursor_login
    print(f"\n{Fore.CYAN}{EMOJI['UPDATE']} 启动Cursor登录流程...{Style.RESET_ALL}")
    try:
        cursor_login.main()
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Cursor登录流程完成。{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} Cursor登录过程中出错: {str(e)}{Style.RESET_ALL}")
        logging.error(f"Cursor登录过程中出错: {str(e)}")

def main_menu_loop():
    """
    主菜单循环，让用户可以多次选择操作而不退出程序
    """
    while True:
        try:
            main_program_logic()
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} 用户中断程序{Style.RESET_ALL}")
            break
        except SystemExit:
            break
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 程序出现错误: {str(e)}{Style.RESET_ALL}")
            logging.error(f"程序出现错误: {str(e)}")
            continue

def main_program_logic():
    """
    原main逻辑，现在在循环中调用
    """
    # 原来if __name__ == "__main__"中的逻辑
    browser_manager = None
    will_exit_cursor = False
    config_instance = Config() # 获取配置实例

    # 定义需要进行连接测试的 URL 列表
    url_cursor_main_page = "https://www.cursor.com"
    url_cloudflare_turnstile_api = "https://challenges.cloudflare.com/turnstile/v0/g/6fab0cec561d/api.js?onload=lwyEv2&render=explicit"
    sign_up_url = "https://www.cursor.com/api/auth/login"
    urls_to_test_with_proxy = [url_cursor_main_page, url_cloudflare_turnstile_api, sign_up_url]

    # 初始化 translator 变量 (尝试导入，如果失败则为 None)
    translator = None
    try:
        # 尝试获取translator
        from main import translator as main_translator
        translator = main_translator
    except ImportError:
        # 如果无法导入，则使用None
        pass

    try:
        # 首先显示账户信息
        # 加载账号信息以获取数量
        accounts_for_count = load_accounts_from_json(translator)
        accounts_count = len(accounts_for_count)
        display_account_info(translator=translator, accounts_count=accounts_count)

        # 提示用户选择操作模式
        print("\n请选择操作模式:")
        print("1. 仅注册账号 (默认)")
        print("2. 查看账户信息 / 切换账号")
        print("3. 监听邮箱获取验证码")
        print("4. 自动邮箱购买和监控")
        print("5. 获取指定邮箱验证码")
        print("6. Cursor登录获取Token")
        print("7. 批量刷新Token")
        print("0. 退出程序")
        print("-----------------------------")

        choice = 0
        while True:
            try:
                choice_input = input("请输入选项 (默认1): ").strip()
                # 如果用户直接按回车，默认选择1
                if choice_input == "":
                    choice = 1
                    break
                choice = int(choice_input)
                if choice in [0, 1, 2, 3, 4, 5, 6, 7, 8]: # 更新范围，添加选项7和8
                    break
                else:
                    print("无效的选项,请重新输入")
            except ValueError:
                print("请输入有效的数字")

        # --- Handle choices that don't involve registration first ---
        if choice == 0: # 退出选项
            logging.info("退出程序")
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 谢谢使用！{Style.RESET_ALL}")
            sys.exit(0)
        elif choice == 6: # Cursor登录
            cursor_login_function(translator)
            # 登录完成后返回主菜单，不退出
            input(f"\n{Fore.CYAN}按回车键返回主菜单...{Style.RESET_ALL}")
            return
        elif choice == 2: # New position for View/Manage Accounts
            # --- Start copied block for View/Manage Accounts ---
            print(f"\n{Fore.YELLOW}--- 查看/管理已保存的账号 ({ACCOUNTS_JSON_FILE}) ---{Style.RESET_ALL}")
            saved_accounts = load_accounts_from_json(translator) # 使用加载函数

            # 初始化缓存字典 (保持不变)
            accounts_usage_cache = {}

            # +++ 新增：并发获取账号数据的函数 +++
            def fetch_account_data(account):
                """获取单个账号的订阅和用量信息"""
                email = account.get('email')
                token = account.get('token')
                if not email or not token:
                    return email, None # 返回 email 以便识别，但数据为 None

                # REMOVED: Individual print statements for fetching_data_for
                # sys.stdout.flush()

                try:
                    start_time = time.time()
                    # 注意：这里的 UsageManager 实例需要在线程内创建或确保是线程安全的
                    # 假设 UsageManager 的方法是线程安全的（通常requests是）
                    # 或者可以在函数外部创建一次 manager 实例传递进来（如果安全）
                    # 为简单起见，暂时假设它是安全的或无状态的
                    subscription_info = UsageManager.get_stripe_profile(token)
                    usage_info = UsageManager.get_usage(token)
                    end_time = time.time()
                    # REMOVED: clear_line and individual print for fetching_data_success

                    return email, {'subscription_info': subscription_info, 'usage_info': usage_info}
                except Exception as e:
                    # REMOVED: clear_line and individual print for fetching_data_error
                    return email, None # 返回 email 和 None 表示失败
            # +++ 结束新增函数 +++

            # 递归函数，用于显示账号列表并处理用户操作
            def show_accounts_and_handle_actions(accounts, accounts_usage_cache):
                if not accounts:
                    print(f"{Fore.YELLOW}未找到账号信息文件 ({ACCOUNTS_JSON_FILE}) 或文件为空。{Style.RESET_ALL}")
                    return False

                # +++ 修改：仅当缓存为空时并发获取所有账号数据 +++
                if not accounts_usage_cache: # 如果缓存为空，则尝试填充它
                    accounts_to_fetch_for_cache = [acc for acc in accounts if acc.get('token')]
                    if accounts_to_fetch_for_cache:
                        total_to_fetch = len(accounts_to_fetch_for_cache)
                        fetched_count = 0
                        # accounts_usage_cache.clear() # 不再需要，因为我们只在cache为空时填充
                        max_workers = min(10, len(accounts_to_fetch_for_cache)) # 避免过多线程
                        collected_errors = [] # 用于收集错误信息

                        # 初始进度消息
                        progress_message_text = translator.get('accounts.manage.fetching_progress', done=fetched_count, total=total_to_fetch) if translator else f'正在并发获取 {fetched_count}/{total_to_fetch} 个账号的数据...'
                        sys.stdout.write(f"{Fore.CYAN}{EMOJI['WAIT']} {progress_message_text}{Style.RESET_ALL}") # 使用单个图标
                        sys.stdout.flush()

                        start_fetch_all = time.time()

                        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                            future_to_email = {executor.submit(fetch_account_data, acc): acc.get('email') for acc in accounts_to_fetch_for_cache}

                            for future in concurrent.futures.as_completed(future_to_email):
                                fetched_count += 1
                                email_from_future = future_to_email[future] # 使用未来对象中的 email

                                try:
                                    email_result, data = future.result()
                                    if email_result == email_from_future and data:
                                        accounts_usage_cache[email_from_future] = data # 填充缓存
                                    elif email_result == email_from_future and data is None:
                                        error_detail = translator.get('accounts.manage.fetch_data_returned_none', email=email_from_future) if translator else f"数据获取返回空值"
                                        collected_errors.append({'email': email_from_future, 'error': error_detail})
                                except Exception as exc:
                                    error_detail = str(exc)
                                    collected_errors.append({'email': email_from_future, 'error': error_detail})
                                
                                # 更新进度行
                                progress_message_text = translator.get('accounts.manage.fetching_progress', done=fetched_count, total=total_to_fetch) if translator else f'正在并发获取 {fetched_count}/{total_to_fetch} 个账号的数据...'
                                # 清理行并打印新消息
                                terminal_width_for_progress = 80  # 根据需要调整
                                full_progress_line = f"{Fore.CYAN}{EMOJI['WAIT']} {progress_message_text}{Style.RESET_ALL}" # 使用单个图标
                                sys.stdout.write('\r' + full_progress_line.ljust(terminal_width_for_progress) + '\r')
                                sys.stdout.flush()
                        
                        end_fetch_all = time.time()

                        # 最终摘要消息，覆盖进度条
                        final_summary_text = translator.get('accounts.manage.fetching_all_complete_summary', count=fetched_count, total_time=f'{end_fetch_all - start_fetch_all:.2f}') if translator else f'所有 {fetched_count} 个账号数据尝试获取完成，总耗时: {end_fetch_all - start_fetch_all:.2f}秒'
                        full_final_summary_line = f"{Fore.GREEN}{EMOJI['SUCCESS']} {final_summary_text}{Style.RESET_ALL}" # 使用单个图标
                        # 使用 print 会自动换行，ljust 用于清除旧进度条的残余
                        print() # 添加一个空行以分隔
                        print('\r' + full_final_summary_line.ljust(terminal_width_for_progress))

                        # 如果有错误，打印错误摘要
                        if collected_errors:
                            error_header = translator.get('accounts.manage.fetching_error_summary_header') if translator else '以下账号在获取数据时发生错误:'
                            # print(f"{Fore.RED}{EMOJI['ERROR']} {error_header}{Style.RESET_ALL}") # 用户要求不显示错误信息
                            for err_info in collected_errors:
                                # print(f"  {Fore.YELLOW}- {err_info['email']}: {err_info['error']}{Style.RESET_ALL}") # 用户要求不显示错误信息
                                pass # 保留循环结构，但内部不执行任何操作
                    # else:
                        # 如果没有带token的账号，缓存将保持为空，后续显示会体现这一点
                        # print(f"{Fore.YELLOW}首次加载：无带Token账号可获取详细信息。{Style.RESET_ALL}") # 可选的调试信息
                # else:
                    # accounts_usage_cache 不为空，说明是后续调用，使用已有的缓存数据
                    # print(f"{Fore.CYAN}使用已缓存的账号实时信息。{Style.RESET_ALL}") # 可选的调试信息
                # +++ 结束并发获取的条件修改 +++


                # 显示账号列表 (这部分逻辑不变，只是现在从缓存读取)
                # --- Fix unnecessary backslashes in print statements ---
                print(f"\n{Fore.CYAN}{'━' * 40}{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{EMOJI['USER']} 账号管理{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{'─' * 40}{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{translator.get('accounts.manage.title') if translator else '选择操作:'}{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}0. {translator.get('accounts.manage.return') if translator else '返回主菜单'}{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{'─' * 40}{Style.RESET_ALL}")

                # 为每个账号添加选项编号和详细信息
                last_index = -1 # Keep track of the last displayed index
                for i, acc in enumerate(accounts):
                    acc_email = acc.get('email', 'N/A')
                    # acc_token = acc.get('token') # 不再需要在循环中获取 token 来判断
                    acc_number = i + 1
                    last_index = i # Update last index

                    # 账号选项基本信息
                    print(f"{Fore.YELLOW}{acc_number}. {Fore.CYAN}{EMOJI['USER']} {Fore.WHITE}{acc_email}{Style.RESET_ALL}")

                    # 获取并显示实时信息 (现在从缓存读取)
                    indent = "   "
                    # --- 修改：从 accounts_usage_cache 获取数据 ---
                    cached_data = accounts_usage_cache.get(acc_email)
                    if cached_data:
                        subscription_info = cached_data.get('subscription_info')
                        usage_info = cached_data.get('usage_info')
                        # --- 数据显示逻辑保持不变 ---
                        if subscription_info:
                            subscription_type = format_subscription_type(subscription_info)
                            print(f"{indent}{Fore.BLUE}{EMOJI['SUBSCRIPTION']} 订阅: {Fore.WHITE}{subscription_type}{Style.RESET_ALL}")
                            
                            # 优先显示token过期时间，如果没有则显示试用天数
                            acc_expire_time = acc.get('expire_time')
                            if acc_expire_time:
                                print(f"{indent}{Fore.BLUE}{EMOJI['TIME']} Token过期时间: {Fore.WHITE}{acc_expire_time}{Style.RESET_ALL}")
                            else:
                                days_remaining = subscription_info.get("daysRemainingOnTrial")
                                if days_remaining is not None and isinstance(days_remaining, (int, float)) and days_remaining > 0:
                                    print(f"{indent}{Fore.BLUE}{EMOJI['TIME']} 实时剩余试用: {Fore.WHITE}{int(days_remaining)} 天{Style.RESET_ALL}")
                                else:
                                    print(f"{indent}{Fore.BLUE}{EMOJI['TIME']} 实时剩余试用: {Fore.WHITE}无{Style.RESET_ALL}")

                        if usage_info:
                            premium_usage = usage_info.get('premium_usage', 0)
                            max_premium_usage = usage_info.get('max_premium_usage', "No Limit")
                            basic_usage = usage_info.get('basic_usage', 0)
                            max_basic_usage = usage_info.get('max_basic_usage', "No Limit")
                            premium_display = f"{premium_usage}/{max_premium_usage}"
                            basic_display = f"{basic_usage}/{max_basic_usage}"
                            print(f"{indent}{Fore.MAGENTA}{EMOJI['PREMIUM']} 高级: {Fore.WHITE}{premium_display}{Style.RESET_ALL}")
                            print(f"{indent}{Fore.MAGENTA}{EMOJI['BASIC']} 基础: {Fore.WHITE}{basic_display}{Style.RESET_ALL}")
                    else:
                        # 如果缓存中没有数据 (例如 token 不存在或获取失败)
                        if acc.get('token'): # 检查 token 是否存在，如果存在但没数据，说明获取失败
                             print(f"{indent}{Fore.RED}{EMOJI['ERROR']} 获取实时信息失败{Style.RESET_ALL}")
                        else: # 如果 token 本身就不存在
                             print(f"{indent}{Fore.YELLOW}{EMOJI['WARNING']} 无Token，无法获取实时信息{Style.RESET_ALL}")
                    # --- 结束修改 ---

                    # 账号间分隔线
                    if i < len(accounts) - 1:
                         print(f"{Fore.CYAN}{'─' * 40}{Style.RESET_ALL}")

                print(f"{Fore.CYAN}{'─' * 40}{Style.RESET_ALL}")
                # Use last_index + 1 for the delete prompt index
                print(f"{Fore.RED}D{last_index + 1}. {translator.get('accounts.manage.delete_prompt', index=last_index + 1) if translator else f'删除账号 {last_index + 1}'}") # 添加删除选项提示
                # --- End Fix ---

                # 获取用户选择
                manage_choice = ""
                while True:
                    try:
                        manage_input = input(f"{Fore.CYAN}{translator.get('accounts.manage.prompt') if translator else '请输入选项 (数字切换, d+数字删除, 0 返回): '}{Style.RESET_ALL}").strip().lower()
                        if manage_input == "0":
                            manage_choice = "0"
                            break
                        elif manage_input.startswith('d') and len(manage_input) > 1:
                            delete_index_str = manage_input[1:]
                            if delete_index_str.isdigit():
                                delete_index = int(delete_index_str) - 1 # 转换为 0-based index
                                if 0 <= delete_index < len(accounts):
                                    manage_choice = f"d{delete_index}" # 存储 0-based index
                                    break
                                else:
                                     print(f"{Fore.RED}{translator.get('accounts.manage.invalid_delete_index') if translator else '无效的删除序号'}{Style.RESET_ALL}")
                            else:
                                 print(f"{Fore.RED}{translator.get('accounts.manage.invalid_delete_format') if translator else '删除格式错误，应为 d+数字'}{Style.RESET_ALL}")
                        elif manage_input.isdigit():
                            switch_choice = int(manage_input)
                            if 1 <= switch_choice <= len(accounts):
                                manage_choice = f"s{switch_choice - 1}" # 存储 0-based index for switch
                                break
                            else:
                                print(f"{Fore.RED}{translator.get('accounts.manage.invalid_switch_option') if translator else '无效的切换选项'}{Style.RESET_ALL}")
                        else:
                            print(f"{Fore.RED}{translator.get('accounts.manage.invalid_input') if translator else '无效的输入'}{Style.RESET_ALL}")
                    except ValueError:
                         print(f"{Fore.RED}{translator.get('accounts.manage.invalid_input') if translator else '无效的输入'}{Style.RESET_ALL}")

                # 处理用户选择
                if manage_choice == "0":
                    # 返回主菜单
                    return False
                elif manage_choice.startswith('s'):
                    switch_index = int(manage_choice[1:])
                    # 用户选择了切换账号
                    selected_account = accounts[switch_index]
                    selected_email = selected_account.get('email')
                    selected_token = selected_account.get('token')

                    if not selected_token:
                        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.no_token') if translator else '选择的账号没有有效的Token，无法切换。'}{Style.RESET_ALL}")
                    else:
                        print(f"\n{Fore.CYAN}{'━' * 40}{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}--- 正在切换账号 ---{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}{'─' * 40}{Style.RESET_ALL}")
                        # 获取密码
                        selected_password = selected_account.get('password', translator.get('accounts.password_not_found') if translator else '密码未找到')
                        # 修改打印语句以包含密码，并换行显示
                        print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('accounts.switch.switching_email', email=selected_email) if translator else f'正在切换到账号: {selected_email}'}{Style.RESET_ALL}")
                        # 计算缩进空格数，使其与上一行对齐 (emoji + 空格)
                        indent_spaces = " " * (len(EMOJI['UPDATE']) + 1)
                        print(f"{Fore.CYAN}{indent_spaces}{translator.get('accounts.switch.switching_password', password=selected_password) if translator else f'密码: {selected_password}'}{Style.RESET_ALL}")
                        
                        switch_successful_regular = False
                        use_curs0rgo_api_regular = os.getenv("CURSOR_GO_API") == 'True' # This is the line the script uses

                        if use_curs0rgo_api_regular:
                            print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('accounts.switch.using_curs0rgo_api') if translator else '检测到 CURSOR_GO_API=True，将使用 curs0rgo API 进行切换...'}{Style.RESET_ALL}")
                            curs0rgo_process_regular = None # Keep track if needed
                            try:
                                print(f"{Fore.BLUE}{EMOJI['INFO']} 正在检查并尝试启动 curs0rgo 服务（如果尚未运行）...{Style.RESET_ALL}")
                                # 尝试启动 curs0rgo 应用
                                curs0rgo_start_command_regular = ['open', '-a', 'curs0rgo']
                                try:
                                    subprocess.run(['pkill', 'curs0rgo'], check=False, capture_output=True)
                                    time.sleep(0.5)
                                    process_result_reg = subprocess.run(curs0rgo_start_command_regular, check=True, capture_output=True)
                                    # print(f"{Fore.GREEN}{EMOJI['SUCCESS']} curs0rgo 应用启动命令 'open -a curs0rgo' 已执行。{Style.RESET_ALL}")
                                    if process_result_reg.returncode == 0:
                                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} curs0rgo 应用启动命令 'open -a curs0rgo' 执行成功。{Style.RESET_ALL}")
                                    else:
                                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} curs0rgo 应用启动命令 'open -a curs0rgo' 执行，但返回码为 {process_result_reg.returncode}。输出: {process_result_reg.stderr.decode()}{Style.RESET_ALL}")
                                    
                                    time.sleep(3) # 给予应用足够的启动时间
                                except subprocess.CalledProcessError as e_start_reg_sub:
                                    print(f"{Fore.RED}{EMOJI['ERROR']} 启动 curs0rgo 应用 ('open -a curs0rgo') 失败: {e_start_reg_sub}{Style.RESET_ALL}")
                                    print(f"{Fore.RED}错误输出: {e_start_reg_sub.stderr.decode()}{Style.RESET_ALL}")
                                    raise
                                except FileNotFoundError:
                                    print(f"{Fore.RED}{EMOJI['ERROR']} 命令 'open' 未找到。请确保在 macOS 环境下运行。{Style.RESET_ALL}")
                                    raise
                                except Exception as e_start_reg:
                                    print(f"{Fore.RED}{EMOJI['ERROR']} 启动 curs0rgo 应用时发生未知错误: {e_start_reg}{Style.RESET_ALL}")
                                    raise

                                from curs0rgo_switch_account import switch_account as curs0rgo_api_switch_regular
                                print(f"{Fore.CYAN}{EMOJI['WAIT']} {translator.get('accounts.switch.calling_api', email=selected_email) if translator else f'正在调用 curs0rgo API 切换到账号 {selected_email}...'}{Style.RESET_ALL}")
                                
                                api_response_ok_regular = curs0rgo_api_switch_regular(selected_email, selected_token)
                                
                                if api_response_ok_regular:
                                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.switch.api_success') if translator else 'curs0rgo API 切换成功。'}{Style.RESET_ALL}")
                                    switch_successful_regular = True
                                    # API 调用成功后，尝试关闭 curs0rgo 服务
                                    # print(f"{Fore.BLUE}{EMOJI['INFO']} curs0rgo API 调用成功，正在尝试关闭 curs0rgo 服务...{Style.RESET_ALL}")
                                    # subprocess.run(['pkill', '-f', 'run_curs0rgo.sh'], check=False)
                                    # print(f"{Fore.GREEN}{EMOJI['SUCCESS']} curs0rgo 服务关闭命令已执行。{Style.RESET_ALL}")
                                else:
                                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.api_failed_status') if translator else 'curs0rgo API 切换失败 (API未返回成功状态)。'}{Style.RESET_ALL}")
                                    switch_successful_regular = False
                            except ImportError:
                                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.api_import_error', script_name='curs0rgo_switch_account.py') if translator else '错误: CURSOR_GO_API 设置为True, 但无法导入 curs0rgo_switch_account.py。'}{Style.RESET_ALL}")
                                switch_successful_regular = False
                            except Exception as e:
                                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.api_call_exception', error=str(e)) if translator else f'调用 curs0rgo API 时发生意外错误: {str(e)}'}{Style.RESET_ALL}")
                                switch_successful_regular = False
                            finally:
                                # 无论 curs0rgo_process_regular 状态如何，如果使用了 API，都尝试关闭
                                if use_curs0rgo_api_regular:
                                    print(f"{Fore.BLUE}{EMOJI['INFO']} 正在尝试关闭 curs0rgo 应用...{Style.RESET_ALL}")
                                    try:
                                        subprocess.run(['pkill', 'curs0rgo'], check=False, capture_output=True)
                                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} curs0rgo 应用关闭命令 'pkill curs0rgo' 已执行。{Style.RESET_ALL}")
                                    except Exception as e_kill:
                                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 关闭 curs0rgo 应用时可能发生错误: {e_kill}{Style.RESET_ALL}")
                        else:
                            print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('accounts.switch.using_local_script') if translator else 'CURSOR_GO_API 未设置或不为True，将使用本地脚本进行重置和切换...'}{Style.RESET_ALL}")
                            print(f"{Fore.CYAN}{EMOJI['RESET']} {translator.get('accounts.switch.resetting_completely') if translator else '正在执行完全重置Cursor...'}{Style.RESET_ALL}")
                            try:
                                print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('cursor.exiting_for_reset') if translator else '准备关闭 Cursor 以执行重置...'}{Style.RESET_ALL}")
                                ExitCursor()
                                time.sleep(3)

                                print(f"\n{Fore.CYAN}{EMOJI['RESET']} {translator.get('accounts.manage.cleaning_cache') if translator else '正在清理Cursor缓存...'}{Style.RESET_ALL}")
                                clean_cursor_cache(translator)

                                totally_reset_cursor.run(translator)
                                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.switch.reset_complete_success') if translator else '完全重置执行成功。'}{Style.RESET_ALL}")
                                switch_successful_regular = True # 假设 run 成功如果不抛出异常
                            except FileNotFoundError:
                                 print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.reset_script_not_found') if translator else '错误：未找到重置所需的脚本 (例如 totally_reset_cursor.py)。'}{Style.RESET_ALL}")
                            except subprocess.CalledProcessError as e:
                                cmd_str = ' '.join(e.cmd)
                                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.reset_script_failed', cmd=cmd_str, code=e.returncode) if translator else f'重置脚本执行失败 (命令: {cmd_str}, 返回码: {e.returncode})'}:{Style.RESET_ALL}")
                            except Exception as e:
                                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.reset_exception', error=str(e)) if translator else f'执行完全重置时发生意外错误: {str(e)}'}{Style.RESET_ALL}")
                                # 打印更详细的错误信息
                                import traceback
                                logging.error(f"完全重置时出错: {traceback.format_exc()}")


                            # --- Only proceed if reset succeeded ---
                            # if go_sh_success:
                            if switch_successful_regular: # 使用新的标志位
                                # 然后更新认证信息
                                print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('accounts.switch.updating_auth') if translator else '正在更新认证信息...'}{Style.RESET_ALL}") # 添加更新提示
                                auth_updated = update_cursor_auth(
                                    email=selected_email,
                                    access_token=selected_token,
                                    refresh_token=selected_token # 假设 refresh_token 和 access_token 一样
                                )

                                if auth_updated:
                                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.switch.auth_updated') if translator else '认证信息已更新'}{Style.RESET_ALL}")
                                    print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('accounts.switch.restarting') if translator else '准备重启Cursor...'}{Style.RESET_ALL}")
                                    # ExitCursor() # 重置前已退出
                                    # time.sleep(3)
                                    restart_cursor() # 直接重启
                                    print(f"\n{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('accounts.switch.complete') if translator else '账号切换完成！'}{Style.RESET_ALL}")
                                else:
                                    print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.auth_failed') if translator else '更新认证信息失败，账号切换未完成。'}{Style.RESET_ALL}")
                            else:
                                 print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('accounts.switch.skipped_due_to_reset_error') if translator else '由于完全重置执行失败，已跳过后续的认证更新和重启步骤。'}{Style.RESET_ALL}")
                        
                        # 切换账号后退出循环，返回主菜单
                        return False

                elif manage_choice.startswith('d'):
                    delete_index = int(manage_choice[1:])
                    # 用户选择了删除账号
                    email_to_delete = accounts[delete_index].get("email", "未知邮箱")
                    # 修改确认提示和逻辑，使回车默认为删除
                    print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('accounts.delete.confirm_enter_yes', email=email_to_delete) if translator else f'您确定要删除账号 {email_to_delete} 吗？此操作不可恢复。(回车=是, n=否)'}{Style.RESET_ALL}")
                    confirm_input = input().strip().lower()
                    if confirm_input == '' or confirm_input == 'y': # 如果是回车或输入 'y'
                        # 3. 添加 Git Sync after Deletion
                        if delete_account_from_json(delete_index, translator):
                            # 删除成功后同步 Git
                            commit_msg = f"Delete account: {email_to_delete}"
                            git_sync_accounts(commit_msg)
                            
                            # 从缓存中移除被删除的账号
                            if email_to_delete in accounts_usage_cache:
                                del accounts_usage_cache[email_to_delete]
                            
                            # 删除成功后重新加载账号列表，但继续循环而不是退出
                            accounts = load_accounts_from_json(translator)
                            # 递归调用，显示更新后的账号列表 (传递已更新的缓存)
                            return show_accounts_and_handle_actions(accounts, accounts_usage_cache)
                        # else: 删除失败，delete_account_from_json 已打印错误
                    else:
                        print(f"{Fore.CYAN}{translator.get('accounts.delete.cancelled') if translator else '删除操作已取消。'}{Style.RESET_ALL}")
                    
                    # 删除操作后，无论成功与否，继续循环显示账号列表 (传递可能已更新的缓存)
                    return show_accounts_and_handle_actions(accounts, accounts_usage_cache)
                
                # 默认情况下继续循环
                return show_accounts_and_handle_actions(accounts, accounts_usage_cache)
            
            # 调用递归函数显示账号列表并处理用户操作
            # --- 修改：传递空的 cache 给第一次调用 ---
            if show_accounts_and_handle_actions(saved_accounts, {}) == False:
                 # Note: The cache is now populated *inside* the function before display
                # 返回主菜单而不是退出程序
                input(f"\n{Fore.CYAN}按回车键返回主菜单...{Style.RESET_ALL}")
                return
            # --- End copied block for View/Manage Accounts ---

        elif choice == 3: # Should now correctly handle Monitor Email
            logging.info("=== 开始监听邮箱获取验证码 ===")
            try:
                # 初始化邮箱验证处理器
                email_handler = EmailVerificationHandler()

                # --- 移除询问用户输入邮箱的步骤 ---
                # print(f"\n{Fore.CYAN}{EMOJI['MAIL']} {translator.get('email.monitor.enter_email') if translator else '请输入要监听的邮箱账号（留空则监听配置文件中的默认邮箱）：'}{Style.RESET_ALL}")
                # account_input = input().strip()

                # 直接使用配置中的默认邮箱
                account = email_handler.username + email_handler.emailExtension
                print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('email.monitor.using_default') if translator else f'将监听默认邮箱：{account}'}{Style.RESET_ALL}")
                # --- 结束移除 ---

                # 监听邮箱获取验证码
                print(f"{Fore.CYAN}{EMOJI['WAIT']} {translator.get('email.monitor.starting') if translator else '开始监听邮箱，等待验证码...'}{Style.RESET_ALL}")
                verification_code = monitor_email_for_verification(email_handler, account, max_attempts=60, interval=3)

                if verification_code:
                    print(f"\n{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('email.monitor.code_received') if translator else '已收到验证码：'} {Fore.YELLOW}{verification_code}{Style.RESET_ALL}")
                else:
                    print(f"\n{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('email.monitor.no_code') if translator else '监听结束，未收到验证码。'}{Style.RESET_ALL}")

            except Exception as e:
                logging.error(f"监听邮箱时出错: {str(e)}")
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('email.monitor.error') if translator else f'监听邮箱时出错: {str(e)}'}{Style.RESET_ALL}")

            # 监听完成后返回主菜单
            input(f"\n{Fore.CYAN}按回车键返回主菜单...{Style.RESET_ALL}")
            return
        
        elif choice == 4: # 自动邮箱购买和监控
            try:
                import auto_email_purchase_monitor
                print(f"\n{Fore.CYAN}{EMOJI['START']} 启动自动邮箱购买和监控系统...{Style.RESET_ALL}")
                auto_email_purchase_monitor.main()
            except ImportError:
                print(f"{Fore.RED}{EMOJI['ERROR']} 找不到自动邮箱购买监控模块 (auto_email_purchase_monitor.py){Style.RESET_ALL}")
            except Exception as e:
                print(f"{Fore.RED}{EMOJI['ERROR']} 自动邮箱购买监控过程中出错: {str(e)}{Style.RESET_ALL}")
            
            # 完成后返回主菜单
            input(f"\n{Fore.CYAN}按回车键返回主菜单...{Style.RESET_ALL}")
            return
        
        elif choice == 5: # 获取指定邮箱验证码
            get_outlook_verification_code(translator)
            # 完成后返回主菜单
            input(f"\n{Fore.CYAN}按回车键返回主菜单...{Style.RESET_ALL}")
            return

        elif choice == 7: # 批量刷新Token
            batch_refresh_tokens(translator)
            # 完成后返回主菜单
            input(f"\n{Fore.CYAN}按回车键返回主菜单...{Style.RESET_ALL}")
            return



        # --- Choices 1: Registration flow ---
        elif choice == 1: # 仅处理选项 1
            # Decide if Cursor needs exiting
            # if choice == 4: # 原 3 -- 注释掉或删除这部分逻辑
            #     will_exit_cursor = True
                # ExitCursor() # 注释掉这一行，不再退出 Cursor

            logging.info("正在初始化浏览器...")
            # 获取user_agent (移回开头)
            user_agent = get_user_agent()

            browser_manager = BrowserManager()
            # 修改为接收浏览器和代理信息
            # TODO (建议): 为了确保主浏览器实例也以干净的状态启动并具有随机视口，
            # 考虑在 BrowserManager.init_browser() (位于 browser_utils.py) 中实现以下逻辑：
            # 1. 使用 tempfile.mkdtemp() 创建临时的 user_data_path 并传递给 ChromiumOptions。
            # 2. 随机化 --window-size 参数，类似于 get_user_agent 中的实现。
            # 3. 确保在 BrowserManager.quit() 中或适当的 finally 块中清理此临时 user_data_path。
            # 目前，这些自定义仅应用于 get_user_agent 中的临时浏览器实例。
            browser_instance, proxy_address = browser_manager.init_browser(user_agent) # 传递获取到的UA

            if not browser_instance: # 保留通用浏览器初始化失败检查
                logging.error("浏览器未能成功初始化。程序即将退出。") # BrowserManager应已记录具体原因
                sys.exit(1)

            # 根据用户描述的逻辑进行代理失败检查
            # configured_proxy_url = getattr(config_instance, 'PROXY_URL', None)
            # configured_browser_proxy = getattr(config_instance, 'BROWSER_PROXY', None)
            
            # is_juliang_configured_initial_check = bool(configured_proxy_url and "juliangip.com" in configured_proxy_url)

            # if is_juliang_configured_initial_check:
                # 如果 JuliangIP 被配置为 PROXY_URL
                # 检查最终使用的 proxy_address 是否表明 JuliangIP 获取失败
                # juliang_failed_and_fell_back = False
                # if not proxy_address or proxy_address == 'no_proxy': # Juliang 失败且没有代理或明确无代理
                #     juliang_failed_and_fell_back = True
                # elif configured_browser_proxy and proxy_address == configured_browser_proxy: # Juliang 失败且回退到静态 BROWSER_PROXY
                #     juliang_failed_and_fell_back = True
                # 注意：如果 JuliangIP 成功，proxy_address 应该是从 JuliangIP 获取的动态地址，
                # 它既不为空/no_proxy，也不等于静态的 BROWSER_PROXY (除非巧合，但不太可能)

                # if juliang_failed_and_fell_back:
                    # BrowserManager 应该已经记录了具体的 JuliangIP 403 错误
                    # logging.error("检测到 PROXY_URL (JuliangIP) 配置尝试失败 (例如，出现403错误)，并且未使用预期的动态代理。根据用户要求，程序将退出。")
                    # 添加用户友好的退出信息并退出脚本
                    # print(f"{Fore.RED}{EMOJI['ERROR']} 配置的动态代理 (JuliangIP) 获取失败，程序将退出。{Style.RESET_ALL}")
                    # sys.exit(1) # Exit the script

            browser = browser_instance # 将浏览器实例赋给 browser 变量
            
            # 初始化 proxies 字典，这个字典会被 requests库使用
            proxies = None
            if proxy_address and proxy_address != 'no_proxy':
                # 将代理地址转换为 requests 需要的字典格式
                proxies = {
                    'http': proxy_address,
                    'https': proxy_address
                }
                logging.info(f"将使用代理进行后续请求: {proxies}")
            else:
                logging.info("后续请求将不使用特定代理。")
            
            # ---- 新的代理连接测试逻辑 ----
            proxy_url_env = os.getenv("PROXY_URL")
            is_juliang_configured = bool(proxy_url_env and "juliangip.com" in proxy_url_env)
            is_static_proxy_configured = bool(os.getenv("BROWSER_PROXY"))
            # refresh_token_url = "https://cursor.sh/api/refreshAuthToken" # 已移到 urls_to_test_with_proxy
            max_juliang_retries = 6
            proxy_test_connected_successfully = False # Flag to indicate overall success

            if is_juliang_configured:
                print(f"{Fore.CYAN}{EMOJI['INFO']} API代理 (JuliangIP) 已配置。将尝试使用动态IP并测试所有目标URL，最多重试 {max_juliang_retries} 次。{Style.RESET_ALL}")
                for attempt in range(max_juliang_retries):
                    print(f"{Fore.CYAN}{EMOJI['WAIT']} JuliangIP 尝试 {attempt + 1}/{max_juliang_retries}...{Style.RESET_ALL}")
                    
                    current_dynamic_proxy = get_new_proxy_from_juliang_only(translator)
                    
                    if not current_dynamic_proxy:
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 从 JuliangIP 获取IP失败 (尝试 {attempt + 1})。{Style.RESET_ALL}")
                        if attempt < max_juliang_retries - 1:
                            time.sleep(2) # 等待后重试
                            continue # 进行下一次获取IP的尝试
                        else:
                            print(f"{Fore.RED}{EMOJI['ERROR']} {max_juliang_retries} 次尝试后均未能从 JuliangIP 获取IP。程序将退出。{Style.RESET_ALL}")
                            sys.exit(1)
                    
                    # 获取到IP，现在测试所有URL
                    all_urls_passed_for_this_ip = True
                    for test_url in urls_to_test_with_proxy:
                        print(f"{Fore.CYAN}{EMOJI['WAIT']} 测试 JuliangIP: {current_dynamic_proxy.get('http')} 连接到 {test_url} (GET)...{Style.RESET_ALL}")
                        try:
                            response_get = requests.get(test_url, proxies=current_dynamic_proxy, timeout=5)
                            logging.info(f"JuliangIP GET测试成功 (URL: {test_url}, HTTP: {response_get.status_code})。")
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} -> {test_url} (GET) 连接成功。{Style.RESET_ALL}")

                        except requests.exceptions.RequestException as e_get:
                            logging.warning(f"JuliangIP GET测试失败 (尝试 {attempt + 1}) 使用 {current_dynamic_proxy.get('http')} 到 {test_url}: {str(e_get)}")
                            print(f"{Fore.YELLOW}{EMOJI['WARNING']} -> {test_url} (GET) 连接失败: {str(e_get)}{Style.RESET_ALL}")
                            all_urls_passed_for_this_ip = False
                            break # 当前IP的GET测试失败，跳出内层URL测试循环
                    
                    if all_urls_passed_for_this_ip:
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 当前 JuliangIP {current_dynamic_proxy.get('http')} 已通过所有URL测试。{Style.RESET_ALL}")
                        proxies = current_dynamic_proxy # 更新主 proxies 变量
                        proxy_test_connected_successfully = True
                        break # 成功，跳出 JuliangIP 的重试循环 (attempt loop)
                    else:
                        # 当前IP未能通过所有URL测试
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 当前 JuliangIP 未能通过所有URL测试 (尝试 {attempt + 1})。{Style.RESET_ALL}")
                        if attempt < max_juliang_retries - 1:
                            time.sleep(2) # 等待后进行下一次获取新IP的尝试
                            # continue is implicit here for the outer loop
                        else:
                            print(f"{Fore.RED}{EMOJI['ERROR']} {max_juliang_retries} 次尝试后，JuliangIP未能提供一个通过所有URL测试的IP。程序将退出。{Style.RESET_ALL}")
                            sys.exit(1)
                
                # 此检查实际上由循环结束时的 else 子句或内部的 sys.exit(1) 覆盖
                # if not proxy_test_connected_successfully: 
                #     print(f"{Fore.RED}{EMOJI['ERROR']} 未能通过 API代理 (JuliangIP) 建立连接 (所有URL)。程序将退出。{Style.RESET_ALL}")
                #     sys.exit(1)

            elif is_static_proxy_configured:
                if not proxies and os.getenv("BROWSER_PROXY"):
                    static_proxy_val = os.getenv("BROWSER_PROXY")
                    if static_proxy_val and not static_proxy_val.startswith(('http://', 'https://')):
                         static_proxy_val = f"http://{static_proxy_val}"
                    if static_proxy_val:
                        proxies = {'http': static_proxy_val, 'https': static_proxy_val}
                        logging.info(f"静态代理已配置，设置为: {proxies}")

                if proxies: 
                    print(f"{Fore.CYAN}{EMOJI['INFO']} API代理未配置。静态代理已配置: {proxies.get('http')}。正在测试所有目标URL...{Style.RESET_ALL}")
                    all_urls_passed_for_static_proxy = True
                    for test_url in urls_to_test_with_proxy:
                        print(f"{Fore.CYAN}{EMOJI['WAIT']} 测试静态代理连接到 {test_url} (GET)...{Style.RESET_ALL}")
                        try:
                            response_get = requests.get(test_url, proxies=proxies, timeout=5)
                            logging.info(f"静态代理 GET测试成功 (URL: {test_url}, HTTP: {response_get.status_code})。")
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} -> {test_url} (GET) 连接成功。{Style.RESET_ALL}")

                        except requests.exceptions.RequestException as e_get:
                            logging.error(f"静态代理 GET测试失败 ({proxies.get('http')}) 到 {test_url}: {str(e_get)}")
                            print(f"{Fore.RED}{EMOJI['ERROR']} -> {test_url} (GET) 连接失败: {str(e_get)}{Style.RESET_ALL}")
                            all_urls_passed_for_static_proxy = False
                            break # 静态代理的GET测试失败，跳出URL测试循环

                    if all_urls_passed_for_static_proxy:
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 静态代理已通过所有URL测试。{Style.RESET_ALL}")
                        proxy_test_connected_successfully = True
                    else:
                        print(f"{Fore.RED}{EMOJI['ERROR']} 静态代理未能通过所有URL测试。程序将退出。{Style.RESET_ALL}")
                        sys.exit(1)
                else: 
                    print(f"{Fore.RED}{EMOJI['ERROR']} 静态代理已配置，但无法正确设置代理信息。程序将退出。{Style.RESET_ALL}")
                    sys.exit(1)
            
            else:
                print(f"{Fore.CYAN}{EMOJI['INFO']} 未配置API代理或静态代理。将不使用代理继续。{Style.RESET_ALL}")
                proxies = None # 确保 proxies 为 None
                proxy_test_connected_successfully = True # 无需测试，视为成功

            # 最终检查，如果未能成功连接且需要代理，则退出
            if not proxy_test_connected_successfully and (is_juliang_configured or is_static_proxy_configured) :
                 print(f"{Fore.RED}{EMOJI['ERROR']} 代理配置检查和连接尝试完成，但未能建立有效代理连接。程序将退出。{Style.RESET_ALL}")
                 sys.exit(1)
            # ---- 结束新的代理连接测试逻辑 ----
            
            # 记录最终使用的代理状态 (这行日志现在由新的测试逻辑的末尾部分处理，或者可以被移除如果上面有等效的)
            if proxies:
                logging.info(f"继续注册流程，将使用代理: {proxies}")
            else:
                logging.info("继续注册流程，不使用代理。")
            # -------------------- 结束代理连接性测试的逻辑 --------------------

            logging.info("正在初始化邮箱验证模块...")
            email_handler = EmailVerificationHandler()
            logging.info("\n=== 配置信息 ===")
            login_url = "https://authenticator.cursor.sh"
            sign_up_url = "https://www.cursor.com/api/auth/login"
            settings_url = "https://www.cursor.com/cn/settings"
            mail_url = "https://tempmail.plus"

            logging.info("尝试从文件 outlooks.txt 获取账号信息...")
            OUTLOOKS_FILE_PATH = os.path.join(LOG_DIR, "outlooks.txt")
            account_from_file = None
            password_from_file = None
            client_id_from_file = None
            token_from_file = None
            
            try:
                if os.path.exists(OUTLOOKS_FILE_PATH):
                    with open(OUTLOOKS_FILE_PATH, "r", encoding="utf-8") as f_outlook:
                        first_line = f_outlook.readline().strip()
                    if first_line:
                        parts = first_line.split("----")
                        if len(parts) == 4:
                            account_from_file = parts[0]
                            password_from_file = parts[1]
                            client_id_from_file = parts[2]
                            token_from_file = parts[3]
                            logging.info(f"成功从文件加载账号: {account_from_file}")
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功从文件加载账号: {account_from_file}{Style.RESET_ALL}")
                            print(f"{Fore.CYAN}{EMOJI['INFO']} 从文件加载的 Client ID: {client_id_from_file}{Style.RESET_ALL}")
                            print(f"{Fore.CYAN}{EMOJI['INFO']} 从文件加载的授权令牌: {token_from_file}{Style.RESET_ALL}")
                        else:
                            logging.warning(f"文件 {OUTLOOKS_FILE_PATH} 首行格式不正确，期望4个部分，得到 {len(parts)} 部分。")
                            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 文件 {OUTLOOKS_FILE_PATH} 首行格式不正确。将使用随机生成。{Style.RESET_ALL}")
                    else:
                        logging.warning(f"文件 {OUTLOOKS_FILE_PATH} 为空。")
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 文件 {OUTLOOKS_FILE_PATH} 为空。将使用随机生成。{Style.RESET_ALL}")
                else:
                    logging.warning(f"账号文件 {OUTLOOKS_FILE_PATH} 未找到。")
                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 账号文件 {OUTLOOKS_FILE_PATH} 未找到。将使用随机生成。{Style.RESET_ALL}")
            except Exception as e_read_outlook:
                logging.error(f"读取 outlooks.txt 文件时出错: {str(e_read_outlook)}")
                print(f"{Fore.RED}{EMOJI['ERROR']} 读取 outlooks.txt 文件时出错。将使用随机生成。{Style.RESET_ALL}")

            email_generator = EmailGenerator() # 用于生成 first_name 和 last_name

            if account_from_file and password_from_file:
                account = account_from_file
                password = password_from_file
                logging.info(f"使用文件提供的账号: {account}, 密码: ******, ClientID: {client_id_from_file}, Token: ******")
            else:
                logging.info("未能从文件加载账号信息或文件信息不完整，将随机生成新账号。")
                email_generator = EmailGenerator() # Fallback: instantiate if needed for email/password
                account_details = email_generator.get_account_info()
                account = account_details["email"]
                password = account_details["password"]
                logging.info(f"随机生成的邮箱账号: {account}")

            tab = browser.latest_tab

            tab.run_js("try { turnstile.reset() } catch(e) { }")

            logging.info("\n=== 开始注册流程 ===")

            # +++ 在首次调用 sign_up_account 前解析注册 URL +++
            initial_sign_up_url = "https://www.cursor.com/api/auth/login" # 从配置或硬编码获取初始 URL
            resolved_sign_up_url_for_session = None
            print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('register.resolve_signup_url_initial_start', url=initial_sign_up_url) if translator else f'正在解析初始注册 URL: {initial_sign_up_url}'}{Style.RESET_ALL}")
            try:
                resolved_sign_up_url_for_session = get_final_url(initial_sign_up_url, proxies=proxies)
                if not resolved_sign_up_url_for_session:
                    raise ValueError("Resolved URL is empty or None") # 确保有值
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('register.resolve_signup_url_initial_success', url=resolved_sign_up_url_for_session) if translator else f'初始注册 URL 解析成功，将使用: {resolved_sign_up_url_for_session}'}{Style.RESET_ALL}")
            except Exception as e_initial_resolve:
                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('register.resolve_signup_url_initial_failed', url=initial_sign_up_url, error=str(e_initial_resolve)) if translator else f'解析初始注册 URL {initial_sign_up_url} 失败: {str(e_initial_resolve)}。程序将退出。'}{Style.RESET_ALL}")
                if browser_manager: browser_manager.quit()
                sys.exit(1)
            # +++ 结束初始 URL 解析 +++

            # Pass necessary parameters from outlooks.txt if available
            # Also pass the resolved_sign_up_url_for_session
            if sign_up_account(browser, tab, account, password, resolved_sign_up_url_for_session, settings_url, translator=translator, proxies=proxies, account_from_file=account_from_file, client_id_from_file=client_id_from_file, token_from_file=token_from_file):
                logging.info("注册步骤已在 sign_up_account 函数内完成。")

                # 获取cookie
                bearer_token = None
                max_cookie_detection_attempts = 3
                cookie_detection_attempt_count = 0
                
                sign_up_rerun_count = 0
                MAX_SIGN_UP_RERUNS_FROM_COOKIE_FAILURE = 3

                print(f"{Fore.CYAN}{EMOJI['INFO']} {translator.get('cookie.fetch_attempt_start') if translator else '开始尝试获取 WorkosCursorSessionToken Cookie...'}{Style.RESET_ALL}")
                
                while bearer_token is None and cookie_detection_attempt_count < max_cookie_detection_attempts:
                    current_attempt_number = cookie_detection_attempt_count + 1
                    print(f"{Fore.CYAN}{EMOJI['INFO']} Cookie检测尝试 {current_attempt_number}/{max_cookie_detection_attempts}...{Style.RESET_ALL}")

                    # Try to get cookies on the current page
                    cookies = tab.cookies()
                    # print(f"{Fore.MAGENTA}{EMOJI['INFO']} (尝试 {current_attempt_number}) 当前获取到的Cookies: {cookies}{Style.RESET_ALL}") # Debug log
                    
                    for cookie_item in cookies:
                        if cookie_item.get("name") == "WorkosCursorSessionToken" and "cursor.com" in cookie_item.get("domain", ""):
                            bearer_token = cookie_item["value"]
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('cookie.fetch_success_token') if translator else '成功获取 WorkosCursorSessionToken Cookie。'}{Style.RESET_ALL}")
                            break 
                    
                    if bearer_token is None:
                        # Cookie not found on this current_attempt_number
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 在尝试 {current_attempt_number}/{max_cookie_detection_attempts} 时未能获取Cookie。{Style.RESET_ALL}")

                        email_field_present = False
                        try:
                            if tab.ele("@name=email", timeout=0.5): # Quick check for email input field
                                email_field_present = True
                        except Exception:
                            email_field_present = False # Element not found or other error

                        if email_field_present and sign_up_rerun_count < MAX_SIGN_UP_RERUNS_FROM_COOKIE_FAILURE:
                            print(f"{Fore.CYAN}{EMOJI['INFO']} 检测到邮箱输入框。尝试重新执行注册流程 (sign_up_account 调用次数: {sign_up_rerun_count + 1}/{MAX_SIGN_UP_RERUNS_FROM_COOKIE_FAILURE})。{Style.RESET_ALL}")
                            sign_up_rerun_count += 1
                            sign_up_account( 
                                browser, tab, account, password, 
                                resolved_sign_up_url_for_session, # 使用已解析的 URL
                                settings_url, 
                                translator=translator, proxies=proxies, 
                                account_from_file=account_from_file, 
                                client_id_from_file=client_id_from_file, 
                                token_from_file=token_from_file
                            )
                            # After re-running sign_up_account, the loop will continue to the next cookie detection attempt.
                        elif cookie_detection_attempt_count + 1 < max_cookie_detection_attempts: # More cookie attempts left, but not re-running sign_up
                            print(f"{Fore.CYAN}{EMOJI['WAIT']} {translator.get('cookie.refreshing_page_general_retry') if translator else '将刷新页面并再次尝试获取Cookie...'}{Style.RESET_ALL}")
                            tab.refresh()
                            wait_with_spinner(random.uniform(3, 5), translator.get('cookie.wait_after_refresh') if translator else "页面刷新后等待...")
                        # If it's the last cookie attempt (max_cookie_detection_attempts reached) and bearer_token is still None, the loop will terminate.
                    
                    cookie_detection_attempt_count += 1 # Increment for each pass through the loop where bearer_token was initially None or became None

                    if bearer_token is None and cookie_detection_attempt_count < max_cookie_detection_attempts:
                        # If cookie still not found and more attempts are left, wait a bit before next attempt.
                        # This wait applies whether sign_up_account was re-run or page was refreshed.
                        wait_with_spinner(random.uniform(1, 2), translator.get('cookie.wait_before_next_generic') if translator else "短暂等待后进行下一次Cookie检测...")
                
                # After the while loop for cookie detection
                if bearer_token is None:
                    error_msg = translator.get('error.no_bearer_token_fatal_after_retries') if translator else '致命错误: 经过多次尝试后，仍未能获取 WorkosCursorSessionToken (Cookie)。后续步骤无法执行。'
                    log_msg = error_msg # Same message for logging
                    
                    print(f"{Fore.RED}{EMOJI['ERROR']} {error_msg}{Style.RESET_ALL}")
                    logging.error(log_msg)
                    
                    if browser_manager:
                        try:
                            browser_manager.quit()
                        except Exception as e_quit:
                            logging.warning(f"尝试关闭浏览器时出错: {e_quit}")
                    sys.exit(1) # Exit the script

                # 获取会员信息
                membership_info = UsageManager.get_membership_info(bearer_token)
                # 获取用量信息
                usage_info = UsageManager.get_usage(bearer_token)
                print(f"{Fore.CYAN}    试用天数: {membership_info.get('daysRemainingOnTrial', 0)}{Style.RESET_ALL}")
                print(f"{Fore.CYAN}    剩余额度: {usage_info.get('max_premium_usage', '未知')}{Style.RESET_ALL}")

                # 使用cursor_login.py中的方法获取登录URL和Token
                print(f"{Fore.CYAN}{EMOJI['INFO']} 开始使用登录URL方式获取Token...{Style.RESET_ALL}")
                
                # 导入cursor_login模块中的函数，确保可以使用
                from cursor_login import generate_pkce_pair, get_login_url, query_auth_poll
                
                # 生成PKCE对和UUID
                pkce_pair = generate_pkce_pair()
                verifier = pkce_pair["verifier"]
                challenge = pkce_pair["challenge"]
                uuid_str = str(uuid.uuid4())
                
                # 检查是否有WorkosCursorSessionToken Cookie
                cookies = tab.cookies()
                bearer_token = None
                for cookie_item in cookies:
                    if cookie_item.get("name") == "WorkosCursorSessionToken":
                        bearer_token = cookie_item["value"]
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功获取已有的WorkosCursorSessionToken Cookie。{Style.RESET_ALL}")
                        break
                
                if bearer_token:
                    # 模拟cursor.js中的loginDeepCallbackControl请求
                    print(f"{Fore.CYAN}{EMOJI['INFO']} 尝试使用WorkosCursorSessionToken发起深度认证请求...{Style.RESET_ALL}")
                    
                    # 添加深度认证请求的重试逻辑
                    deep_auth_max_retries = 6
                    deep_auth_success = False
                    
                    for deep_auth_attempt in range(deep_auth_max_retries):
                        try:
                            auth_response = requests.post(
                                "https://www.cursor.com/api/auth/loginDeepCallbackControl",
                                headers={
                                    "Accept": "*/*",
                                    "Content-Type": "application/json",
                                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.210 Safari/537.36",
                                    "Cookie": f"WorkosCursorSessionToken={bearer_token}"
                                },
                                json={
                                    "uuid": uuid_str,
                                    "challenge": challenge
                                },
                                timeout=10,
                                proxies=proxies
                            )
                            
                            if auth_response.status_code == 200:
                                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 深度认证请求成功。{Style.RESET_ALL}")
                                deep_auth_success = True
                                break
                            else:
                                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 深度认证请求返回非200状态码: {auth_response.status_code}，尝试 {deep_auth_attempt+1}/{deep_auth_max_retries}{Style.RESET_ALL}")
                                
                                # 如果是最后一次尝试，记录失败并继续
                                if deep_auth_attempt == deep_auth_max_retries - 1:
                                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 深度认证请求失败达到最大重试次数，将继续尝试获取Token。{Style.RESET_ALL}")
                                    break
                                    
                                # 如果不是最后一次尝试且配置了JuliangIP，则尝试更换代理
                                if is_juliang_configured:
                                    print(f"{Fore.CYAN}{EMOJI['UPDATE']} 尝试更换代理IP并重试深度认证...{Style.RESET_ALL}")
                                    new_proxies = get_new_proxy_from_juliang_only(translator)
                                    if new_proxies:
                                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功获取新的代理IP: {new_proxies.get('http')}{Style.RESET_ALL}")
                                        proxies = new_proxies  # 更新代理
                                    else:
                                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法获取新的代理IP，将使用当前代理重试。{Style.RESET_ALL}")
                                
                                # 等待一段时间后重试
                                wait_with_spinner(random.uniform(1, 2), translator.get('deep_auth.wait_before_retry') if translator else "等待重试深度认证...")
                                
                        except Exception as e:
                            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 深度认证请求出错: {str(e)}，尝试 {deep_auth_attempt+1}/{deep_auth_max_retries}{Style.RESET_ALL}")
                            
                            # 如果是最后一次尝试，记录失败并继续
                            if deep_auth_attempt == deep_auth_max_retries - 1:
                                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 深度认证请求失败达到最大重试次数，将继续尝试获取Token。{Style.RESET_ALL}")
                                break
                                
                            # 如果配置了JuliangIP，则尝试更换代理
                            if is_juliang_configured:
                                print(f"{Fore.CYAN}{EMOJI['UPDATE']} 深度认证请求失败，尝试更换代理IP并重试...{Style.RESET_ALL}")
                                new_proxies = get_new_proxy_from_juliang_only(translator)
                                if new_proxies:
                                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 成功获取新的代理IP: {new_proxies.get('http')}{Style.RESET_ALL}")
                                    proxies = new_proxies  # 更新代理
                                else:
                                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法获取新的代理IP，将使用当前代理重试。{Style.RESET_ALL}")
                            
                            # 等待一段时间后重试
                            wait_with_spinner(random.uniform(1, 2), translator.get('deep_auth.wait_before_retry') if translator else "等待重试深度认证...")
                
                # 轮询获取认证信息 (增加到20次尝试，与cursor.js保持一致)
                retry_attempts = 20
                token_result = None
                
                for i in range(retry_attempts):
                    print(f"{Fore.CYAN}{EMOJI['WAIT']} 等待登录中... ({i + 1}/{retry_attempts}){Style.RESET_ALL}")
                    data = query_auth_poll(uuid_str, verifier, proxies=proxies) # Pass proxies
                    if data:
                        access_token = data.get("accessToken")
                        token_result = access_token
                        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 登录成功。获取到Cursor Token:{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}{token_result}{Style.RESET_ALL}")

                        # 打印完整的data信息
                        #print(f"{Fore.MAGENTA}DEBUG: query_auth_poll successful data:{Style.RESET_ALL}")
                        #print(f"{Fore.MAGENTA}{json.dumps(data, indent=2, ensure_ascii=False)}{Style.RESET_ALL}")
                        
                        break
                    else: # data is None
                        print(f"{Fore.MAGENTA}DEBUG: query_auth_poll attempt {i+1} returned no data.{Style.RESET_ALL}")
                    
                    # 每秒轮询一次，与cursor.js保持一致
                    time.sleep(1)
                    
                    if i == retry_attempts - 1:
                        print(f"{Fore.RED}{EMOJI['ERROR']} 登录超时，请重试。{Style.RESET_ALL}")
                
                if token_result:
                    logging.info("Token 获取成功。现在处理保存和同步...")
                    
                    
                    # 获取会话信息
                    session_info = get_session_info(bearer_token, proxies)
                    
                    # 将保存逻辑移到这里，保存token和会话信息
                    print(f"{Fore.CYAN}{EMOJI['SAVE']} 准备保存账号信息...{Style.RESET_ALL}")
                    
                    # 如果获取到会话信息，使用完整信息保存，否则只保存token
                    if session_info:
                        # 格式化过期时间
                        token_info = {
                            "token": token_result,
                            "session_id": session_info.get("session_id"),
                            "expire_time": session_info.get("expire_time"),
                            "days_left": session_info.get("days_left")
                        }
                        save_success = save_account_to_json(account, password, token_info, translator)
                    else:
                        # 如果获取会话信息失败，仅保存token
                        save_success = save_account_to_json(account, password, token_result, translator)
                    # save_account_to_json 内部会打印成功或失败消息
                    
                    # 添加 Git Sync after Registration
                    if save_success:
                        # 保存成功后同步 Git
                        commit_msg_accounts = f"Add/Update account: {account}"
                        print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('accounts.save.sync_git') if translator else '准备同步账号文件到 Git...'}{Style.RESET_ALL}")
                        git_sync_accounts(commit_msg_accounts) # Sync accounts.json
                        # git_sync_accounts 内部会打印成功或失败消息

                        # ++ 新增: 如果使用了 outlooks.txt 的数据，则删除第一行并同步 ++
                        if account_from_file: # Check if data from outlooks.txt was used
                            print(f"{Fore.CYAN}{EMOJI['UPDATE']} {translator.get('outlooks.remove_first_line.start', file=OUTLOOKS_FILE_PATH) if translator else f'处理 {OUTLOOKS_FILE_PATH}：移除已使用的第一行...'}{Style.RESET_ALL}")
                            try:
                                with open(OUTLOOKS_FILE_PATH, 'r', encoding='utf-8') as f_read_outlook:
                                    lines = f_read_outlook.readlines()
                                
                                if lines: # If the file was not empty
                                    with open(OUTLOOKS_FILE_PATH, 'w', encoding='utf-8') as f_write_outlook:
                                        f_write_outlook.writelines(lines[1:]) # Write back all lines except the first
                                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('outlooks.remove_first_line.success', file=OUTLOOKS_FILE_PATH) if translator else f'{OUTLOOKS_FILE_PATH} 的第一行已成功移除。'}{Style.RESET_ALL}")
                                    
                                    # 同步 outlooks.txt 的更改到 Git
                                    commit_msg_outlooks = f"Remove used account line from {os.path.basename(OUTLOOKS_FILE_PATH)}: {account_from_file}"
                                    # 使用新的同步函数
                                    git_sync_specific_file(OUTLOOKS_FILE_PATH, commit_msg_outlooks, translator)
                                else:
                                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('outlooks.remove_first_line.empty', file=OUTLOOKS_FILE_PATH) if translator else f'{OUTLOOKS_FILE_PATH} 文件为空，无需移除行。'}{Style.RESET_ALL}")
                            except Exception as e_outlook_remove:
                                print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('outlooks.remove_first_line.error', file=OUTLOOKS_FILE_PATH, error=str(e_outlook_remove)) if translator else f'处理 {OUTLOOKS_FILE_PATH} (移除第一行) 时出错: {str(e_outlook_remove)}'}{Style.RESET_ALL}")
                        # ++ 结束新增逻辑 ++
                    
                    # 仅注册账号 (choice == 1) 的后续操作
                    if choice == 1:
                        logging.info("\n=== 注册账号并获取 Token 完成 ===")
                        account_info = f"Cursor 账号信息:\n邮箱: {account}\n密码: {password}"
                        logging.info(account_info)
                        # 打印完整的token信息
                        logging.info(f"Token: {token_result}")

                        print_end_message()
                        logging.info("注册成功，返回主菜单。")
                        # 注册完成后返回主菜单
                        input(f"\n{Fore.CYAN}按回车键返回主菜单...{Style.RESET_ALL}")
                        return
                else:
                    print(f"{Fore.RED}{EMOJI['ERROR']} 未能获取Token，注册流程未完成。{Style.RESET_ALL}")
                    logging.error("未能获取Token，注册流程未完成。")
                    # 注册失败后返回主菜单
                    input(f"\n{Fore.CYAN}按回车键返回主菜单...{Style.RESET_ALL}")
                    return

        # 这个选择已在上面处理了
        # elif choice == 7 已经在上面处理为退出程序
        # +++ 结束新增选项 8 的处理 +++

    except Exception as e:
        logging.error(f"程序执行出现错误: {str(e)}")
        import traceback

        logging.error(traceback.format_exc())
    finally:
        if browser_manager:
            browser_manager.quit() # <<< 修正这里的调用


if __name__ == "__main__":
    # 1. 添加 Git Pull
    print(f"\n{Fore.CYAN}🔄 正在从 Git 拉取最新代码...{Style.RESET_ALL}")
    if run_git_command("git pull"):
        print(f"{Fore.GREEN}✅ 代码同步成功！{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}❌ 代码同步失败，请检查 Git 配置或网络连接。可能继续使用本地旧代码。{Style.RESET_ALL}")
        # 这里可以选择是否在 pull 失败时退出，暂时不退出
        # sys.exit(1) 

    print_logo()
    
    # 启动主菜单循环
    main_menu_loop()
