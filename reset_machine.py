import os
import sys
import json
import uuid
import hashlib
import shutil
import sqlite3
import platform
from colorama import Fore, Style, init

# 初始化colorama
init()

# 定义emoji和颜色常量
EMOJI = {
    "FILE": "📄",
    "BACKUP": "💾",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "INFO": "ℹ️",
    "RESET": "🔄",
    "WARNING": "⚠️",
}


class MachineIDResetter:
    def __init__(self):
        # 判断操作系统
        if sys.platform == "win32":  # Windows
            appdata = os.getenv("APPDATA")
            if appdata is None:
                raise EnvironmentError("APPDATA 环境变量未设置")
            self.db_path = os.path.join(
                appdata, "Cursor", "User", "globalStorage", "storage.json"
            )
            self.sqlite_path = os.path.join(
                appdata, "Cursor", "Local Storage", "leveldb", "000003.log"
            )
            self.machine_id_path = os.path.join(appdata, "Cursor", "machineId")
        elif sys.platform == "darwin":  # macOS
            self.db_path = os.path.abspath(
                os.path.expanduser(
                    "~/Library/Application Support/Cursor/User/globalStorage/storage.json"
                )
            )
            self.sqlite_path = os.path.abspath(
                os.path.expanduser(
                    "~/Library/Application Support/Cursor/Local Storage/leveldb/000003.log"
                )
            )
            self.machine_id_path = os.path.abspath(
                os.path.expanduser("~/Library/Application Support/Cursor/machineId")
            )
        elif sys.platform == "linux":  # Linux 和其他类Unix系统
            self.db_path = os.path.abspath(
                os.path.expanduser("~/.config/Cursor/User/globalStorage/storage.json")
            )
            self.sqlite_path = os.path.abspath(
                os.path.expanduser("~/.config/Cursor/Local Storage/leveldb/000003.log")
            )
            self.machine_id_path = os.path.abspath(
                os.path.expanduser("~/.config/Cursor/machineId")
            )
        else:
            raise NotImplementedError(f"不支持的操作系统: {sys.platform}")

    def generate_new_ids(self):
        """生成新的机器ID"""
        # 生成新的UUID
        dev_device_id = str(uuid.uuid4())

        # 生成新的machineId (64个字符的十六进制)
        machine_id = hashlib.sha256(os.urandom(32)).hexdigest()

        # 生成新的macMachineId (128个字符的十六进制)
        mac_machine_id = hashlib.sha512(os.urandom(64)).hexdigest()

        # 生成新的sqmId
        sqm_id = "{" + str(uuid.uuid4()).upper() + "}"

        # 更新machineId文件
        self.update_machine_id_file(dev_device_id)

        return {
            "telemetry.devDeviceId": dev_device_id,
            "telemetry.macMachineId": mac_machine_id,
            "telemetry.machineId": machine_id,
            "telemetry.sqmId": sqm_id,
            "storage.serviceMachineId": dev_device_id,  # 添加storage.serviceMachineId
        }

    def update_sqlite_db(self, new_ids):
        """更新SQLite数据库中的机器ID"""
        try:
            print(f"{Fore.CYAN}{EMOJI['INFO']} 正在更新SQLite数据库...{Style.RESET_ALL}")
            
            if not os.path.exists(self.sqlite_path):
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} SQLite数据库文件不存在: {self.sqlite_path}{Style.RESET_ALL}")
                return False
                
            # 尝试创建与SQLite的连接
            try:
                conn = sqlite3.connect(self.sqlite_path)
                cursor = conn.cursor()

                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ItemTable (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                """)

                updates = [
                    (key, value) for key, value in new_ids.items()
                ]

                for key, value in updates:
                    cursor.execute("""
                        INSERT OR REPLACE INTO ItemTable (key, value) 
                        VALUES (?, ?)
                    """, (key, value))
                    print(f"{EMOJI['INFO']} {Fore.CYAN} 更新键值对: {key}{Style.RESET_ALL}")

                conn.commit()
                conn.close()
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} SQLite数据库更新成功{Style.RESET_ALL}")
                return True
            except Exception as e:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} SQLite数据库更新失败，这可能不会影响机器码重置: {str(e)}{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} SQLite操作异常: {str(e)}{Style.RESET_ALL}")
            return False

    def update_system_ids(self, new_ids):
        """更新系统级别的ID"""
        try:
            print(f"{Fore.CYAN}{EMOJI['INFO']} 正在更新系统ID...{Style.RESET_ALL}")
            
            if sys.platform.startswith("win"):
                self._update_windows_machine_guid()
                self._update_windows_machine_id()
            elif sys.platform == "darwin":
                self._update_macos_platform_uuid(new_ids)
                
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 系统ID更新成功{Style.RESET_ALL}")
            return True
        except Exception as e:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 系统ID更新失败，这可能不会影响机器码重置: {str(e)}{Style.RESET_ALL}")
            return False

    def _update_windows_machine_guid(self):
        """更新Windows MachineGuid"""
        try:
            import winreg
            key = winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                "SOFTWARE\\Microsoft\\Cryptography",
                0,
                winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
            )
            new_guid = str(uuid.uuid4())
            winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
            winreg.CloseKey(key)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Windows MachineGuid 更新成功{Style.RESET_ALL}")
        except PermissionError:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 权限不足，需要管理员权限{Style.RESET_ALL}")
            raise
        except Exception as e:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 更新Windows MachineGuid失败: {str(e)}{Style.RESET_ALL}")
            raise
    
    def _update_windows_machine_id(self):
        """更新Windows SQMClient注册表中的MachineId"""
        try:
            import winreg
            # 生成新GUID
            new_guid = "{" + str(uuid.uuid4()).upper() + "}"
            print(f"{Fore.CYAN}{EMOJI['INFO']} 新的MachineId: {new_guid}{Style.RESET_ALL}")
            
            # 打开注册表键值
            try:
                key = winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SOFTWARE\Microsoft\SQMClient",
                    0,
                    winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
                )
            except FileNotFoundError:
                # 如果键不存在，创建它
                key = winreg.CreateKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SOFTWARE\Microsoft\SQMClient"
                )
            
            # 设置MachineId值
            winreg.SetValueEx(key, "MachineId", 0, winreg.REG_SZ, new_guid)
            winreg.CloseKey(key)
            
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Windows MachineId 更新成功{Style.RESET_ALL}")
            return True
            
        except PermissionError:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 权限不足，需要管理员权限{Style.RESET_ALL}")
            return False
        except Exception as e:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 更新Windows MachineId失败: {str(e)}{Style.RESET_ALL}")
            return False

    def _update_macos_platform_uuid(self, new_ids):
        """更新macOS平台UUID"""
        try:
            uuid_file = "/var/root/Library/Preferences/SystemConfiguration/com.apple.platform.uuid.plist"
            if os.path.exists(uuid_file):
                # 使用sudo执行plutil命令
                cmd = f'sudo plutil -replace "UUID" -string "{new_ids["telemetry.macMachineId"]}" "{uuid_file}"'
                result = os.system(cmd)
                if result == 0:
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} macOS平台UUID更新成功{Style.RESET_ALL}")
                else:
                    raise Exception(f"执行plutil命令失败")
        except Exception as e:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 更新macOS平台UUID失败: {str(e)}{Style.RESET_ALL}")
            raise

    def update_machine_id_file(self, machine_id):
        """
        更新machineId文件
        """
        try:
            # 如果路径不存在，创建目录
            os.makedirs(os.path.dirname(self.machine_id_path), exist_ok=True)

            # 如果文件存在，创建备份
            if os.path.exists(self.machine_id_path):
                backup_path = self.machine_id_path + ".backup"
                try:
                    shutil.copy2(self.machine_id_path, backup_path)
                    print(f"{Fore.GREEN}{EMOJI['INFO']} 已创建备份文件: {backup_path}{Style.RESET_ALL}")
                except Exception as e:
                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法创建备份: {str(e)}{Style.RESET_ALL}")

            # 将新机器ID写入文件
            with open(self.machine_id_path, "w", encoding="utf-8") as f:
                f.write(machine_id)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 更新machineId文件成功{Style.RESET_ALL}")
            return True
        except Exception as e:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 更新machineId文件失败: {str(e)}{Style.RESET_ALL}")
            return False

    def reset_machine_ids(self):
        """重置机器ID并备份原文件"""
        try:
            print(f"{Fore.CYAN}{EMOJI['INFO']} 正在检查配置文件...{Style.RESET_ALL}")

            # 检查文件是否存在
            if not os.path.exists(self.db_path):
                print(
                    f"{Fore.RED}{EMOJI['ERROR']} 配置文件不存在: {self.db_path}{Style.RESET_ALL}"
                )
                return False

            # 检查文件权限
            if not os.access(self.db_path, os.R_OK | os.W_OK):
                print(
                    f"{Fore.RED}{EMOJI['ERROR']} 无法读写配置文件，请检查文件权限！{Style.RESET_ALL}"
                )
                print(
                    f"{Fore.RED}{EMOJI['ERROR']} 如果你使用过 go-cursor-help 来修改 ID; 请修改文件只读权限 {self.db_path} {Style.RESET_ALL}"
                )
                return False

            # 读取现有配置
            print(f"{Fore.CYAN}{EMOJI['FILE']} 读取当前配置...{Style.RESET_ALL}")
            with open(self.db_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            # 创建备份
            backup_path = self.db_path + ".bak"
            if not os.path.exists(backup_path):
                print(f"{Fore.YELLOW}{EMOJI['BACKUP']} 正在创建备份: {backup_path}{Style.RESET_ALL}")
                shutil.copy2(self.db_path, backup_path)
            else:
                print(f"{Fore.YELLOW}{EMOJI['INFO']} 备份文件已存在{Style.RESET_ALL}")

            # 生成新ID
            print(f"{Fore.CYAN}{EMOJI['RESET']} 生成新的机器标识...{Style.RESET_ALL}")
            new_ids = self.generate_new_ids()

            # 更新配置
            config.update(new_ids)

            # 保存新配置
            print(f"{Fore.CYAN}{EMOJI['FILE']} 保存新配置...{Style.RESET_ALL}")
            with open(self.db_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=4)

            # 更新SQLite数据库
            self.update_sqlite_db(new_ids)

            # 更新系统ID
            self.update_system_ids(new_ids)

            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 机器标识重置成功！{Style.RESET_ALL}")
            print(f"\n{Fore.CYAN}新的机器标识:{Style.RESET_ALL}")
            for key, value in new_ids.items():
                print(f"{EMOJI['INFO']} {key}: {Fore.GREEN}{value}{Style.RESET_ALL}")

            return True

        except PermissionError as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 权限错误: {str(e)}{Style.RESET_ALL}")
            print(
                f"{Fore.YELLOW}{EMOJI['INFO']} 请尝试以管理员身份运行此程序{Style.RESET_ALL}"
            )
            return False
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 重置过程出错: {str(e)}{Style.RESET_ALL}")
            return False


if __name__ == "__main__":
    print(f"\n{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['RESET']} Cursor 机器标识重置工具{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")

    resetter = MachineIDResetter()
    resetter.reset_machine_ids()

    print(f"\n{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    input(f"{EMOJI['INFO']} 按回车键退出...")
