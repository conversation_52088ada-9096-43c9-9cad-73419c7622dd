#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块分析工具 - 多线程版本
获取全部板块信息，计算5日线和10日线，找出5日线在10日线上方的板块

主要功能:
- 板块数据获取和分析
- 20线程并发处理，提高效率
- 简化的代理管理，动态替换
- 板块均线分析（5日线、10日线）
- 自动筛选5日线在10日线上方的板块
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import sys
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import os
from dotenv import load_dotenv

class JuliangProxyConfig:
    """
    巨量IP代理配置类
    参考 fund_analyzer.py 的实现方式
    """
    _env_loaded = False  # 标记是否已加载 .env 文件

    @classmethod
    def _load_env_file(cls):
        """加载 .env 文件"""
        if cls._env_loaded:
            return

        try:
            # 获取应用程序的根目录路径
            if getattr(sys, "frozen", False):
                # 如果是打包后的可执行文件
                application_path = os.path.dirname(sys.executable)
            else:
                # 如果是开发环境
                application_path = os.path.dirname(os.path.abspath(__file__))

            # 指定 .env 文件的路径
            dotenv_path = os.path.join(application_path, ".env")

            if os.path.exists(dotenv_path):
                # 加载 .env 文件
                load_dotenv(dotenv_path)
            else:
                print(f"⚠️ .env 文件不存在: {dotenv_path}")
                print("💡 提示: 您可以创建 .env 文件并配置 PROXY_URL")

            cls._env_loaded = True

        except Exception as e:
            print(f"❌ 加载 .env 文件失败: {e}")
            cls._env_loaded = True  # 即使失败也标记为已尝试

    @classmethod
    def get_proxy_url(cls):
        """获取巨量IP API地址"""
        # 确保已加载 .env 文件
        cls._load_env_file()

        # 直接从环境变量获取（包括从 .env 文件加载的）
        proxy_url = os.getenv("PROXY_URL")

        if proxy_url and "juliangip.com" in proxy_url:
            return proxy_url
        else:
            print("⚠️ 未在 .env 文件中找到有效的 PROXY_URL 配置")
            print("💡 提示：请在 .env 文件中设置 PROXY_URL")
            print("📝 .env 文件示例:")
            print("   PROXY_URL=https://api.juliangip.com/dynamic_proxy?neek=your_api_key&num=1&type=2&sep=1&regions=")
            return None

def get_juliang_proxy(max_retries=3, retry_delay=2):
    """
    从巨量IP获取一个新的代理，支持重试机制
    参考 fund_analyzer.py 的实现方式

    参数:
    - max_retries: 最大重试次数，默认3次
    - retry_delay: 重试间隔秒数，默认2秒

    如果重试失败，程序将退出运行
    """
    # 使用配置类获取代理URL
    proxy_url = JuliangProxyConfig.get_proxy_url()

    if not proxy_url:
        print("❌ 未配置巨量IP代理URL，程序无法继续运行")
        print("💡 请在 .env 文件中配置 PROXY_URL")
        return None

    # 主API重试逻辑
    for attempt in range(max_retries):
        try:
            response = requests.get(proxy_url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200 and data.get("data") and data["data"].get("proxy_list"):
                    proxy_info = data["data"]["proxy_list"][0]
                    proxy_address = proxy_info.split(',')[0]  # 提取 IP:端口 部分

                    # 确保代理地址有正确的格式
                    if not proxy_address.startswith(('http://', 'https://')):
                        proxy_address = f"http://{proxy_address}"

                    proxy_dict = {
                        'http': proxy_address,
                        'https': proxy_address
                    }
                    return proxy_dict
                else:
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
            else:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue

        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue

    # 所有重试都失败，返回None而不是退出程序（由代理池处理）
    return None

class ProxyPool:
    """
    简化的代理池管理类，动态获取和替换代理
    """
    def __init__(self):
        self.current_proxy = None
        self.lock = threading.Lock()
        self.proxy_creation_time = 0
        self.refresh_interval = 300  # 5分钟自动刷新代理
        
        # 初始化获取第一个代理
        self._initialize_pool()

    def _initialize_pool(self):
        """简化的代理池初始化，只获取一个代理"""
        print("🔄 获取代理...")
        
        proxy = get_juliang_proxy(max_retries=3, retry_delay=1)
        if proxy:
            with self.lock:
                self.current_proxy = proxy
                self.proxy_creation_time = time.time()
            print(f"✅ 代理获取成功: {proxy.get('http', 'Unknown')[:30]}...")
        else:
            print("❌ 代理获取失败，程序无法继续")
            sys.exit(1)

    def get_proxy(self):
        """获取当前代理"""
        with self.lock:
            # 检查代理是否过期（5分钟自动刷新）
            current_time = time.time()
            if current_time - self.proxy_creation_time > self.refresh_interval:
                self._refresh_proxy()
            
            return self.current_proxy

    def get_different_proxy(self, exclude_proxy=None):
        """
        获取一个新的代理（用于重试时替换当前代理）
        """
        return self._get_new_proxy()
    
    def _get_new_proxy(self):
        """获取一个新的代理"""
        print("🔄 获取新代理...")
        proxy = get_juliang_proxy(max_retries=3, retry_delay=1)
        if proxy:
            with self.lock:
                self.current_proxy = proxy
                self.proxy_creation_time = time.time()
            print(f"✅ 新代理获取成功: {proxy.get('http', 'Unknown')[:30]}...")
            return proxy
        else:
            print("❌ 新代理获取失败")
            return None
    
    def _refresh_proxy(self):
        """刷新当前代理"""
        print("🔄 代理已过期，正在刷新...")
        proxy = get_juliang_proxy(max_retries=3, retry_delay=1)
        if proxy:
            self.current_proxy = proxy
            self.proxy_creation_time = time.time()
            print(f"✅ 代理刷新成功: {proxy.get('http', 'Unknown')[:30]}...")
        else:
            print("❌ 代理刷新失败，继续使用旧代理")
    
    def replace_proxy_on_failure(self):
        """在请求失败时替换代理"""
        return self._get_new_proxy()
    
    def record_proxy_performance(self, proxy, success, response_time=None, is_network_error=False):
        """记录代理性能数据（简化版本）"""
        # 如果是网络错误，不做任何记录，让调用方处理代理替换
        pass

    def get_pool_status(self):
        """获取代理池状态"""
        with self.lock:
            if self.current_proxy:
                age = time.time() - self.proxy_creation_time
                return [{
                    'proxy': self.current_proxy.get('http', 'Unknown')[:30] + '...',
                    'age_seconds': age,
                    'is_fresh': age < self.refresh_interval,
                }]
            else:
                return []

def process_sector_analysis(sector, proxy_pool, result_queue, thread_id, sector_index, total_sectors):
    """
    多线程处理单个板块分析的函数，支持动态代理替换和详细日志
    
    参数：
    - sector: 板块信息字典
    - proxy_pool: 代理池对象
    - result_queue: 结果队列
    - thread_id: 线程标识
    - sector_index: 当前板块序号
    - total_sectors: 总板块数
    """
    max_proxy_retries = 3  # 最多尝试3个不同的代理
    
    try:
        sector_code = sector['code']
        sector_name = sector['name']
        
        # 显示开始分析的日志
        print(f"📈 [{sector_index}/{total_sectors}] 开始分析板块: {sector_name} ({sector_code}) [线程: {thread_id}]")
        
        # 🔄 代理重试循环
        analysis_result = None
        last_error = None
        
        for retry_attempt in range(max_proxy_retries):
            # 从代理池获取代理
            proxy = proxy_pool.get_proxy()
            
            # 🛑 检查代理有效性
            if not proxy or not proxy.get('http'):
                last_error = f"代理获取失败，板块{sector_code}无法处理"
                continue
            
            try:
                # 创建专用的SectorAnalyzer实例，传入代理配置
                analyzer = SectorAnalyzer(proxy=proxy, thread_safe=True)
                
                # 记录开始时间用于性能跟踪
                start_time = time.time()
                
                # 分析板块均线情况
                analysis_result = analyzer.analyze_sector(sector)
                
                # 记录成功时间
                response_time = time.time() - start_time
                
                # 🎯 检查获取结果
                if analysis_result:
                    # 成功获取数据，跳出重试循环
                    if retry_attempt > 0:
                        print(f"✅ [{sector_index}/{total_sectors}] 板块{sector_code}在第{retry_attempt+1}次尝试成功获取数据 [线程: {thread_id}]")
                    break
                else:
                    last_error = f"板块{sector_code}分析失败 - 返回空结果"
                    
                    # 如果不是最后一次尝试，继续重试
                    if retry_attempt < max_proxy_retries - 1:
                        print(f"🔄 [{sector_index}/{total_sectors}] 板块分析返回空结果，正在重试: {sector_name} ({sector_code}) [线程: {thread_id}]")
                        time.sleep(1)  # 短暂延迟避免频繁请求
                        continue
                        
            except Exception as e:
                last_error = f"板块{sector_code}发生异常: {e}"
                
                # 检查是否为代理相关的网络错误
                error_msg = str(e).lower()
                is_network_error = any(keyword in error_msg for keyword in [
                    'timeout', 'connection', 'proxy', 'httperror', 'network',
                    'unreachable', 'refused', 'reset', 'read timed out', 'ssl',
                    'proxyerror', 'remotedisconnected', 'max retries exceeded'
                ])
                
                # 如果是网络错误，尝试替换代理
                if is_network_error:
                    print(f"🔄 [{sector_index}/{total_sectors}] 板块网络错误，正在替换代理: {sector_name} ({sector_code}) [线程: {thread_id}]")
                    new_proxy = proxy_pool.replace_proxy_on_failure()
                    if not new_proxy:
                        print(f"❌ [{sector_index}/{total_sectors}] 替换代理失败，板块{sector_code}无法继续 [线程: {thread_id}]")
                        break
                
                # 如果不是最后一次尝试，继续重试
                if retry_attempt < max_proxy_retries - 1:
                    print(f"🔄 [{sector_index}/{total_sectors}] 板块处理异常，正在重试: {sector_name} ({sector_code}) [线程: {thread_id}]")
                    time.sleep(1)  # 短暂延迟避免频繁请求
                    continue
        
        # 记录分析结果并显示结果日志
        if analysis_result:
            ma5_above_ma10 = "是" if analysis_result['ma5_above_ma10'] else "否"
            golden_cross_today = "是" if analysis_result['is_golden_cross_today'] else "否"
            print(f"✅ [{sector_index}/{total_sectors}] 板块分析成功: {sector_name} ({sector_code}) - 5日线>10日线: {ma5_above_ma10} - 今日上穿: {golden_cross_today} [线程: {thread_id}]")
            
            result_queue.put({
                'sector_code': sector_code,
                'sector_name': sector_name,
                'success': True,
                'error': None,
                'result': analysis_result,
                'sector_index': sector_index
            })
        else:
            error_msg = f"板块{sector_code}尝试{max_proxy_retries}个代理后仍失败 - {last_error}"
            print(f"❌ [{sector_index}/{total_sectors}] 板块分析失败: {sector_name} ({sector_code}) - {last_error} [线程: {thread_id}]")
            
            result_queue.put({
                'sector_code': sector_code,
                'sector_name': sector_name,
                'success': False,
                'error': error_msg,
                'result': None,
                'sector_index': sector_index
            })
        
    except Exception as e:
        error_msg = f"板块{sector['code']}处理严重异常: {e}"
        print(f"🛑 [{sector_index}/{total_sectors}] {error_msg} [线程: {thread_id}]")
        
        result_queue.put({
            'sector_code': sector['code'],
            'sector_name': sector['name'],
            'success': False,
            'error': error_msg,
            'result': None,
            'sector_index': sector_index
        })

class SectorAnalyzer:
    def __init__(self, proxy=None, thread_safe=True):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # 代理配置
        self.proxy = proxy
        if proxy:
            self.session.proxies.update(proxy)

        # 多线程环境下的智能频率控制
        self.thread_safe = thread_safe
        if thread_safe:
            self.lock = threading.Lock()

        self.last_request_time = 0
        self.success_count = 0   # 连续成功次数
        self.error_count = 0     # 连续错误次数
        self.base_delay = 0.1    # 基础延迟（多线程环境下）
        self.max_delay = 0.5     # 最大延迟（多线程环境下）

    def _calculate_smart_delay(self):
        """
        智能计算请求延迟时间
        - 连续成功时减少延迟
        - 出现错误时增加延迟
        - 添加随机性避免规律性请求
        """
        # 基于成功/失败情况调整延迟
        if self.error_count > 0:
            # 有错误时增加延迟
            delay_factor = 1 + (self.error_count * 0.2)
            base_delay = min(self.base_delay * delay_factor, self.max_delay)
        elif self.success_count > 5:
            # 连续成功5次以上时减少延迟
            delay_factor = max(0.5, 1 - (self.success_count - 5) * 0.05)
            base_delay = self.base_delay * delay_factor
        else:
            base_delay = self.base_delay

        # 添加随机性，避免规律性请求
        random_factor = random.uniform(0.7, 1.3)
        final_delay = base_delay * random_factor

        return max(0.05, min(final_delay, self.max_delay))

    def _handle_delay_logic(self, current_time):
        """处理延迟逻辑（线程安全）"""
        if self.last_request_time > 0:
            elapsed = current_time - self.last_request_time
            smart_delay = self._calculate_smart_delay()
            if elapsed < smart_delay:
                return smart_delay - elapsed
        return 0

    def run_analysis_concurrent(self, max_workers=20):
        """
        运行完整的多线程并发分析（默认20线程）
        """
        # 记录开始时间
        start_time = time.time()
        
        print("🚀 开始多线程并发板块均线分析...")
        print("=" * 60)
        
        # 🚀 初始化简化的代理池
        proxy_pool = ProxyPool()
        
        # 获取所有板块
        sectors = self.get_all_sectors()
        if not sectors:
            print("无法获取板块数据，程序退出")
            return
        
        print(f"\n开始多线程分析 {len(sectors)} 个板块的均线情况...")
        print(f"使用 {max_workers} 个线程并发处理")
        
        # 优化并发配置
        result_queue = queue.Queue()
        all_results = []
        
        # 使用线程池执行并发任务
        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="SectorWorker") as executor:
            # 为每个板块提交任务，传入板块索引和总数
            future_to_sector = {}
            
            for i, sector in enumerate(sectors, 1):
                # 传入代理池、结果队列、线程标识、板块索引和总数
                future = executor.submit(
                    process_sector_analysis, 
                    sector, 
                    proxy_pool, 
                    result_queue, 
                    f"Worker-{(i-1) % max_workers + 1}",  # 简化线程名
                    i,  # 板块索引
                    len(sectors)  # 总板块数
                )
                future_to_sector[future] = (sector, i)
            
            # 等待所有任务完成
            print(f"⏳ 已提交 {len(sectors)} 个分析任务，等待完成...\n")
            
            completed_sectors = 0
            for future in as_completed(future_to_sector):
                sector, sector_index = future_to_sector[future]
                completed_sectors += 1
                
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                except Exception as e:
                    # 只显示关键错误
                    print(f"🛑 板块{sector['code']}任务异常: {e}")
        
        # 收集所有结果
        print(f"\n📈 收集分析结果...")
        
        while not result_queue.empty():
            result = result_queue.get()
            all_results.append(result)
        
        # 按原始顺序排序结果
        all_results.sort(key=lambda x: x.get('sector_index', 0))
        
        # 统计结果
        successful_results = [r for r in all_results if r['success'] and r['result']]
        failed_results = [r for r in all_results if not r['success']]
        ma5_above_ma10_sectors = [r for r in successful_results if r['result']['ma5_above_ma10']]
        golden_cross_today_sectors = [r for r in successful_results if r['result']['is_golden_cross_today']]
        
        # 计算总耗时
        total_time = time.time() - start_time
        total_time_str = f"{int(total_time // 60)}分{int(total_time % 60)}秒" if total_time >= 60 else f"{total_time:.1f}秒"
        
        # 显示统计信息
        print(f"\n📈 分析统计:")
        print(f"  总板块数: {len(sectors)}")
        print(f"  成功分析: {len(successful_results)}")
        print(f"  分析失败: {len(failed_results)}")
        print(f"  5日线在10日线上方: {len(ma5_above_ma10_sectors)}")
        print(f"  今日刚刚上穿: {len(golden_cross_today_sectors)}")
        print(f"  总耗时: {total_time_str}")
        
        # 显示失败的板块（如果有的话）
        if failed_results:
            print(f"\n⚠️ 分析失败的板块 ({len(failed_results)}个):")
            for result in failed_results[:5]:  # 最多显示5个
                print(f"  {result['sector_code']} - {result['sector_name']}: {result['error']}")
            if len(failed_results) > 5:
                print(f"  ... 还有 {len(failed_results) - 5} 个失败的板块")
        
        # 显示所有板块信息
        self._print_all_sectors_info(successful_results)
        
        # 显示5日线在10日线上方的板块
        self._print_ma5_above_ma10_sectors(ma5_above_ma10_sectors)
        
        # 显示今日刚刚上穿的板块
        self._print_golden_cross_today_sectors(golden_cross_today_sectors)
        
        print(f"\n🎉 多线程并发分析完成！")
        print(f"  共分析了 {len(successful_results)} 个板块，其中 {len(ma5_above_ma10_sectors)} 个板块的5日线在10日线上方。")
        print(f"  今日刚刚上穿的板块: {len(golden_cross_today_sectors)} 个")
        print(f"  分析时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  总耗时：{total_time_str}")
        
        return successful_results, ma5_above_ma10_sectors, golden_cross_today_sectors
    
    def _print_all_sectors_info(self, successful_results):
        """显示所有板块信息"""
        print("\n" + "=" * 90)
        print("所有板块信息")
        print("=" * 90)
        print(f"{'序号':<4} {'板块代码':<10} {'板块名称':<15} {'当前价格':<10} {'5日线':<10} {'10日线':<10} {'5>10':<6} {'今日上穿':<8} {'涨跌幅%':<8} {'均线差%':<8}")
        print("-" * 90)
        
        for i, result_item in enumerate(successful_results, 1):
            result = result_item['result']
            ma5_above = "是" if result['ma5_above_ma10'] else "否"
            golden_cross = "是" if result['is_golden_cross_today'] else "否"
            print(f"{i:<4} {result['code']:<10} {result['name']:<15} {result['current_price']:<10.2f} "
                  f"{result['ma5']:<10.2f} {result['ma10']:<10.2f} {ma5_above:<6} {golden_cross:<8} "
                  f"{result['change_pct']:<8.2f} {result['ma_diff_pct']:<8.2f}")
    
    def _print_ma5_above_ma10_sectors(self, ma5_above_ma10_sectors):
        """显示5日线在10日线上方的板块"""
        print("\n" + "=" * 90)
        print("5日线在10日线上方的板块列表")
        print("=" * 90)
        
        if ma5_above_ma10_sectors:
            # 按均线差值排序
            ma5_above_ma10_sectors.sort(key=lambda x: x['result']['ma_diff_pct'], reverse=True)
            
            print(f"共找到 {len(ma5_above_ma10_sectors)} 个符合条件的板块：")
            print(f"{'序号':<4} {'板块代码':<10} {'板块名称':<15} {'当前价格':<10} {'5日线':<10} {'10日线':<10} {'今日上穿':<8} {'涨跌幅%':<8} {'均线差%':<8}")
            print("-" * 90)
            
            for i, result_item in enumerate(ma5_above_ma10_sectors, 1):
                result = result_item['result']
                golden_cross = "是" if result['is_golden_cross_today'] else "否"
                print(f"{i:<4} {result['code']:<10} {result['name']:<15} {result['current_price']:<10.2f} "
                      f"{result['ma5']:<10.2f} {result['ma10']:<10.2f} {golden_cross:<8} "
                      f"{result['change_pct']:<8.2f} {result['ma_diff_pct']:<8.2f}")
        else:
            print("未找到5日线在10日线上方的板块")
    
    def _print_golden_cross_today_sectors(self, golden_cross_today_sectors):
        """显示今日刚刚上穿的板块"""
        print("\n" + "=" * 90)
        print("今日刚刚发生5日线上穿10日线的板块列表")
        print("=" * 90)
        
        if golden_cross_today_sectors:
            # 按均线差值排序
            golden_cross_today_sectors.sort(key=lambda x: x['result']['ma_diff_pct'], reverse=True)
            
            print(f"共找到 {len(golden_cross_today_sectors)} 个今日刚刚上穿的板块：")
            print(f"{'序号':<4} {'板块代码':<10} {'板块名称':<15} {'当前价格':<10} {'5日线':<10} {'10日线':<10} {'涨跌幅%':<8} {'均线差%':<8}")
            print("-" * 90)
            
            for i, result_item in enumerate(golden_cross_today_sectors, 1):
                result = result_item['result']
                print(f"{i:<4} {result['code']:<10} {result['name']:<15} {result['current_price']:<10.2f} "
                      f"{result['ma5']:<10.2f} {result['ma10']:<10.2f} "
                      f"{result['change_pct']:<8.2f} {result['ma_diff_pct']:<8.2f}")
        else:
            print("未找到今日刚刚上穿的板块")
        
    def get_sectors_by_type(self, sector_type, max_pages=10):
        """根据类型获取板块列表，支持分页获取所有数据，带智能延迟和错误处理"""
        url = "https://push2.eastmoney.com/api/qt/clist/get"
        all_sectors = []
        page = 1

        while page <= max_pages:
            params = {
                'fs': sector_type,
                'fields': 'f12,f14,f2,f3,f62,f184,f66,f69,f72,f75,f78,f81,f84,f87,f204,f205,f124',
                'pn': page,
                'pz': 100,  # 每页100个
                'po': 1,
                'np': 1,
                'fltt': 2,
                'invt': 2,
                'fid': 'f3'
            }

            try:
                # 线程安全的智能延迟控制
                current_time = time.time()

                if self.thread_safe:
                    with self.lock:
                        delay_needed = self._handle_delay_logic(current_time)
                else:
                    delay_needed = self._handle_delay_logic(current_time)

                if delay_needed > 0:
                    time.sleep(delay_needed)

                self.last_request_time = time.time()

                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()
                data = response.json()

                if data.get('rc') == 0 and data.get('data') and data['data'].get('diff'):
                    page_sectors = []
                    for item in data['data']['diff']:
                        sector = {
                            'code': item.get('f12', ''),  # 板块代码
                            'name': item.get('f14', ''),  # 板块名称
                            'price': item.get('f2', 0),   # 当前价格
                            'change_pct': item.get('f3', 0),  # 涨跌幅
                            'volume': item.get('f5', 0),      # 成交量
                            'amount': item.get('f6', 0),      # 成交额
                        }
                        if sector['code'] and sector['name']:
                            page_sectors.append(sector)

                    if len(page_sectors) == 0:
                        # 如果当前页没有数据，说明已经到最后一页了
                        break

                    all_sectors.extend(page_sectors)

                    # 请求成功，更新计数器
                    if self.thread_safe:
                        with self.lock:
                            self.success_count += 1
                            self.error_count = 0  # 重置错误计数
                    else:
                        self.success_count += 1
                        self.error_count = 0

                    # 如果当前页数据少于100个，说明是最后一页
                    if len(page_sectors) < 100:
                        break

                    page += 1
                else:
                    break

            except Exception as e:
                # 请求失败，更新计数器
                if self.thread_safe:
                    with self.lock:
                        self.error_count += 1
                        self.success_count = 0  # 重置成功计数
                else:
                    self.error_count += 1
                    self.success_count = 0

                print(f"获取第{page}页板块列表失败：{e}")
                break

        return all_sectors

    def get_all_sectors(self):
        """获取所有板块列表（包括行业板块和概念板块）"""
        print("正在获取板块列表...")

        all_sectors = []

        # 获取行业板块（1页即可，共86个）
        print("正在获取行业板块...")
        industry_sectors = self.get_sectors_by_type('m:90+t:2', max_pages=1)
        all_sectors.extend(industry_sectors)
        print(f"获取到 {len(industry_sectors)} 个行业板块")

        # 获取概念板块（需要5页，共435个）
        print("正在获取概念板块...")
        concept_sectors = self.get_sectors_by_type('m:90+t:3', max_pages=5)
        all_sectors.extend(concept_sectors)
        print(f"获取到 {len(concept_sectors)} 个概念板块")

        # 检查是否包含军工相关板块
        military_count = 0
        for sector in all_sectors:
            if any(keyword in sector['name'] for keyword in ['军工', '军民融合', '通用航空', '商业航天', '航母', '国防']):
                military_count += 1

        print(f"其中包含 {military_count} 个军工相关板块")
        print(f"总共获取 {len(all_sectors)} 个板块")
        return all_sectors
    
    def get_sector_kline(self, sector_code, days=30):
        """获取板块K线数据，带智能延迟和错误处理"""
        url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            'secid': f'90.{sector_code}',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': 101,  # 日K线
            'fqt': 1,    # 复权类型
            'beg': 0,
            'end': 20500101,
            'lmt': days + 20  # 多获取一些数据以确保有足够的数据计算均线
        }

        try:
            # 线程安全的智能延迟控制
            current_time = time.time()

            if self.thread_safe:
                with self.lock:
                    delay_needed = self._handle_delay_logic(current_time)
            else:
                delay_needed = self._handle_delay_logic(current_time)

            if delay_needed > 0:
                time.sleep(delay_needed)

            self.last_request_time = time.time()

            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get('rc') == 0 and data.get('data') and data['data'].get('klines'):
                klines = []
                for line in data['data']['klines']:
                    parts = line.split(',')
                    if len(parts) >= 6:
                        klines.append({
                            'date': parts[0],
                            'open': float(parts[1]),
                            'close': float(parts[2]),
                            'high': float(parts[3]),
                            'low': float(parts[4]),
                            'volume': int(parts[5]) if parts[5].isdigit() else 0,
                            'amount': float(parts[6]) if len(parts) > 6 else 0
                        })

                # 请求成功，更新计数器
                if self.thread_safe:
                    with self.lock:
                        self.success_count += 1
                        self.error_count = 0  # 重置错误计数
                else:
                    self.success_count += 1
                    self.error_count = 0

                # 按日期排序，最新的在前面
                klines.sort(key=lambda x: x['date'], reverse=True)
                return klines[:days] if len(klines) > days else klines
            else:
                return []

        except Exception as e:
            # 请求失败，更新计数器
            if self.thread_safe:
                with self.lock:
                    self.error_count += 1
                    self.success_count = 0  # 重置成功计数
            else:
                self.error_count += 1
                self.success_count = 0

            print(f"获取板块 {sector_code} K线数据失败：{e}")
            return []
    
    def calculate_ma(self, prices, period):
        """计算移动平均线"""
        if len(prices) < period:
            return None
        return sum(prices[:period]) / period
    
    def analyze_sector(self, sector):
        """分析单个板块的均线情况"""
        code = sector['code']
        name = sector['name']
        
        # 获取K线数据
        klines = self.get_sector_kline(code, days=30)
        if len(klines) < 10:
            return None
        
        # 提取收盘价
        closes = [k['close'] for k in klines]
        
        # 计算5日线和10日线
        ma5 = self.calculate_ma(closes, 5)
        ma10 = self.calculate_ma(closes, 10)
        
        if ma5 is None or ma10 is None:
            return None
        
        # 当前价格
        current_price = closes[0]
        
        # 判断5日线是否在10日线上方
        ma5_above_ma10 = ma5 > ma10
        
        # 检查是否刚刚发生上穿（今天刚刚发生）
        is_golden_cross_today = False
        if len(closes) >= 11:  # 需要足够的数据来计算前一日的均线
            # 计算前一日的5日线和10日线
            prev_closes = closes[1:]  # 去掉今天的数据
            prev_ma5 = self.calculate_ma(prev_closes, 5)
            prev_ma10 = self.calculate_ma(prev_closes, 10)
            
            if prev_ma5 is not None and prev_ma10 is not None:
                # 检查是否从昨天的5日线<10日线变为今天的5日线>10日线
                prev_ma5_below_ma10 = prev_ma5 <= prev_ma10
                current_ma5_above_ma10 = ma5 > ma10
                is_golden_cross_today = prev_ma5_below_ma10 and current_ma5_above_ma10
        
        return {
            'code': code,
            'name': name,
            'current_price': current_price,
            'ma5': ma5,
            'ma10': ma10,
            'ma5_above_ma10': ma5_above_ma10,
            'is_golden_cross_today': is_golden_cross_today,
            'change_pct': sector.get('change_pct', 0),
            'ma_diff_pct': ((ma5 - ma10) / ma10 * 100) if ma10 != 0 else 0
        }

def main():
    """主函数，使用20线程多线程并发分析"""
    print("🚀 板块均线分析工具 - 多线程并发模式（20个线程）")
    print("=" * 60)
    
    analyzer = SectorAnalyzer()
    analyzer.run_analysis_concurrent(max_workers=20)

if __name__ == "__main__":
    main()