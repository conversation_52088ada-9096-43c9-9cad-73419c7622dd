#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF技术指标突破检测器
检测今天发生5日线上穿10日线或下穿布林带下轨的ETF并导出为Excel
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
import numpy as np

class ETFBreakthroughDetector:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://money.finance.sina.com.cn/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })
        self.lock = threading.Lock()
        
    def get_etf_list_from_sina(self):
        """从新浪财经获取ETF列表（包含完整基金信息）"""
        url = "https://quotes.sina.com.cn/cn/api/openapi.php/CN_ETFService.getETFRankList"
        params = {
            'pageIndex': 1,
            'pageSize': 2000,
            'asc': 0,
            'sort': 'change',
            'filter': '',
            'type': 'all'
        }
        
        try:
            print("正在获取ETF列表...")
            response = self.session.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            data = response.json()
            if 'result' in data and 'data' in data['result']:
                result_data = data['result']['data']
                if 'data' in result_data:
                    etf_list = result_data['data']
                else:
                    etf_list = result_data
                
                print(f"获取到 {len(etf_list)} 个ETF")
                return etf_list
            else:
                print(f"API返回格式异常")
                return []
                
        except Exception as e:
            print(f"获取ETF列表失败: {e}")
            return []
    
    def parse_etf_basic_info(self, etf):
        """解析ETF基本信息（与原来etf_to_excel.py相同的格式）"""
        def safe_float(value, default=0):
            if value == '--' or value is None or value == '':
                return default
            try:
                return float(value)
            except:
                return default
        
        etf_info = {
            '基金代码': etf.get('symbol', ''),
            '基金名称': etf.get('name', ''),
            '现价': safe_float(etf.get('price')),
            '涨跌幅(%)': safe_float(etf.get('change')),
            '成交额': safe_float(etf.get('amount')),
            '换手率(%)': safe_float(etf.get('change_speed')),
            '溢价率(%)': safe_float(etf.get('premium_rate')) if etf.get('premium_rate') != '--' else '',
            '净申购': safe_float(etf.get('net_subscription')),
            '净买入': safe_float(etf.get('net_purchase')),
            '总规模': safe_float(etf.get('total_scale')),
            '单位净值': safe_float(etf.get('net_unit_value')),
            '增长率(%)': safe_float(etf.get('increase_rate')),
            '最大回撤': safe_float(etf.get('maximum_pullback')) if etf.get('maximum_pullback') != '--' else '',
            '夏普比率': safe_float(etf.get('sharpe_ratio')),
            '跟踪误差': safe_float(etf.get('tracking_error')) if etf.get('tracking_error') != '--' else '',
            '基金经理': etf.get('fund_manager', ''),
            '托管人': etf.get('custodian', ''),
            '评级': etf.get('grade', ''),
        }
        
        # 处理标志位
        flags = etf.get('flags', {})
        if flags:
            etf_info['融资融券'] = '是' if flags.get('rzrq', 0) == 1 else '否'
            etf_info['沪深港通'] = '是' if flags.get('hs_gt', 0) == 1 else '否'
            etf_info['T+0交易'] = '是' if flags.get('t0', 0) == 1 else '否'
        
        return etf_info
    
    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """计算布林带"""
        if len(prices) < period:
            return None, None, None
        
        # 计算移动平均线
        sma = np.mean(prices[-period:])
        
        # 计算标准差
        std = np.std(prices[-period:])
        
        # 计算布林带上轨和下轨
        upper_band = sma + (std_dev * std)
        lower_band = sma - (std_dev * std)
        
        return upper_band, sma, lower_band
    
    def get_etf_kline_data(self, symbol, days=30):
        """获取单个ETF的K线数据（包含均线）"""
        # 确保symbol格式正确
        if not symbol.startswith(('sh', 'sz')):
            if symbol.startswith('5') or symbol.startswith('1'):
                if symbol.startswith('5'):
                    symbol = f'sh{symbol}'
                else:
                    symbol = f'sz{symbol}'
        
        url = "http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData"
        params = {
            'symbol': symbol,
            'scale': '240',  # 日线
            'ma': '5,10',    # 5日和10日均线
            'datalen': days  # 获取最近30天数据
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data and len(data) > 0:
                return {
                    'symbol': symbol,
                    'data': data,
                    'success': True
                }
            else:
                return {'symbol': symbol, 'success': False, 'error': 'No data'}
                
        except Exception as e:
            return {'symbol': symbol, 'success': False, 'error': str(e)}
    
    def detect_ma_crossover(self, kline_data):
        """检测5日线上穿10日线（今天发生）"""
        if not kline_data or len(kline_data) < 2:
            return False, None
        
        # 获取最新两天的数据
        latest_day = kline_data[-1]  # 今天
        previous_day = kline_data[-2]  # 昨天
        
        try:
            # 今天的均线数据
            today_ma5 = float(latest_day.get('ma_price5', 0))
            today_ma10 = float(latest_day.get('ma_price10', 0))
            
            # 昨天的均线数据
            yesterday_ma5 = float(previous_day.get('ma_price5', 0))
            yesterday_ma10 = float(previous_day.get('ma_price10', 0))
            
            # 检测上穿条件：
            # 1. 昨天：5日线 <= 10日线
            # 2. 今天：5日线 > 10日线
            crossover_detected = (yesterday_ma5 <= yesterday_ma10) and (today_ma5 > today_ma10)
            
            if crossover_detected:
                crossover_info = {
                    'signal_type': '5日线上穿10日线',
                    'date': latest_day.get('day'),
                    'today_ma5': today_ma5,
                    'today_ma10': today_ma10,
                    'yesterday_ma5': yesterday_ma5,
                    'yesterday_ma10': yesterday_ma10,
                    'close_price': float(latest_day.get('close', 0)),
                    'change_percent': ((float(latest_day.get('close', 0)) - float(previous_day.get('close', 0))) / float(previous_day.get('close', 1))) * 100,
                    'volume': latest_day.get('volume', 0)
                }
                return True, crossover_info
            
            return False, None
            
        except (ValueError, KeyError) as e:
            return False, None
    
    def detect_bollinger_breakout(self, kline_data):
        """检测下穿布林带下轨（今天发生）"""
        if not kline_data or len(kline_data) < 22:  # 需要足够的数据计算布林带
            return False, None
        
        try:
            # 获取最新两天的数据
            latest_day = kline_data[-1]  # 今天
            previous_day = kline_data[-2]  # 昨天
            
            # 获取收盘价数组用于计算布林带
            prices = [float(day.get('close', 0)) for day in kline_data]
            
            # 计算昨天的布林带
            yesterday_prices = prices[:-1]
            yesterday_upper, yesterday_middle, yesterday_lower = self.calculate_bollinger_bands(yesterday_prices)
            
            # 计算今天的布林带
            today_upper, today_middle, today_lower = self.calculate_bollinger_bands(prices)
            
            if yesterday_lower is None or today_lower is None:
                return False, None
            
            # 获取价格
            today_close = float(latest_day.get('close', 0))
            yesterday_close = float(previous_day.get('close', 0))
            
            # 检测下穿条件：
            # 1. 昨天：收盘价 >= 布林带下轨
            # 2. 今天：收盘价 < 布林带下轨
            breakout_detected = (yesterday_close >= yesterday_lower) and (today_close < today_lower)
            
            if breakout_detected:
                breakout_info = {
                    'signal_type': '下穿布林带下轨',
                    'date': latest_day.get('day'),
                    'today_close': today_close,
                    'today_lower_band': today_lower,
                    'yesterday_close': yesterday_close,
                    'yesterday_lower_band': yesterday_lower,
                    'today_middle_band': today_middle,
                    'close_price': today_close,
                    'change_percent': ((today_close - yesterday_close) / yesterday_close) * 100,
                    'volume': latest_day.get('volume', 0)
                }
                return True, breakout_info
            
            return False, None
            
        except (ValueError, KeyError) as e:
            return False, None
    
    def process_single_etf(self, etf):
        """处理单个ETF的检测"""
        symbol = etf.get('symbol', '')
        
        if not symbol:
            return None
        
        # 获取ETF基本信息
        basic_info = self.parse_etf_basic_info(etf)
        
        # 获取K线数据
        kline_result = self.get_etf_kline_data(symbol)
        
        if not kline_result['success']:
            return None
        
        # 检测5日线上穿10日线
        is_ma_crossover, ma_info = self.detect_ma_crossover(kline_result['data'])
        
        # 检测布林带下轨突破
        is_boll_breakout, boll_info = self.detect_bollinger_breakout(kline_result['data'])
        
        # 如果任一条件满足，返回结果
        if is_ma_crossover or is_boll_breakout:
            result = basic_info.copy()  # 包含所有基本信息
            
            if is_ma_crossover:
                # 添加均线上穿相关信息
                result.update({
                    '信号类型': ma_info['signal_type'],
                    '检测日期': ma_info['date'],
                    '今日5日均线': round(ma_info['today_ma5'], 3),
                    '今日10日均线': round(ma_info['today_ma10'], 3),
                    '昨日5日均线': round(ma_info['yesterday_ma5'], 3),
                    '昨日10日均线': round(ma_info['yesterday_ma10'], 3),
                    '布林带下轨': '',
                    '昨日布林带下轨': '',
                    '匹配原因': f"5日线({ma_info['today_ma5']:.3f})上穿10日线({ma_info['today_ma10']:.3f})"
                })
            elif is_boll_breakout:
                # 添加布林带突破相关信息
                result.update({
                    '信号类型': boll_info['signal_type'],
                    '检测日期': boll_info['date'],
                    '今日5日均线': '',
                    '今日10日均线': '',
                    '昨日5日均线': '',
                    '昨日10日均线': '',
                    '布林带下轨': round(boll_info['today_lower_band'], 3),
                    '昨日布林带下轨': round(boll_info['yesterday_lower_band'], 3),
                    '匹配原因': f"收盘价({boll_info['today_close']:.3f})下穿布林带下轨({boll_info['today_lower_band']:.3f})"
                })
            
            return result
        
        return None
    
    def detect_breakthroughs_batch(self, etf_list, max_workers=20):
        """批量检测ETF技术指标突破"""
        breakthrough_results = []
        total_count = len(etf_list)
        processed_count = 0
        
        print(f"开始检测 {total_count} 个ETF的技术指标突破情况...")
        print("检测条件: 1) 5日线上穿10日线  2) 下穿布林带下轨")
        print(f"使用 {max_workers} 个线程并发处理")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_etf = {executor.submit(self.process_single_etf, etf): etf for etf in etf_list}
            
            # 收集结果
            for future in as_completed(future_to_etf):
                etf = future_to_etf[future]
                processed_count += 1
                
                try:
                    result = future.result()
                    if result:
                        breakthrough_results.append(result)
                        print(f"✅ [{processed_count}/{total_count}] 发现突破: {result['基金代码']} - {result['基金名称']} ({result['信号类型']})")
                    else:
                        if processed_count % 50 == 0:  # 每50个显示一次进度
                            print(f"📊 [{processed_count}/{total_count}] 处理进度: {processed_count/total_count*100:.1f}%")
                            
                except Exception as e:
                    print(f"❌ [{processed_count}/{total_count}] {etf.get('symbol', 'Unknown')}: {str(e)}")
                
                # 避免请求过快
                time.sleep(0.01)
        
        print(f"\n检测完成！共发现 {len(breakthrough_results)} 个ETF发生技术指标突破")
        return breakthrough_results
    
    def export_to_excel(self, breakthrough_data, filename=None):
        """导出检测结果到Excel"""
        if not breakthrough_data:
            print("没有发现任何技术指标突破的ETF")
            return None
        
        if filename is None:
            today = datetime.now().strftime('%Y%m%d')
            import os
            downloads_path = os.path.expanduser("~/Downloads")
            filename = f"{downloads_path}/ETF_技术指标突破_{today}.xlsx"
        
        try:
            df = pd.DataFrame(breakthrough_data)
            
            # 按信号类型和涨跌幅排序
            df = df.sort_values(['信号类型', '涨跌幅(%)'], ascending=[True, False])
            
            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='技术指标突破ETF', index=False)
                
                # 获取工作表以设置格式
                worksheet = writer.sheets['技术指标突破ETF']
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"数据已导出到: {filename}")
            print(f"共导出 {len(df)} 个ETF")
            
            # 显示统计信息
            print(f"\n📈 统计信息:")
            ma_crossover_count = len(df[df['信号类型'] == '5日线上穿10日线'])
            boll_breakout_count = len(df[df['信号类型'] == '下穿布林带下轨'])
            print(f"5日线上穿10日线: {ma_crossover_count} 个")
            print(f"下穿布林带下轨: {boll_breakout_count} 个")
            print(f"上涨ETF: {len(df[df['涨跌幅(%)'] > 0])} 个")
            print(f"下跌ETF: {len(df[df['涨跌幅(%)'] < 0])} 个")
            print(f"平盘ETF: {len(df[df['涨跌幅(%)'] == 0])} 个")
            if len(df) > 0:
                print(f"平均涨跌幅: {df['涨跌幅(%)'].mean():.2f}%")
                print(f"最大涨幅: {df['涨跌幅(%)'].max():.2f}%")
                if df['涨跌幅(%)'].min() < 0:
                    print(f"最大跌幅: {df['涨跌幅(%)'].min():.2f}%")
            
            return filename
            
        except Exception as e:
            print(f"导出Excel时出错: {e}")
            return None
    
    def run(self):
        """执行主要流程"""
        print("=" * 70)
        print("ETF技术指标突破检测器")
        print("检测条件: 1) 5日线上穿10日线  2) 下穿布林带下轨")
        print(f"检测日期: {datetime.now().strftime('%Y-%m-%d')}")
        print("=" * 70)
        
        # 获取ETF列表
        etf_list = self.get_etf_list_from_sina()
        if not etf_list:
            print("未能获取ETF列表，程序退出")
            return
        
        # 批量检测技术指标突破
        breakthrough_results = self.detect_breakthroughs_batch(etf_list)
        
        # 导出结果
        if breakthrough_results:
            filename = self.export_to_excel(breakthrough_results)
            if filename:
                print(f"\n🎉 任务完成！Excel文件已保存: {filename}")
        else:
            print("\n📋 今天没有发现符合条件的技术指标突破ETF")

def main():
    """主函数"""
    try:
        detector = ETFBreakthroughDetector()
        detector.run()
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n程序执行异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()