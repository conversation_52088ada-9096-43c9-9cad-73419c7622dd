#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基金分析器 - 多线程并发基金筛选和导出工具

主要功能:
- 基金数据获取和分析
- 多线程并发处理，提高效率
- 智能代理池管理，避免IP限制
- 基金筛选条件检测（回撤修复期、RSI指标、均线关系等）
- 自动导出CSV报告到Downloads目录

更新日志:
- 添加了巨量IP代理获取的重试逻辑
- 支持自定义重试次数和重试间隔
- 重试失败后程序直接退出，确保运行的可靠性
- 针对并发场景优化了重试参数
- 简化了错误处理逻辑，去除备用机制
- 添加代理池管理，支持多代理轮换使用
- 增加历史净值处理线程数配置
- 添加代理失效检测和自动刷新机制
- 添加基金估值功能，支持获取实时估值数据
- 🎯 重构回撤计算算法：采用基准回撤方法，从观察期第一天作基准，更贴近投资者实际体验
- 🔧 优化修复期判断：第2-5天范围（已放宽条件），从最低点上涨即为修复期
- 🆕 增加新筛选条件：回撤修复期第三天 + 五日线站在十日线上
"""

from get_fund_list import FundListGetter
from fund_analysis_api_client import FundAnalysisApiClient, convert_fund_data_for_api
import os
import sys
from pathlib import Path
import csv
from datetime import datetime
import threading
import time
import queue
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv
import random
import json
import logging

def format_fund_size(fund_size):
    """
    将基金规模转换为万或亿单位的格式化字符串

    参数：
    - fund_size: 基金规模（元）

    返回：
    - 格式化后的字符串，如 "5.41万" 或 "84.77亿"
    """
    if not fund_size:
        return "未知"

    try:
        size_float = float(fund_size)

        if size_float <= 0:
            return "未知"

        # 如果大于等于1亿，使用亿作为单位
        if size_float >= 100000000:  # 1亿 = 100,000,000
            size_yi = size_float / 100000000
            return f"{size_yi:.2f}亿"
        # 如果大于等于1万，使用万作为单位
        elif size_float >= 10000:  # 1万 = 10,000
            size_wan = size_float / 10000
            return f"{size_wan:.2f}万"
        # 小于1万的直接显示元
        else:
            return f"{size_float:.2f}元"

    except (ValueError, TypeError):
        return "未知"

class JuliangProxyConfig:
    """
    巨量IP代理配置类
    参考 cursor_pro_keep_alive.py 和 config.py 的实现方式
    """
    _env_loaded = False  # 标记是否已加载 .env 文件
    
    @classmethod
    def _load_env_file(cls):
        """
        加载 .env 文件
        参考 config.py 的实现方式
        """
        if cls._env_loaded:
            return
            
        try:
            # 获取应用程序的根目录路径 (与 config.py 保持一致)
            if getattr(sys, "frozen", False):
                # 如果是打包后的可执行文件
                application_path = os.path.dirname(sys.executable)
            else:
                # 如果是开发环境
                application_path = os.path.dirname(os.path.abspath(__file__))

            # 指定 .env 文件的路径
            dotenv_path = os.path.join(application_path, ".env")
            
            if os.path.exists(dotenv_path):
                # 加载 .env 文件
                load_dotenv(dotenv_path)
            else:
                print(f"⚠️ .env 文件不存在: {dotenv_path}")
                print("💡 提示: 您可以创建 .env 文件并配置 PROXY_URL")
                
            cls._env_loaded = True
            
        except Exception as e:
            print(f"❌ 加载 .env 文件失败: {e}")
            cls._env_loaded = True  # 即使失败也标记为已尝试
    
    @classmethod
    def get_proxy_url(cls):
        """
        获取巨量IP API地址
        参考 cursor_pro_keep_alive.py 的实现方式
        """
        # 确保已加载 .env 文件
        cls._load_env_file()
        
        # 直接从环境变量获取（包括从 .env 文件加载的）
        proxy_url = os.getenv("PROXY_URL")
        
        if proxy_url and "juliangip.com" in proxy_url:
            return proxy_url
        else:
            print("⚠️ 未在 .env 文件中找到有效的 PROXY_URL 配置")
            print("💡 提示：请在 .env 文件中设置 PROXY_URL")
            print("📝 .env 文件示例:")
            print("   PROXY_URL=https://api.juliangip.com/dynamic_proxy?neek=your_api_key&num=1&type=2&sep=1&regions=")
            return None
    

    


class ProxyPool:
    """
    代理池管理类，支持多代理轮换使用、自动刷新和动态质量评估
    """
    def __init__(self, pool_size=5, refresh_interval=240, quality_check_threshold=10):  # 4分钟刷新一次
        self.pool_size = pool_size
        self.refresh_interval = refresh_interval  # 代理刷新间隔（秒）
        self.quality_check_threshold = quality_check_threshold  # 质量检查的最小请求次数

        self.proxies = []
        self.proxy_creation_times = []  # 记录每个代理的创建时间
        self.proxy_stats = []  # 记录每个代理的性能统计
        self.current_index = 0
        self.lock = threading.Lock()
        self.last_refresh_time = 0
        self.last_quality_check = 0

        # 移除了代理质量测试配置，不再需要测试基金代码

        # 初始化代理池
        self._initialize_pool()

    def _test_proxy_quality(self, proxy):
        """
        🆕 测试代理质量：简化版本，不再测试估值接口

        参数：
        - proxy: 代理配置字典

        返回：
        - bool: True表示代理质量合格，False表示不合格
        """
        if not proxy:
            return False

        # 简化代理质量测试：只要代理配置存在就认为合格
        # 移除了原来的估值接口测试，避免初始化时的网络错误
        return True

    def _initialize_pool(self):
        """初始化代理池，快速获取代理不进行质量测试"""
        failed_count = 0
        max_failures = self.pool_size // 2  # 🛑 允许最多一半的代理获取失败

        for i in range(self.pool_size):
            # 🔄 获取代理的循环（不进行质量测试）
            max_attempts = 3  # 每个代理位置最多尝试3次
            proxy_acquired = False

            # 显示进度条
            progress = (i / self.pool_size) * 100
            bar_length = 20
            filled_length = int(bar_length * i / self.pool_size)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f"\r🔄 代理池初始化: |{bar}| {i}/{self.pool_size} ({progress:.0f}%)", end='', flush=True)

            for attempt in range(max_attempts):
                proxy = get_juliang_proxy(max_retries=2, retry_delay=1)
                if proxy:
                    # 🆕 跳过代理质量测试，直接添加到代理池
                    self.proxies.append(proxy)
                    self.proxy_creation_times.append(time.time())
                    # 初始化代理统计数据
                    self.proxy_stats.append({
                        'success_count': 0,
                        'fail_count': 0,
                        'total_response_time': 0.0,
                        'request_count': 0,
                        'avg_response_time': 0.0,
                        'success_rate': 1.0,  # 初始成功率设为1.0
                        'quality_score': 1.0,  # 初始质量分数
                        'blacklisted': False,  # 是否被列入黑名单
                        'last_used': time.time()
                    })
                    proxy_acquired = True
                    time.sleep(1)  # 避免频繁请求
                    break
                else:
                    time.sleep(1)

            if not proxy_acquired:
                failed_count += 1

                # 🛑 如果失败过多，立即停止
                if failed_count > max_failures:
                    print(f"\n🛑 代理获取失败过多（{failed_count}/{self.pool_size}），程序停止!")
                    print("💡 调试建议:")
                    print("1. 检查 .env 文件中的 PROXY_URL 配置")
                    print("2. 检查巨量IP账户余额和状态")
                    print("3. 检查网络连接")
                    sys.exit(1)

        # 完成进度条
        print(f"\r🔄 代理池初始化: |{'█' * 20}| {self.pool_size}/{self.pool_size} (100%)")

        if not self.proxies:
            print("🛑 代理池初始化完全失败，程序无法继续")
            print("💡 请检查代理服务配置后重试")
            sys.exit(1)

        # 🛑 检查代理数量是否足够（调试模式下允许单个代理）
        min_proxies = 1 if self.pool_size == 1 else 2  # 调试模式允许单个代理
        if len(self.proxies) < min_proxies:
            print(f"🛑 可用代理数量不足（{len(self.proxies)}个），程序停止!")
            print(f"💡 至少需要{min_proxies}个可用代理才能保证程序稳定运行")
            sys.exit(1)

        print(f"✅ 代理池初始化完成，共{len(self.proxies)}个可用代理")
        self.last_refresh_time = time.time()
    
    def get_proxy(self):
        """获取一个可用的代理，支持轮换、自动刷新和基于质量的选择"""
        with self.lock:
            current_time = time.time()

            # 检查是否需要进行质量评估
            if current_time - self.last_quality_check > 60:  # 每分钟检查一次质量
                self._evaluate_proxy_quality()
                self.last_quality_check = current_time

            # 检查是否需要刷新代理池
            if current_time - self.last_refresh_time > self.refresh_interval:
                self._refresh_expired_proxies()
                self.last_refresh_time = current_time

            if not self.proxies:
                print("⚠️ 代理池为空，尝试重新初始化...")
                self._initialize_pool()

            # 基于质量选择代理（优先选择高质量代理）
            proxy, proxy_index = self._select_best_proxy()

            if proxy_index is not None:
                # 更新最后使用时间
                self.proxy_stats[proxy_index]['last_used'] = current_time

            return proxy

    def get_different_proxy(self, exclude_proxy=None):
        """
        🆕 获取一个不同的代理（用于重试时避免使用相同的代理）

        参数:
        - exclude_proxy: 要排除的代理

        返回:
        - 不同的代理对象，如果没有其他可用代理则返回None
        """
        with self.lock:
            if not self.proxies:
                return None

            exclude_http = exclude_proxy.get('http') if exclude_proxy else None

            # 查找可用的不同代理
            available_proxies = []
            for i, (proxy, stats) in enumerate(zip(self.proxies, self.proxy_stats)):
                # 排除指定的代理和被列入黑名单的代理
                if (not stats['blacklisted'] and
                    proxy.get('http') != exclude_http):
                    available_proxies.append((i, proxy, stats))

            if not available_proxies:
                return None

            # 🆕 随机选择一个可用的代理（实现您建议的随机选择策略）
            import random
            selected_index, selected_proxy, selected_stats = random.choice(available_proxies)

            # 更新最后使用时间
            selected_stats['last_used'] = time.time()

            return selected_proxy


    
    def _refresh_expired_proxies(self):
        """刷新过期的代理，包含质量测试"""
        current_time = time.time()
        refreshed_count = 0

        for i in range(len(self.proxies)):
            proxy_age = current_time - self.proxy_creation_times[i]

            # 如果代理使用时间超过刷新间隔，则刷新
            if proxy_age > self.refresh_interval:
                # 🔄 获取新代理并进行质量测试的循环
                max_attempts = 3  # 每个代理位置最多尝试3次
                proxy_refreshed = False

                for attempt in range(max_attempts):
                    new_proxy = get_juliang_proxy(max_retries=2, retry_delay=1)

                    if new_proxy:
                        # 🆕 进行代理质量测试
                        if self._test_proxy_quality(new_proxy):
                            # 质量测试通过，更新代理
                            self.proxies[i] = new_proxy
                            self.proxy_creation_times[i] = current_time
                            # 重置统计数据
                            self.proxy_stats[i] = {
                                'success_count': 0,
                                'fail_count': 0,
                                'total_response_time': 0.0,
                                'request_count': 0,
                                'avg_response_time': 0.0,
                                'success_rate': 1.0,
                                'quality_score': 1.0,
                                'blacklisted': False,
                                'last_used': current_time
                            }
                            refreshed_count += 1
                            proxy_refreshed = True
                            break
                        else:
                            # 质量测试失败，尝试获取新的代理
                            time.sleep(1)  # 短暂等待后重试
                    else:
                        time.sleep(1)

                time.sleep(0.5)  # 避免频繁请求

        if refreshed_count > 0:
            # print(f"🔄 代理池已刷新{refreshed_count}个代理")  # 已注释，避免干扰单行进度条
            pass
    
    def _evaluate_proxy_quality(self):
        """评估所有代理的质量，淘汰低质量代理"""
        if not self.proxies:
            return
        
        current_time = time.time()
        low_quality_indices = []
        
        for i, stats in enumerate(self.proxy_stats):
            # 只对有足够请求数据的代理进行质量评估
            if stats['request_count'] >= self.quality_check_threshold:
                # 计算成功率
                success_rate = stats['success_count'] / stats['request_count']
                stats['success_rate'] = success_rate
                
                # 计算平均响应时间
                if stats['success_count'] > 0:
                    avg_response_time = stats['total_response_time'] / stats['success_count']
                    stats['avg_response_time'] = avg_response_time
                else:
                    avg_response_time = float('inf')  # 如果没有成功请求，设为无穷大
                
                # 计算质量分数 (成功率权重70%, 响应时间权重30%)
                # 响应时间越小越好，成功率越高越好
                response_time_score = max(0, 1 - (avg_response_time - 1) / 10)  # 1秒以内得满分，11秒以上得0分
                quality_score = success_rate * 0.7 + response_time_score * 0.3
                stats['quality_score'] = quality_score

                # 标记低质量代理（质量分数低于0.3或成功率低于50%）
                if quality_score < 0.3 or success_rate < 0.5:
                    if not stats['blacklisted']:
                        stats['blacklisted'] = True
                        low_quality_indices.append(i)
        
        # 移除低质量代理（如果还有足够的好代理）
        if low_quality_indices and len(self.proxies) - len(low_quality_indices) >= 2:
            self._remove_low_quality_proxies(low_quality_indices)
    
    def _remove_low_quality_proxies(self, low_quality_indices):
        """移除低质量代理并补充新代理"""
        removed_count = 0
        
        # 从后往前删除，避免索引问题
        for i in sorted(low_quality_indices, reverse=True):
            if i < len(self.proxies):
                self.proxies.pop(i)
                self.proxy_creation_times.pop(i)
                self.proxy_stats.pop(i)
                removed_count += 1
        
        # 补充新的代理，包含质量测试
        if removed_count > 0:
            for _ in range(removed_count):
                # 🔄 获取新代理并进行质量测试的循环
                max_attempts = 3  # 每个代理位置最多尝试3次

                for attempt in range(max_attempts):
                    new_proxy = get_juliang_proxy(max_retries=2, retry_delay=1)
                    if new_proxy:
                        # 🆕 进行代理质量测试
                        if self._test_proxy_quality(new_proxy):
                            # 质量测试通过，添加到代理池
                            self.proxies.append(new_proxy)
                            self.proxy_creation_times.append(time.time())
                            self.proxy_stats.append({
                                'success_count': 0,
                                'fail_count': 0,
                                'total_response_time': 0.0,
                                'request_count': 0,
                                'avg_response_time': 0.0,
                                'success_rate': 1.0,
                                'quality_score': 1.0,
                                'blacklisted': False,
                                'last_used': time.time()
                            })
                            break
                        else:
                            # 质量测试失败，尝试获取新的代理
                            time.sleep(1)  # 短暂等待后重试
                    else:
                        time.sleep(1)
    
    def _select_best_proxy(self):
        """选择最优质的代理"""
        if not self.proxies:
            return None, None
        
        # 过滤掉被黑名单的代理
        available_proxies = []
        for i, (proxy, stats) in enumerate(zip(self.proxies, self.proxy_stats)):
            if not stats['blacklisted']:
                available_proxies.append((i, proxy, stats))
        
        if not available_proxies:
            # 🚀 如果所有代理都被列入黑名单，先尝试补充新代理
            print(f"⚠️ 所有代理都不可用，尝试补充新代理...")
            success = self._add_new_proxy_immediately()
            
            if success:
                # 新代理添加成功，返回新代理
                latest_index = len(self.proxies) - 1
                return self.proxies[latest_index], latest_index
            else:
                # 补充失败，临时恢复一个代理使用
                print(f"❌ 补充新代理失败，临时恢复最近的代理")
                if self.proxies:
                    latest_index = len(self.proxies) - 1
                    self.proxy_stats[latest_index]['blacklisted'] = False  # 临时解除黑名单
                    return self.proxies[latest_index], latest_index
                else:
                    return None, None
        
        # 根据质量分数排序，优先选择高质量代理
        # 同时考虑负载均衡，避免总是使用同一个代理
        current_time = time.time()
        
        # 计算综合评分（质量分数 + 负载均衡分数）
        scored_proxies = []
        for i, proxy, stats in available_proxies:
            quality_score = stats['quality_score']
            
            # 负载均衡分数：最近使用时间越久远，分数越高
            time_since_last_used = current_time - stats['last_used']
            load_balance_score = min(time_since_last_used / 60, 1.0)  # 最多1分钟的加分
            
            # 综合评分
            final_score = quality_score * 0.8 + load_balance_score * 0.2
            scored_proxies.append((i, proxy, final_score))
        
        # 选择评分最高的代理
        best_index, best_proxy, _ = max(scored_proxies, key=lambda x: x[2])
        return best_proxy, best_index
    
    def record_proxy_performance(self, proxy, success, response_time=None, is_network_error=False):
        """
        记录代理性能数据，支持立即淘汰有问题的代理

        参数:
        - proxy: 代理对象
        - success: 是否成功
        - response_time: 响应时间
        - is_network_error: 是否为网络错误（手动标记）
        """
        if not proxy:
            return

        proxy_http = proxy.get('http')
        if not proxy_http:
            return

        with self.lock:
            # 查找对应的代理索引
            proxy_index = None
            for i, p in enumerate(self.proxies):
                if p.get('http') == proxy_http:
                    proxy_index = i
                    break

            if proxy_index is not None and proxy_index < len(self.proxy_stats):
                stats = self.proxy_stats[proxy_index]
                stats['request_count'] += 1

                if success:
                    stats['success_count'] += 1
                    if response_time is not None:
                        stats['total_response_time'] += response_time
                    # 🚀 成功请求时，如果代理之前被误标为黑名单，可以恢复
                    if stats['blacklisted'] and stats['success_count'] >= 3:
                        stats['blacklisted'] = False
                        print(f"✅ 代理{proxy_index+1}恢复正常使用")
                else:
                    stats['fail_count'] += 1

                    # 🚀 只有明确标记为网络错误时才立即淘汰代理
                    if is_network_error:
                        # 立即标记为黑名单，避免继续使用有问题的代理
                        stats['blacklisted'] = True
                        # 增加失败权重，快速降低质量分数
                        stats['fail_count'] += 5  # 网络异常按5次失败计算
                        print(f"🚫 代理{proxy_index+1}因网络异常被立即标记为不可用")

                        # 🆕 检查是否需要重新加载整个代理池
                        self._check_and_reload_proxy_pool_if_needed()

    def _check_and_reload_proxy_pool_if_needed(self):
        """
        🆕 检查是否需要重新加载整个代理池
        只有当所有代理都不可用时，才重新加载整个代理池
        """
        # 统计可用代理数量
        available_count = sum(1 for stats in self.proxy_stats if not stats['blacklisted'])

        # 如果可用代理数量少于总数的20%，重新加载整个代理池
        min_available = max(1, len(self.proxies) // 5)  # 至少保持20%可用

        if available_count < min_available:
            print(f"⚠️ 可用代理不足（{available_count}/{len(self.proxies)}），重新加载整个代理池...")
            self._reload_entire_proxy_pool()

    def _reload_entire_proxy_pool(self):
        """
        🆕 重新加载整个代理池
        """
        print(f"🔄 开始重新加载整个代理池（{self.pool_size}个代理）...")

        # 清空现有代理池
        old_proxy_count = len(self.proxies)
        self.proxies.clear()
        self.proxy_creation_times.clear()
        self.proxy_stats.clear()

        print(f"🗑️ 已清空{old_proxy_count}个旧代理")

        # 重新初始化代理池
        failed_count = 0
        max_failures = self.pool_size // 2  # 🛑 允许最多一半的代理获取失败

        for i in range(self.pool_size):
            # 🔄 获取代理并进行质量测试的循环
            max_attempts = 3  # 每个代理位置最多尝试3次
            proxy_acquired = False

            for attempt in range(max_attempts):
                proxy = get_juliang_proxy(max_retries=2, retry_delay=1)
                if proxy:
                    # 🆕 跳过代理质量测试，直接添加到代理池
                    self.proxies.append(proxy)
                    self.proxy_creation_times.append(time.time())
                    # 初始化代理统计数据
                    self.proxy_stats.append({
                        'success_count': 0,
                        'fail_count': 0,
                        'total_response_time': 0.0,
                        'request_count': 0,
                        'avg_response_time': 0.0,
                        'success_rate': 1.0,  # 初始成功率设为1.0
                        'quality_score': 1.0,  # 初始质量分数
                        'blacklisted': False,  # 是否被列入黑名单
                        'last_used': time.time()
                    })
                    proxy_acquired = True
                    time.sleep(1)  # 避免频繁请求
                    break
                else:
                    time.sleep(1)

            if not proxy_acquired:
                failed_count += 1
                print(f"❌ 新代理 {i+1}/{self.pool_size} 最终获取失败（已尝试{max_attempts}次）")

                # 🛑 如果失败过多，停止重新加载
                if failed_count > max_failures:
                    print(f"🛑 新代理获取失败过多（{failed_count}/{self.pool_size}），停止重新加载")
                    break

        if self.proxies:
            print(f"🎉 代理池重新加载完成，共{len(self.proxies)}个可用代理")
            self.last_refresh_time = time.time()
        else:
            print(f"🛑 代理池重新加载失败，无可用代理")

    def _replace_bad_proxy_immediately(self, bad_proxy_index):
        """
        🆕 立即替换有问题的代理

        参数:
        - bad_proxy_index: 有问题的代理索引
        """
        if bad_proxy_index >= len(self.proxies):
            return

        old_proxy = self.proxies[bad_proxy_index]
        # 静默替换代理，不输出日志干扰进度条

        # 🔄 获取新代理的循环（不进行质量测试）
        max_attempts = 3  # 最多尝试3次

        for attempt in range(max_attempts):
            new_proxy = get_juliang_proxy(max_retries=2, retry_delay=1)

            if new_proxy:
                # 🆕 跳过代理质量测试，直接替换代理
                self.proxies[bad_proxy_index] = new_proxy
                self.proxy_creation_times[bad_proxy_index] = time.time()
                # 重置统计数据
                self.proxy_stats[bad_proxy_index] = {
                    'success_count': 0,
                    'fail_count': 0,
                    'total_response_time': 0.0,
                    'request_count': 0,
                    'avg_response_time': 0.0,
                    'success_rate': 1.0,
                    'quality_score': 1.0,
                    'blacklisted': False,
                    'last_used': time.time()
                }
                # 静默成功替换，不输出日志
                return
            else:
                # 获取失败，静默重试
                time.sleep(1)

        print(f"❌ 替换代理最终失败（已尝试{max_attempts}次），保持原代理但标记为不可用")

    def _add_new_proxy_immediately(self):
        """
        立即添加新代理补充池子（只在所有代理都不可用时调用），包含质量测试

        返回:
        - bool: 是否成功添加新代理
        """
        try:
            print(f"🔄 尝试补充新代理...")

            # 🔄 获取新代理并进行质量测试的循环
            max_attempts = 3  # 最多尝试3次

            for attempt in range(max_attempts):
                new_proxy = get_juliang_proxy(max_retries=2, retry_delay=1)

                if new_proxy:
                    # 🆕 跳过代理质量测试，直接添加到代理池
                    self.proxies.append(new_proxy)
                    self.proxy_creation_times.append(time.time())
                    self.proxy_stats.append({
                        'success_count': 0,
                        'fail_count': 0,
                        'total_response_time': 0.0,
                        'request_count': 0,
                        'avg_response_time': 0.0,
                        'success_rate': 1.0,
                        'quality_score': 1.0,
                        'blacklisted': False,
                        'last_used': time.time()
                    })
                    print(f"✅ 成功补充新代理: {new_proxy.get('http', 'Unknown')[:30]}...")
                    return True
                else:
                    print(f"❌ 获取新代理失败 (尝试 {attempt+1}/{max_attempts})")
                    time.sleep(1)

            print(f"❌ 补充新代理最终失败（已尝试{max_attempts}次）")
            return False

        except Exception as e:
            print(f"❌ 补充新代理异常: {e}")
            return False
    
    def get_pool_status(self):
        """获取代理池状态（包含质量信息）"""
        current_time = time.time()
        status = []
        
        for i, (proxy, creation_time, stats) in enumerate(zip(self.proxies, self.proxy_creation_times, self.proxy_stats)):
            age = current_time - creation_time
            status.append({
                'index': i,
                'proxy': proxy.get('http', 'Unknown')[:30] + '...',
                'age_seconds': age,
                'is_fresh': age < self.refresh_interval,
                'success_rate': f"{stats['success_rate']:.1%}",
                'avg_response_time': f"{stats['avg_response_time']:.1f}s",
                'quality_score': f"{stats['quality_score']:.2f}",
                'request_count': stats['request_count'],
                'blacklisted': stats['blacklisted']
            })
        
        return status

def make_request_with_proxy_tracking(proxy_pool, request_func, *args, **kwargs):
    """
    使用代理发送请求并记录性能数据的包装函数
    
    参数:
    - proxy_pool: 代理池对象
    - request_func: 请求函数，应该接受proxy参数
    - *args, **kwargs: 传递给请求函数的其他参数
    
    返回:
    - 请求结果
    """
    if not proxy_pool:
        return request_func(*args, **kwargs)
    
    proxy = proxy_pool.get_proxy()
    start_time = time.time()
    success = False
    result = None
    
    try:
        # 在kwargs中添加proxy参数
        kwargs['proxy'] = proxy
        result = request_func(*args, **kwargs)
        success = result is not None
        return result
    except Exception as e:
        success = False
        raise e
    finally:
        # 记录性能数据
        response_time = time.time() - start_time
        proxy_pool.record_proxy_performance(proxy, success, response_time)

def get_juliang_proxy(max_retries=3, retry_delay=2):
    """
    从巨量IP获取一个新的代理，支持重试机制
    参考 cursor_pro_keep_alive.py 的实现方式

    参数:
    - max_retries: 最大重试次数，默认3次
    - retry_delay: 重试间隔秒数，默认2秒

    如果重试失败，程序将退出运行
    """
    # 使用配置类获取代理URL
    proxy_url = JuliangProxyConfig.get_proxy_url()

    if not proxy_url:
        print("❌ 未配置巨量IP代理URL，程序无法继续运行")
        print("💡 请在 .env 文件中配置 PROXY_URL")
        sys.exit(1)

    # 主API重试逻辑
    for attempt in range(max_retries):
        try:
            response = requests.get(proxy_url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200 and data.get("data") and data["data"].get("proxy_list"):
                    proxy_info = data["data"]["proxy_list"][0]
                    proxy_address = proxy_info.split(',')[0]  # 提取 IP:端口 部分

                    # 确保代理地址有正确的格式
                    if not proxy_address.startswith(('http://', 'https://')):
                        proxy_address = f"http://{proxy_address}"

                    proxy_dict = {
                        'http': proxy_address,
                        'https': proxy_address
                    }
                    return proxy_dict
                else:
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
            else:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue

        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue

    # 所有重试都失败，返回None而不是退出程序（由代理池处理）
    return None



def process_fund_page(page_index, proxy_pool, result_queue, history_workers=20, global_progress=None, ignored_fund_codes=None):
    """
    🚀 优化：处理单页基金数据的线程函数，减少实例创建开销
    🛑 新增：严格错误检测，遇到关键错误时停止程序
    🔄 新增：代理重试机制，优先从代理池中换代理重试
    🔗 新增：忽略基金功能，跳过被忽略的基金

    参数：
    - page_index: 页码
    - proxy_pool: 代理池对象
    - result_queue: 结果队列
    - history_workers: 处理历史净值的线程数，默认20
    - global_progress: 全局进度跟踪器
    - ignored_fund_codes: 忽略的基金代码集合
    """
    if ignored_fund_codes is None:
        ignored_fund_codes = set()
    thread_id = threading.current_thread().name
    max_proxy_retries = 3  # 最多尝试3个不同的代理

    try:
        fund_list_data = None
        last_error = None

        # 🔄 代理重试循环
        current_proxy = None
        for retry_attempt in range(max_proxy_retries):
            # 从代理池获取代理
            if retry_attempt == 0:
                # 第一次尝试，正常获取代理
                proxy = proxy_pool.get_proxy()
            else:
                # 重试时，优先获取不同的代理
                proxy = proxy_pool.get_different_proxy(exclude_proxy=current_proxy)
                if not proxy:
                    # 如果没有其他代理，使用正常轮换
                    proxy = proxy_pool.get_proxy()

            current_proxy = proxy  # 记录当前使用的代理
            proxy_info = proxy.get('http')[:30] if proxy and proxy.get('http') else '无代理'

            # 精简日志输出，避免干扰进度条
            # if retry_attempt == 0:
            #     print(f"🧵 [{thread_id}] 开始处理第{page_index}页，使用代理: {proxy_info}...")
            # else:
            #     print(f"🔄 [{thread_id}] 第{page_index}页重试第{retry_attempt+1}次，换用代理: {proxy_info}...")

            # 🛑 检查代理有效性
            if not proxy or not proxy.get('http'):
                last_error = f"代理获取失败，第{page_index}页无法处理"
                print(f"⚠️ [{thread_id}] {last_error}，尝试下一个代理...")
                continue

            try:
                # 创建专用的FundListGetter实例，传入代理配置
                getter = FundListGetter(proxy=proxy)

                # 获取当前页的基金列表（使用新的接口参数）
                fund_list_data = getter.get_fund_list(
                    pageIndex=page_index,
                    pageNum=200  # 每页200个基金
                )

                # 🎯 检查获取结果
                if fund_list_data and fund_list_data.get('Data'):
                    # 成功获取数据，跳出重试循环
                    # if retry_attempt > 0:
                    #     print(f"✅ [{thread_id}] 第{page_index}页在第{retry_attempt+1}次尝试成功获取数据")  # 已注释，避免干扰单行进度条
                    break
                else:
                    last_error = f"第{page_index}页获取基金列表失败 - API返回空数据"
                    # 静默处理代理切换，不输出日志干扰进度条

                    # 如果不是最后一次尝试，继续重试
                    if retry_attempt < max_proxy_retries - 1:
                        time.sleep(1)  # 短暂延迟避免频繁请求
                        continue

            except Exception as e:
                last_error = f"第{page_index}页使用代理{proxy_info}时发生异常: {e}"
                # 静默处理代理切换，不输出日志干扰进度条

                # 如果不是最后一次尝试，继续重试
                if retry_attempt < max_proxy_retries - 1:
                    time.sleep(1)  # 短暂延迟避免频繁请求
                    continue

        # 🛑 所有代理都尝试失败
        if not fund_list_data or not fund_list_data.get('Data'):
            error_msg = f"第{page_index}页尝试{max_proxy_retries}个代理后仍失败 - {last_error}"
            print(f"🛑 [{thread_id}] {error_msg}")

            # 🛑 检查是否为关键错误（连续多页失败）
            is_critical = page_index <= 3  # 前3页失败视为关键错误

            result_queue.put({
                'page_index': page_index,
                'success': False,
                'error': error_msg,
                'critical_error': is_critical,  # 🛑 标记关键错误状态
                'results': []
            })
            return
        
        funds = fund_list_data['Data']
        # 🔗 过滤掉被忽略的基金
        fund_codes = [fund.get('fundCode') for fund in funds
                     if fund.get('fundCode') and fund.get('fundCode') not in ignored_fund_codes]
        
        # 创建基金代码到名称的映射，同时提取收益率数据
        fund_names = {fund.get('fundCode'): fund.get('fundName', 'Unknown') for fund in funds if fund.get('fundCode')}
        
        # 🆕 提取收益率数据映射
        fund_returns = {}
        for fund in funds:
            if fund.get('fundCode'):
                fund_returns[fund['fundCode']] = {
                    'hySyl': fund.get('hySyl', ''),      # 近半年收益率
                    'yearSyl': fund.get('yearSyl', ''),  # 近一年收益率
                    'trySyl': fund.get('trySyl', ''),    # 近三年收益率
                    'sySyl': fund.get('sySyl', ''),      # 今年来收益率
                    'company': fund.get('company', ''),  # 基金公司
                    'ftype': fund.get('ftype', ''),      # 基金类型
                    'fundSize': fund.get('fundSize', 0)  # 基金规模
                }
        
        # 精简日志输出，避免干扰进度条
        # print(f"📊 [{thread_id}] 第{page_index}页获取到 {len(fund_codes)} 个基金，开始多线程检测均线上穿...")

        # 🚀 优化：使用多线程并发处理历史净值检测，使用优化后的函数
        fund_infos = [{'code': code, 'name': fund_names.get(code, 'Unknown'), 'returns': fund_returns.get(code, {})} for code in fund_codes]
        qualified_funds = process_funds_history_concurrent(
            fund_infos,
            proxy_pool,
            max_workers=history_workers,  # 历史净值处理线程数
            global_progress=global_progress  # 传入全局进度跟踪器
        )
        
        # 精简输出，避免干扰进度条
        # if len(qualified_funds) > 0:
        #     print(f"🎯 第{page_index}页发现 {len(qualified_funds)} 个符合条件的基金")
        
        result_queue.put({
            'page_index': page_index,
            'success': True,
            'total_funds': len(fund_codes),
            'qualified_count': len(qualified_funds),
            'results': qualified_funds
        })
        
    except Exception as e:
        error_msg = f"第{page_index}页处理严重异常: {e}"
        print(f"🛑 [{thread_id}] {error_msg}")
        
        # 🛑 任何未捕获的异常都视为关键错误
        result_queue.put({
            'page_index': page_index,
            'success': False,
            'error': error_msg,
            'critical_error': True,  # 🛑 异常都标记为关键错误
            'results': []
        })



def test_api_fund_list_concurrent():
    """
    多线程并发从API获取基金列表并进行均线上穿检测
    """
    # 记录开始时间
    start_time = time.time()

    print("🚀 开始多线程并发基金均线分析...")
    print("=" * 80)

    # 🔗 初始化API客户端，获取忽略基金列表
    api_client = FundAnalysisApiClient()
    ignored_fund_codes = set()

    try:
        if api_client.test_connection():
            ignored_fund_codes = set(api_client.get_ignored_fund_codes())
            print(f"📋 已获取忽略基金列表，共 {len(ignored_fund_codes)} 只基金将被排除")
        else:
            print("⚠️ API连接失败，将不使用忽略基金功能")
    except Exception as e:
        print(f"⚠️ 获取忽略基金列表失败: {e}")
        print("⚠️ 将继续执行分析，但不使用忽略基金功能")

    # 🚀 初始化代理池，支持多代理轮换
    proxy_pool = ProxyPool(pool_size=4, refresh_interval=240)  # 🎯 增加到4个代理，支持更高并发，4分钟刷新

    # 🚀 动态获取总页数和基金总数量
    test_proxy = proxy_pool.get_proxy()
    getter = FundListGetter(proxy=test_proxy)

    # 获取总页数和基金总数量（一次调用，避免重复）
    page_size = 200
    total_pages, total_fund_count = getter.calculate_total_pages(page_size=page_size)

    if total_pages is None or total_fund_count is None:
        # 如果无法获取总页数，使用默认值
        total_pages = 10
        total_fund_count = total_pages * page_size
        print(f"⚠️ 使用默认页数: {total_pages}页，预计基金数量: {total_fund_count}")
    else:
        # 限制最大页数，避免处理时间过长
        max_pages_limit = 50  # 最多处理50页（每页200条，更合适的分页）
        if total_pages > max_pages_limit:
            print(f"⚠️ 总页数({total_pages})超过限制，限制为{max_pages_limit}页")
            total_pages = max_pages_limit
            total_fund_count = min(total_fund_count, total_pages * page_size)
        else:
            print(f"📊 获取到 {total_fund_count} 只基金，共 {total_pages} 页")

    # 🚀 优化并发配置 - 🎯 进一步提升性能
    page_workers = min(12, total_pages)  # 🎯 页面处理线程数：8 -> 12 (进一步增加)
    history_workers = 20                 # 🎯 历史净值处理线程数：15 -> 20 (进一步增加)
    result_queue = queue.Queue()
    all_results = []

    # 🎯 创建全局进度跟踪器和消息收集器
    global_progress = {
        'completed_funds': 0,
        'total_funds': total_fund_count,
        'qualified_messages': [],  # 收集所有符合条件的基金信息
        'filtered_messages': [],   # 收集所有被过滤的基金信息
        'lock': threading.Lock()
    }

    # 使用线程池执行并发任务
    with ThreadPoolExecutor(max_workers=page_workers, thread_name_prefix="FundWorker") as executor:
        # 为每个页面提交任务
        future_to_page = {}

        for page_index in range(1, total_pages + 1):
            # 传入代理池、历史净值处理线程数、全局进度跟踪器和忽略基金列表
            future = executor.submit(process_fund_page, page_index, proxy_pool, result_queue, history_workers, global_progress, ignored_fund_codes)
            future_to_page[future] = page_index
        
        # 等待所有任务完成
        print(f"⏳ 已提交 {total_pages} 个检测任务，等待完成...")

        completed_pages = 0
        for future in as_completed(future_to_page):
            page_index = future_to_page[future]
            completed_pages += 1


            try:
                future.result()  # 获取结果，如果有异常会抛出
            except Exception as e:
                # 只显示关键错误
                print(f"\n❌ 第{page_index}页任务异常: {e}")

    # 收集所有结果
    print("📊 收集检测结果...")
    critical_errors = []  # 🛑 记录关键错误
    
    while not result_queue.empty():
        result = result_queue.get()
        all_results.append(result)
        
        # 🛑 检查关键错误
        if result.get('critical_error'):
            critical_errors.append(result)
    
    # 🛑 如果发现关键错误，立即停止程序
    if critical_errors:
        print(f"\n🛑 发现 {len(critical_errors)} 个关键错误，程序停止!")
        print("=" * 80)
        print("🔍 关键错误详情:")
        
        for error in critical_errors:
            print(f"  📄 第{error['page_index']}页: {error['error']}")
        
        print("\n💡 调试建议:")
        print("1. 检查网络连接是否稳定")
        print("2. 检查代理服务是否正常")
        print("3. 检查API接口是否被限制")
        print("4. 检查系统资源是否充足")
        
        print(f"\n🛑 程序因关键错误停止，请解决上述问题后重试")
        print("=" * 80)
        
        # 保存错误日志
        error_log = {
            'timestamp': datetime.now().isoformat(),
            'critical_errors': critical_errors,
            'total_results': len(all_results),
            'error_details': 'Program stopped due to critical errors'
        }
        
        with open(f'error_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w', encoding='utf-8') as f:
            json.dump(error_log, f, indent=2, ensure_ascii=False)
        
        # 🛑 停止程序执行
        sys.exit(1)
    

    
    # 按页码排序
    all_results.sort(key=lambda x: x['page_index'])
    
    # 收集所有符合条件的基金
    all_qualified_funds = []

    for result in all_results:
        if result['success']:
            all_qualified_funds.extend(result['results'])
    
    # 计算总耗时
    total_time = time.time() - start_time
    total_time_str = f"{int(total_time // 60)}分{int(total_time % 60)}秒" if total_time >= 60 else f"{total_time:.1f}秒"

    # 准备基本信息，供主函数使用
    stats_info = {
        'total_time_str': total_time_str,
        'total_time_seconds': total_time
    }
    
    # 🚀 获取符合条件基金的天天基金指标数据
    if all_qualified_funds:
        print(f"📊 开始获取 {len(all_qualified_funds)} 只基金的指标数据、估值数据和行业信息...")

        # 🚀 优化：使用多线程同时获取天天基金指标数据、估值数据和行业信息
        with ThreadPoolExecutor(max_workers=20, thread_name_prefix="DataWorker") as executor:
            future_to_fund = {}

            # 为每个基金提交天天基金指标获取任务、估值数据获取任务和行业信息获取任务
            for fund in all_qualified_funds:
                fund_code = fund['fund_code']
                # 提交天天基金指标任务
                tiantian_future = executor.submit(get_fund_tiantian_metrics_with_proxy, fund_code, proxy_pool, 2)
                future_to_fund[tiantian_future] = {'fund': fund, 'type': 'tiantian'}

                # 🆕 提交估值数据任务
                estimate_future = executor.submit(get_single_fund_estimate_with_proxy, fund_code, proxy_pool, 2)
                future_to_fund[estimate_future] = {'fund': fund, 'type': 'estimate'}

                # 🆕 提交行业信息获取任务
                industry_future = executor.submit(get_fund_industry_with_proxy, fund_code, proxy_pool, 2)
                future_to_fund[industry_future] = {'fund': fund, 'type': 'industry'}

            # 收集天天基金指标数据、估值数据和行业信息
            tiantian_results = {}
            estimate_results = {}
            industry_results = {}
            completed_count = 0
            total_funds = len(all_qualified_funds)
            total_tasks = total_funds * 3  # 每个基金有3个任务
            fund_task_completion = {}  # 记录每个基金的任务完成情况

            for future in as_completed(future_to_fund):
                task_info = future_to_fund[future]
                fund = task_info['fund']
                fund_code = fund['fund_code']
                task_type = task_info['type']
                completed_count += 1

                # 📊 单行进度条显示（实时更新，不累积换行）
                progress_percent = (completed_count / total_tasks) * 100

                # 创建进度条
                bar_length = 30
                filled_length = int(bar_length * completed_count / total_tasks)
                bar = '█' * filled_length + '░' * (bar_length - filled_length)

                # 使用 \r 回到行首，覆盖之前的内容
                progress_msg = f"\r📈 数据获取: |{bar}| {completed_count}/{total_tasks} ({progress_percent:.1f}%) - {fund_code}({task_type})"
                print(progress_msg, end='', flush=True)

                # 如果是最后一个任务，换行
                if completed_count == total_tasks:
                    print()  # 换行，完成进度条

                try:
                    if task_type == 'tiantian':
                        # 处理天天基金指标数据
                        tiantian_data = future.result()
                        tiantian_results[fund_code] = tiantian_data
                    elif task_type == 'estimate':
                        # 🆕 处理估值数据
                        estimate_data = future.result()
                        if estimate_data and isinstance(estimate_data, tuple) and len(estimate_data) == 2:
                            _, estimate_info = estimate_data
                            estimate_results[fund_code] = estimate_info
                        else:
                            estimate_results[fund_code] = None
                    elif task_type == 'industry':
                        # 🆕 处理行业信息
                        industry_data = future.result()
                        industry_results[fund_code] = industry_data

                    # 记录基金任务完成情况
                    if fund_code not in fund_task_completion:
                        fund_task_completion[fund_code] = set()
                    fund_task_completion[fund_code].add(task_type)

                except Exception as e:
                    # 减少错误输出，避免干扰进度条
                    if task_type == 'tiantian':
                        tiantian_results[fund_code] = None
                    elif task_type == 'estimate':
                        estimate_results[fund_code] = None
                    elif task_type == 'industry':
                        industry_results[fund_code] = None

            # 数据获取完成

            # 🚀 处理天天基金指标数据、估值数据和行业信息
            tiantian_success_count = 0
            no_tiantian_count = 0
            estimate_success_count = 0
            no_estimate_count = 0
            industry_success_count = 0
            no_industry_count = 0

            for fund in all_qualified_funds:
                fund_code = fund['fund_code']

                # 处理天天基金指标数据
                tiantian_data = tiantian_results.get(fund_code)
                if tiantian_data:
                    # 添加天天基金指标数据到基金信息中
                    fund.update({
                        'stddev1': tiantian_data.get('stddev1'),  # 波动率/年
                        'maxretra1': tiantian_data.get('maxretra1'),  # 最大回撤/年
                        'sharp1': tiantian_data.get('sharp1'),  # 夏普比率/年
                        'has_tiantian_metrics': tiantian_data.get('has_metrics', False)
                    })

                    if tiantian_data.get('has_metrics'):
                        tiantian_success_count += 1
                    else:
                        no_tiantian_count += 1
                else:
                    # 设置空值
                    fund.update({
                        'stddev1': None,
                        'maxretra1': None,
                        'sharp1': None,
                        'has_tiantian_metrics': False
                    })

                # 🆕 处理估值数据
                estimate_data = estimate_results.get(fund_code)
                if estimate_data and not estimate_data.get('no_estimate_today', False):
                    # 补充估值数据
                    fund.update({
                        'estimate_value': estimate_data.get('gsz', 0),
                        'estimate_growth': estimate_data.get('gszzl', 0),
                        'estimate_time': estimate_data.get('gztime', ''),
                        'estimate_unit_value': estimate_data.get('dwjz', 0),
                        'estimate_value_date': estimate_data.get('jzrq', ''),
                        'has_estimate': True
                    })
                    estimate_success_count += 1
                else:
                    # 无估值数据
                    fund.update({
                        'estimate_value': 0,
                        'estimate_growth': 0,
                        'estimate_time': '',
                        'estimate_unit_value': 0,
                        'estimate_value_date': '',
                        'has_estimate': False
                    })
                    no_estimate_count += 1

                # 🆕 处理行业信息
                industry_data = industry_results.get(fund_code)
                if industry_data and industry_data.get('has_industry', False):
                    # 添加行业信息到基金信息中
                    fund.update({
                        'industry_name': industry_data.get('industry_name', ''),
                        'industry_code': industry_data.get('industry_code', ''),
                        'industry_source': industry_data.get('source', ''),
                        'has_industry': True
                    })
                    industry_success_count += 1
                else:
                    # 无行业信息
                    fund.update({
                        'industry_name': '',
                        'industry_code': '',
                        'industry_source': '',
                        'has_industry': False
                    })
                    no_industry_count += 1




    
    # 保存结果到CSV
    if all_qualified_funds:
        # 🎯 按照最近三天涨跌幅倒序排列（从高到低）
        all_qualified_funds.sort(key=lambda x: x.get('recent_change', 0), reverse=True)
        print(f"📊 基金列表已按最近三天涨跌幅倒序排列")
        
        download_dir = str(Path.home() / "Downloads")
        csv_file_path = os.path.join(download_dir, f"fund_ma_crossover_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
        
        with open(csv_file_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            # CSV表头
            writer.writerow([
                '基金代码', '基金名称', '基金公司', '基金类型', '基金规模', '最新净值日期', '当日涨跌幅', 'MA5值', 'MA10值',

                '最近三天涨跌幅', '匹配原因', '最新一天5日线在10日线之上',
                '最新净值价格', '所属行业',  # 🆕 新增所属行业列
                '估算净值', '估算涨跌幅', '估值时间',  # 🆕 新增估算净值相关字段
                '回撤深度%', '当前回撤修复率%',  # 🆕 新增回撤相关字段
                '近半年收益率', '近一年收益率', '近三年收益率', '今年来收益率',  # 收益率字段
                '波动率(年)', '最大回撤(年)', '夏普比率(年)'  # 🆕 天天基金指标字段（中文名称）
            ])

            for fund in all_qualified_funds:
                writer.writerow([
                    fund['fund_code'],
                    fund['fund_name'],
                    fund.get('company', ''),  # 基金公司
                    fund.get('ftype', ''),  # 基金类型
                    format_fund_size(fund.get('fund_size', 0)),  # 基金规模
                    fund.get('latest_date', 'N/A'),  # 最新净值日期
                    f"{fund.get('daily_change', 0.0):.2f}%",  # 当日涨跌幅
                    f"{fund['ma5']:.4f}" if fund['ma5'] is not None else 'N/A',
                    f"{fund['ma10']:.4f}" if fund['ma10'] is not None else 'N/A',

                    f"{fund['recent_change']:.2f}%",
                    fund.get('match_reason', '未知原因'),  # 匹配原因
                    "是" if fund.get('latest_ma5_above_ma10', False) else "否",  # 最新一天5日线在10日线之上
                    f"{fund.get('latest_price', 0):.4f}",  # 最新净值价格
                    fund.get('industry_name', ''),  # 🆕 所属行业
                    # 🆕 估算净值相关数据
                    f"{fund.get('estimate_value', 0):.4f}" if fund.get('estimate_value', 0) > 0 else '',  # 估算净值
                    f"{fund.get('estimate_growth', 0):+.2f}%" if fund.get('estimate_value', 0) > 0 else '',  # 估算涨跌幅
                    fund.get('estimate_time', '') if fund.get('has_estimate', False) else '',  # 估值时间
                    # 🆕 回撤相关数据
                    f"{fund.get('drawdown_percent', 0):.2f}%" if fund.get('drawdown_percent', 0) > 0 else '',  # 回撤深度%
                    f"{fund.get('recovery_percent', 0):.2f}%" if fund.get('recovery_percent', 0) > 0 else '',  # 当前回撤修复率%
                    # 🆕 添加收益率数据
                    f"{fund.get('hy_syl', '')}%" if fund.get('hy_syl') else '未知',  # 近半年收益率
                    f"{fund.get('year_syl', '')}%" if fund.get('year_syl') else '未知',  # 近一年收益率
                    f"{fund.get('try_syl', '')}%" if fund.get('try_syl') else '未知',  # 近三年收益率
                    f"{fund.get('sy_syl', '')}%" if fund.get('sy_syl') else '未知',  # 今年来收益率
                    # 🆕 添加天天基金指标数据
                    fund.get('stddev1', '') if fund.get('stddev1') is not None else '',  # STDDEV1(波动率/年)
                    fund.get('maxretra1', '') if fund.get('maxretra1') is not None else '',  # MAXRETRA1(最大回撤/年)
                    fund.get('sharp1', '') if fund.get('sharp1') is not None else ''  # SHARP1(夏普比率/年)
                ])
        
        # 将CSV文件路径加入统计信息
        stats_info['csv_file_path'] = csv_file_path

        # 🔗 保存数据到数据库（通过API）
        print(f"\n💾 开始保存数据到数据库...")
        try:
            # 转换数据格式为API所需格式
            api_fund_list = []
            for fund in all_qualified_funds:
                # 过滤掉被忽略的基金
                if fund['fund_code'] not in ignored_fund_codes:
                    api_fund_data = convert_fund_data_for_api(fund)
                    api_fund_list.append(api_fund_data)

            if api_fund_list:
                # 保存到数据库
                analysis_date = datetime.now().strftime('%Y-%m-%d')
                success = api_client.save_fund_analysis_data(api_fund_list, analysis_date)

                if success:
                    print(f"✅ 成功保存 {len(api_fund_list)} 条基金数据到数据库")
                    stats_info['saved_to_db'] = True
                    stats_info['saved_count'] = len(api_fund_list)
                else:
                    print(f"❌ 保存数据到数据库失败")
                    stats_info['saved_to_db'] = False
            else:
                print(f"📋 所有基金都在忽略列表中，跳过数据库保存")
                stats_info['saved_to_db'] = True
                stats_info['saved_count'] = 0

        except Exception as e:
            print(f"❌ 保存数据到数据库时发生错误: {e}")
            stats_info['saved_to_db'] = False
        
        # 显示符合条件的基金详情
        print(f"\n🏆 符合条件的基金详情:")
        print("-" * 120)
        for i, fund in enumerate(all_qualified_funds, 1):
            match_reason = fund.get('match_reason', '未知原因')
            print(f"{i:2d}. {fund['fund_code']} - {fund['fund_name']}")
            print(f"    最新净值日期: {fund.get('latest_date', 'N/A')} | 当日涨跌幅: {fund.get('daily_change', 0.0):.2f}% | 匹配原因: {match_reason}")
            print(f"    MA10: {fund['ma10']:.4f} > MA5: {fund['ma5']:.4f} | 最近三天涨跌幅: {fund['recent_change']:.2f}%")

            # 显示匹配状态
            latest_ma5_above = "✅ 是" if fund.get('latest_ma5_above_ma10', False) else "❌ 否"
            price_above_ma10 = "✅ 是" if fund.get('price_above_ma10', False) else "❌ 否"
            print(f"    10日线高于5日线: {latest_ma5_above} | 净值在10日线之上: {price_above_ma10}")

            # 🆕 显示行业信息
            industry_name = fund.get('industry_name', '')
            if industry_name:
                print(f"    🏭 所属行业: {industry_name}")
            else:
                print(f"    🏭 所属行业: 未知")

            # 显示净值信息和回撤信息
            latest_price = fund.get('latest_price', 0)
            print(f"    💰 净值信息: 最新净值: {latest_price:.4f} ({fund.get('latest_date', 'N/A')})")

            # 🆕 显示回撤信息
            drawdown_percent = fund.get('drawdown_percent', 0)
            recovery_percent = fund.get('recovery_percent', 0)
            if drawdown_percent > 0:
                print(f"    📉 回撤信息: 回撤深度: {drawdown_percent:.2f}% | 修复进度: {recovery_percent:.2f}%")
    else:
        print(f"\n📊 未发现符合条件的基金（三天内上穿 + 最新一天5日线在10日线之上）")
        # 当没有符合条件的基金时，不设置csv_file_path
    
    print(f"\n🎉 多线程并发检测完成！")

    # 🎯 统一显示所有收集的消息（符合条件和被过滤的基金信息）
    if global_progress:
        with global_progress['lock']:
            qualified_messages = global_progress['qualified_messages']
            filtered_messages = global_progress['filtered_messages']

            # 显示符合条件的基金信息
            if qualified_messages:
                print(f"\n🎯 发现 {len(qualified_messages)} 个符合条件的基金：")
                for message in qualified_messages:
                    print(message)
                print()  # 添加空行分隔

            # 显示被过滤的基金信息（仅显示部分，避免输出过多）
            if filtered_messages:
                display_count = min(20, len(filtered_messages))  # 增加显示数量到20个
                print(f"🚫 过滤了 {len(filtered_messages)} 个基金（显示前{display_count}个）：")
                for message in filtered_messages[:display_count]:
                    print(message)
                if len(filtered_messages) > display_count:
                    print(f"   ... 还有 {len(filtered_messages) - display_count} 个基金被过滤")
                print()  # 添加空行分隔

    return all_results, stats_info

def process_single_fund_history(fund_info, getter_instance):
    """
    🚀 优化：处理单个基金的历史数据检测，复用FundListGetter实例
    🛑 新增：严格错误检测，记录每个基金的处理状态
    🔧 增强：添加重试机制和更详细的错误日志
    🎯 修复：增强重试机制，确保结果一致性

    参数：
    - fund_info: 基金信息字典 {'code': '基金代码', 'name': '基金名称', 'returns': '收益率数据'}
    - getter_instance: 复用的FundListGetter实例

    返回：
    - 检测结果字典，包含均线分析结果
    """
    fund_code = fund_info['code']
    fund_name = fund_info['name']
    fund_returns = fund_info.get('returns', {})
    max_retries = 5  # 🎯 增加重试次数到5次，提高成功率

    for attempt in range(max_retries + 1):
        try:
            # 🎯 添加渐进式延迟，避免频繁重试
            if attempt > 0:
                delay = min(attempt * 0.5, 2.0)  # 最多延迟2秒
                time.sleep(delay)

            # 🚀 优化：直接使用传入的getter实例，避免重复创建
            # 检测严格筛选条件
            result = getter_instance.check_strict_fund_conditions(fund_code, fund_name)

            # 🛑 检查结果完整性
            if not result or not isinstance(result, dict):
                if attempt < max_retries:
                    # 基金{fund_code}第{attempt+1}次尝试返回结果格式异常，重试中...
                    continue
                # 🎯 最后一次尝试失败，记录详细错误但不标记为关键错误
                return {
                    'success': False,
                    'fund_code': fund_code,
                    'error': f'基金{fund_code}返回结果格式异常（已重试{max_retries}次）',
                    'critical_error': False,
                    'retry_count': max_retries
                }

            # 🛑 检查必要字段
            required_fields = ['success', 'latest_data']
            missing_fields = [field for field in required_fields if field not in result]
            if missing_fields:
                if attempt < max_retries:
                    # 基金{fund_code}第{attempt+1}次尝试缺少必要字段: {missing_fields}，重试中...
                    continue
                return {
                    'success': False,
                    'fund_code': fund_code,
                    'error': f'基金{fund_code}缺少必要字段: {missing_fields}（已重试{max_retries}次）',
                    'critical_error': False,
                    'retry_count': max_retries
                }

            # 🎯 成功获取均线结果，添加重试信息和收益率数据
            result['retry_count'] = attempt
            if attempt > 0:
                result['had_retries'] = True
                # print(f"✅ 基金{fund_code}在第{attempt+1}次尝试成功")  # 已注释，避免干扰单行进度条

            # 🆕 添加收益率数据到结果中
            result['fund_returns'] = fund_returns

            return result



        except Exception as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试异常: {e}，重试中...
                continue

            error_msg = f'基金{fund_code}({fund_name})处理异常: {e}（已重试{max_retries}次）'
            print(f"❌ {error_msg}")

            return {
                'success': False,
                'fund_code': fund_code,
                'error': error_msg,
                'critical_error': False,  # 🎯 不标记为关键错误，避免程序停止
                'retry_count': max_retries
            }

def process_funds_history_concurrent(fund_infos, proxy_pool, max_workers=15, global_progress=None):
    """
    🚀 优化：多线程并发处理基金历史数据，使用共享的FundListGetter实例池

    参数：
    - fund_infos: 基金信息列表 [{'code': '基金代码', 'name': '基金名称'}, ...]
    - proxy_pool: 代理池对象
    - max_workers: 最大并发线程数，🎯 默认15（进一步提升性能）
    - global_progress: 全局进度跟踪器

    返回：
    - 符合条件的基金列表
    """
    qualified_funds = []
    qualified_messages = []  # 收集符合条件的基金信息，避免干扰进度条
    filtered_messages = []   # 收集被过滤的基金信息，避免干扰进度条
    total_funds = len(fund_infos)

    # 精简日志输出，避免干扰进度条
    # print(f"🧵 开始多线程处理 {total_funds} 个基金的历史数据（{max_workers}个线程）...")

    # 🚀 优化：预创建FundListGetter实例池，减少实例创建开销
    getter_pool = []
    for i in range(min(max_workers, 8)):  # 🎯 创建最多8个实例，支持更高并发
        proxy = proxy_pool.get_proxy()
        getter = FundListGetter(proxy=proxy)
        getter_pool.append(getter)

    # print(f"✅ 已创建 {len(getter_pool)} 个FundListGetter实例，用于复用")

    with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="HistoryWorker") as executor:
        # 提交所有任务
        future_to_fund = {}
        for i, fund_info in enumerate(fund_infos):
            # 🚀 优化：轮换使用预创建的getter实例
            getter_instance = getter_pool[i % len(getter_pool)]
            future = executor.submit(process_single_fund_history, fund_info, getter_instance)
            future_to_fund[future] = fund_info

        # 处理完成的任务
        completed_count = 0
        critical_fund_errors = []  # 🛑 记录基金处理的关键错误

        for future in as_completed(future_to_fund):
            fund_info = future_to_fund[future]
            fund_code = fund_info['code']
            fund_name = fund_info['name']
            completed_count += 1

            # 🎯 更新全局进度并显示全局进度条
            if global_progress:
                with global_progress['lock']:
                    global_progress['completed_funds'] += 1
                    current_completed = global_progress['completed_funds']
                    total_global_funds = global_progress['total_funds']

                    # 📊 全局进度条显示（基于总基金数量）
                    progress_percent = (current_completed / total_global_funds) * 100

                    # 创建进度条
                    bar_length = 30
                    filled_length = int(bar_length * current_completed / total_global_funds)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)

                    # 使用 \r 回到行首，覆盖之前的内容
                    progress_msg = f"\r📈 基金处理: |{bar}| {current_completed}/{total_global_funds} ({progress_percent:.1f}%) - {fund_code}"
                    print(progress_msg, end='', flush=True)

                    # 如果是最后一个任务，换行
                    if current_completed == total_global_funds:
                        print()  # 换行，完成进度条
            else:
                # 备用方案：使用局部进度
                progress_percent = (completed_count / total_funds) * 100
                bar_length = 30
                filled_length = int(bar_length * completed_count / total_funds)
                bar = '█' * filled_length + '░' * (bar_length - filled_length)
                progress_msg = f"\r📈 基金处理: |{bar}| {completed_count}/{total_funds} ({progress_percent:.1f}%) - {fund_code}"
                print(progress_msg, end='', flush=True)
                if completed_count == total_funds:
                    print()

            try:
                result = future.result()

                # 🛑 检查关键错误
                if result.get('critical_error'):
                    critical_fund_errors.append({
                        'fund_code': fund_code,
                        'fund_name': fund_name,
                        'error': result.get('error', '未知错误')
                    })
                    print(f"🛑 [{completed_count}/{len(fund_infos)}] {fund_code}({fund_name}) 关键错误: {result.get('error')}")
                    
                    # 🛑 如果关键错误超过5个，立即停止
                    if len(critical_fund_errors) >= 5:
                        print(f"\n🛑 基金处理关键错误超过5个，程序停止!")
                        print("🔍 关键错误详情:")
                        for error in critical_fund_errors:
                            print(f"  📄 {error['fund_code']}({error['fund_name']}): {error['error']}")
                        
                        # 保存错误日志并停止
                        error_log = {
                            'timestamp': datetime.now().isoformat(),
                            'error_type': 'Fund Processing Critical Errors',
                            'critical_fund_errors': critical_fund_errors,
                            'completed_count': completed_count,
                            'total_funds': len(fund_infos)
                        }
                        
                        with open(f'fund_error_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w', encoding='utf-8') as f:
                            json.dump(error_log, f, indent=2, ensure_ascii=False)
                        
                        print(f"🛑 错误日志已保存，程序停止")
                        sys.exit(1)

                if result.get('success') and result.get('match_found'):
                    # 找到符合条件的基金
                    total_change = result.get('recent_3_day_change', 0.0)

                    # 🆕 添加过滤条件
                    # 1. 排除基金名称以"A"结尾的基金
                    if fund_name.endswith('A'):
                        # 收集被过滤的基金信息到全局，不立即显示（避免干扰进度条）
                        message = f"🚫 [{completed_count}/{len(fund_infos)}] {fund_code}({fund_name}) 被过滤：基金名称以A结尾"
                        filtered_messages.append(message)
                        if global_progress:
                            with global_progress['lock']:
                                global_progress['filtered_messages'].append(message)
                        continue

                    # 2. 剔除最近三天涨跌幅小于1%的基金
                    if total_change < 1.0:
                        # 收集被过滤的基金信息到全局，不立即显示（避免干扰进度条）
                        message = f"🚫 [{completed_count}/{len(fund_infos)}] {fund_code}({fund_name}) 被过滤：最近三天涨跌幅{total_change:.2f}%小于1%"
                        filtered_messages.append(message)
                        if global_progress:
                            with global_progress['lock']:
                                global_progress['filtered_messages'].append(message)
                        continue

                    # 🆕 3. 排除基金规模小于1000万的基金
                    fund_returns = result.get('fund_returns', {})
                    fund_size = fund_returns.get('fundSize', 0)
                    try:
                        fund_size_float = float(fund_size) if fund_size else 0
                        if fund_size_float < 10000000:  # 1000万 = 10,000,000
                            size_formatted = format_fund_size(fund_size_float)
                            message = f"🚫 [{completed_count}/{len(fund_infos)}] {fund_code}({fund_name}) 被过滤：基金规模{size_formatted}小于1000万"
                            filtered_messages.append(message)
                            if global_progress:
                                with global_progress['lock']:
                                    global_progress['filtered_messages'].append(message)
                            continue
                    except (ValueError, TypeError):
                        # 如果无法解析基金规模，继续正常处理
                        pass

                    # 从严格筛选结果中获取数据
                    ma5_above_ma10_result = result.get('ma5_above_ma10_result', {})
                    price_above_ma10_result = result.get('price_above_ma10_result', {})

                    # 获取移动平均线数据
                    ma5 = ma5_above_ma10_result.get('latest_ma5', 0.0)
                    ma10 = ma5_above_ma10_result.get('latest_ma10', 0.0)

                    # 获取条件满足情况
                    latest_ma5_above_ma10 = ma5_above_ma10_result.get('ma5_above_ma10', False)
                    price_above_ma10 = price_above_ma10_result.get('above_ma10', False) if price_above_ma10_result else False

                    # 🆕 获取收益率数据
                    fund_returns = result.get('fund_returns', {})

                    # 🆕 获取回撤信息数据
                    drawdown_recovery_result = result.get('drawdown_recovery_result', {})
                    drawdown_info = drawdown_recovery_result.get('drawdown_info', {})

                    # 提取回撤深度和修复率
                    drawdown_percent = drawdown_info.get('drawdown_percent', 0) if drawdown_info.get('found') else 0
                    recovery_percent = drawdown_info.get('recovery_percent', 0) if drawdown_info.get('found') else 0

                    # 🆕 创建基金基础信息
                    qualified_fund = {
                        'fund_code': result['fund_code'],
                        'fund_name': fund_name,
                        'latest_date': result['latest_data']['date'],  # 最新净值日期
                        'latest_price': result['latest_data']['price'],  # 🔧 添加最新净值价格
                        'ma5': ma5,
                        'ma10': ma10,
                        'recent_change': total_change,
                        'daily_change': result.get('daily_change', 0.0),  # 当日涨跌幅
                        'latest_ma5_above_ma10': latest_ma5_above_ma10,  # 最新一天5日线是否在10日线之上
                        'price_above_ma10': price_above_ma10,  # 新增：当前净值是否在10日线之上
                        'match_reason': result.get('match_reason', '未知原因'),  # 匹配原因
                        # 🆕 添加回撤信息
                        'drawdown_percent': drawdown_percent,  # 回撤深度%
                        'recovery_percent': recovery_percent,  # 当前回撤修复率%
                        # 🆕 添加收益率数据
                        'hy_syl': fund_returns.get('hySyl', ''),  # 近半年收益率
                        'year_syl': fund_returns.get('yearSyl', ''),  # 近一年收益率
                        'try_syl': fund_returns.get('trySyl', ''),  # 近三年收益率
                        'sy_syl': fund_returns.get('sySyl', ''),  # 今年来收益率
                        'company': fund_returns.get('company', ''),  # 基金公司
                        'ftype': fund_returns.get('ftype', ''),  # 基金类型
                        'fund_size': fund_returns.get('fundSize', 0)  # 基金规模
                    }



                    qualified_funds.append(qualified_fund)

                    # 收集符合条件的基金信息到全局，不立即显示（避免干扰进度条）
                    match_reason = result.get('match_reason', '未知原因')
                    message = f"🎉 {fund_code}({fund_name}) 符合条件！{match_reason}"
                    qualified_messages.append(message)

                    # 同时添加到全局消息收集器
                    if global_progress:
                        with global_progress['lock']:
                            global_progress['qualified_messages'].append(message)
                # 移除其他输出，避免干扰进度条

            except Exception as e:
                # 只显示关键错误，避免过多输出
                if "critical" in str(e).lower():
                    print(f"❌ {fund_code}({fund_name}) 关键错误: {e}")

    # 不再在这里显示消息，所有消息都收集到全局，在主函数最后统一显示

    # 精简日志输出，避免干扰进度条
    # print(f"✅ 历史数据处理完成！发现 {len(qualified_funds)} 个符合条件的基金")
    return qualified_funds

def calculate_recent_change_from_result(result):
    """
    从已有的检测结果中获取最近3天涨跌幅，避免重复获取数据
    """
    # 直接使用已经计算好的涨跌幅数据
    if 'recent_3_day_change' in result:
        return result['recent_3_day_change']
    
    # 备用方案：如果结果中没有涨跌幅数据，使用历史价格数据计算
    if 'historical_prices' in result and len(result['historical_prices']) >= 4:
        try:
            prices = result['historical_prices']
            latest_price = prices[-1]
            three_days_ago_price = prices[-4]
            return (latest_price - three_days_ago_price) / three_days_ago_price * 100
        except (ValueError, IndexError, ZeroDivisionError):
            return 0.0
    
    # 最后的备用方案：返回0
    print(f"    无法计算{result.get('fund_code', 'Unknown')}的最近3天涨跌幅")
    return 0.0



def setup_debug_logging():
    """
    设置调试日志记录
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f'fund_debug_{timestamp}.log'

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    print(f"📝 调试日志将保存到: {log_filename}")
    return logging.getLogger(__name__)

def test_core_metrics_api():
    """
    测试天天基金指标和行业信息API接口
    """
    print("🧪 测试天天基金指标和行业信息API接口...")

    # 测试基金代码列表
    test_funds = ['009941', '161725', '000001', '110022']

    for fund_code in test_funds:
        print(f"\n📊 测试基金 {fund_code}:")

        # 测试天天基金指标API
        print(f"   🔍 天天基金指标:")
        tiantian_result = get_fund_tiantian_metrics(fund_code, proxy=None, max_retries=1)

        if tiantian_result:
            print(f"   ✅ 成功获取天天基金指标数据:")
            print(f"      STDDEV1(波动率/年): {tiantian_result.get('stddev1', 'N/A')}")
            print(f"      MAXRETRA1(最大回撤/年): {tiantian_result.get('maxretra1', 'N/A')}")
            print(f"      SHARP1(夏普比率/年): {tiantian_result.get('sharp1', 'N/A')}")
            print(f"      有指标数据: {tiantian_result.get('has_metrics', False)}")
        else:
            print(f"   ❌ 获取天天基金指标数据失败")

        # 测试行业信息API
        print(f"   🏭 行业信息:")
        industry_result = get_fund_industry(fund_code, proxy=None, max_retries=1)

        if industry_result:
            industry_name = industry_result.get('industry_name', '')
            if industry_name:
                print(f"   ✅ 成功获取行业信息: {industry_name}")
                print(f"      行业代码: {industry_result.get('industry_code', 'N/A')}")
            else:
                print(f"   📊 该基金无行业分类信息")
        else:
            print(f"   ❌ 获取行业信息失败")

    print(f"\n🎉 天天基金指标和行业信息API测试完成！")


def test_small_sample_debug():
    """
    🔍 小样本调试模式 - 只测试少量基金，详细记录每个步骤
    """
    # 记录开始时间
    start_time = time.time()
    
    logger = setup_debug_logging()
    logger.info("🔍 开始小样本调试模式")



    # 初始化代理池（只用1个代理，减少变量）
    proxy_pool = ProxyPool(pool_size=1, refresh_interval=240)

    # 只测试第1页，每页只取20个基金
    test_proxy = proxy_pool.get_proxy()
    getter = FundListGetter(proxy=test_proxy)

    logger.info("📊 获取测试样本...")

    # 🔄 添加代理重试机制（调试模式）
    fund_list_data = None
    max_debug_retries = 3

    for retry_attempt in range(max_debug_retries):
        try:
            if retry_attempt > 0:
                # 换一个代理重试
                test_proxy = proxy_pool.get_proxy()
                getter = FundListGetter(proxy=test_proxy)
                logger.info(f"🔄 调试模式第{retry_attempt+1}次尝试，换用新代理...")

            fund_list_data = getter.get_fund_list(pageIndex=1, pageNum=20)  # 只取20个

            if fund_list_data and fund_list_data.get('Data'):
                if retry_attempt > 0:
                    logger.info(f"✅ 调试模式在第{retry_attempt+1}次尝试成功获取数据")
                break
            else:
                logger.warning(f"⚠️ 调试模式第{retry_attempt+1}次尝试获取数据失败")
                if retry_attempt < max_debug_retries - 1:
                    time.sleep(2)  # 延迟后重试

        except Exception as e:
            logger.error(f"❌ 调试模式第{retry_attempt+1}次尝试异常: {e}")
            if retry_attempt < max_debug_retries - 1:
                time.sleep(2)  # 延迟后重试

    if not fund_list_data or not fund_list_data.get('Data'):
        logger.error(f"❌ 调试模式尝试{max_debug_retries}次后仍无法获取测试数据")
        return None

    funds = fund_list_data['Data']
    fund_codes = [fund.get('fundCode') for fund in funds if fund.get('fundCode')]
    fund_names = {fund.get('fundCode'): fund.get('fundName', 'Unknown') for fund in funds if fund.get('fundCode')}
    
    # 🆕 提取收益率数据映射（调试模式）
    fund_returns = {}
    for fund in funds:
        if fund.get('fundCode'):
            fund_returns[fund['fundCode']] = {
                'hySyl': fund.get('hySyl', ''),      # 近半年收益率
                'yearSyl': fund.get('yearSyl', ''),  # 近一年收益率
                'trySyl': fund.get('trySyl', ''),    # 近三年收益率
                'sySyl': fund.get('sySyl', ''),      # 今年来收益率
                'company': fund.get('company', ''),  # 基金公司
                'ftype': fund.get('ftype', ''),      # 基金类型
                'fundSize': fund.get('fundSize', 0)  # 基金规模
            }

    logger.info(f"📋 获取到 {len(fund_codes)} 个测试基金")

    # 单线程处理，详细记录每个基金
    qualified_funds = []
    processing_log = []

    for i, fund_code in enumerate(fund_codes):
        fund_name = fund_names.get(fund_code, 'Unknown')
        fund_return_data = fund_returns.get(fund_code, {})  # 🆕 获取收益率数据
        logger.info(f"🔍 [{i+1}/{len(fund_codes)}] 开始处理 {fund_code}({fund_name})")

        try:
            # 记录处理开始时间
            start_time = time.time()

            # 检测严格筛选条件
            result = getter.check_strict_fund_conditions(fund_code, fund_name)

            # 记录处理时间
            process_time = time.time() - start_time

            # 详细记录处理结果
            log_entry = {
                'fund_code': fund_code,
                'fund_name': fund_name,
                'process_time': round(process_time, 2),
                'success': result.get('success', False),
                'match_found': result.get('match_found', False),
                'match_reason': result.get('match_reason', ''),
                'recent_change': result.get('recent_3_day_change', 0),
                'timestamp': datetime.now().isoformat()
            }

            processing_log.append(log_entry)

            if result.get('success') and result.get('match_found'):
                # 符合条件的基金
                total_change = result.get('recent_3_day_change', 0.0)

                # 获取移动平均线数据
                ma5_above_ma10_result = result.get('ma5_above_ma10_result', {})
                price_above_ma10_result = result.get('price_above_ma10_result', {})

                qualified_fund = {
                    'fund_code': fund_code,
                    'fund_name': fund_name,
                    'latest_date': result['latest_data']['date'],
                    'latest_price': result['latest_data']['price'],
                    'ma5': ma5_above_ma10_result.get('latest_ma5', 0.0),
                    'ma10': ma5_above_ma10_result.get('latest_ma10', 0.0),
                    'recent_change': total_change,
                    'daily_change': result.get('daily_change', 0.0),
                    'latest_ma5_above_ma10': ma5_above_ma10_result.get('ma5_above_ma10', False),
                    'price_above_ma10': price_above_ma10_result.get('above_ma10', False),
                    'match_reason': result.get('match_reason', '未知原因'),
                    'process_order': i + 1,  # 记录处理顺序
                    # 🆕 添加收益率数据（调试模式）
                    'hy_syl': fund_return_data.get('hySyl', ''),  # 近半年收益率
                    'year_syl': fund_return_data.get('yearSyl', ''),  # 近一年收益率
                    'try_syl': fund_return_data.get('trySyl', ''),  # 近三年收益率
                    'sy_syl': fund_return_data.get('sySyl', ''),  # 今年来收益率
                    'company': fund_return_data.get('company', ''),  # 基金公司
                    'ftype': fund_return_data.get('ftype', ''),  # 基金类型
                    'fund_size': fund_return_data.get('fundSize', 0)  # 基金规模
                }

                # 🆕 注意：估值数据现在在主流程中与天天基金指标数据一起获取，这里不再单独获取
                # 设置默认的估值数据结构（将在主流程中填充）
                qualified_fund.update({
                    'estimate_value': 0,
                    'estimate_growth': 0,
                    'estimate_time': '',
                    'estimate_unit_value': 0,
                    'estimate_value_date': '',
                    'has_estimate': False
                })

                qualified_funds.append(qualified_fund)
                logger.info(f"✅ {fund_code} 符合条件: {result.get('match_reason', '')}")
            else:
                logger.info(f"❌ {fund_code} 不符合条件")

        except Exception as e:
            logger.error(f"❌ {fund_code} 处理异常: {e}")
            processing_log.append({
                'fund_code': fund_code,
                'fund_name': fund_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })

    # 保存详细处理日志
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 保存处理日志
    with open(f'debug_processing_log_{timestamp}.json', 'w', encoding='utf-8') as f:
        json.dump(processing_log, f, indent=2, ensure_ascii=False)

    # 保存符合条件的基金（按处理顺序）
    if qualified_funds:
        with open(f'debug_qualified_funds_{timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump(qualified_funds, f, indent=2, ensure_ascii=False)

        # 也保存CSV格式
        csv_file = f'debug_qualified_funds_{timestamp}.csv'
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow([
                '处理顺序', '基金代码', '基金名称', '基金公司', '基金类型', '基金规模', '最新净值日期', '当日涨跌幅',
                'MA5值', 'MA10值', '最近三天涨跌幅', '匹配原因', '所属行业',  # 🆕 新增所属行业
                '实时估算净值', '实时估算涨跌幅', '实时估值时间', '实时净值日期', '实时单位净值', '有估值数据',  # 🆕 估值相关字段
                '近半年收益率', '近一年收益率', '近三年收益率', '今年来收益率'  # 🆕 添加收益率字段
            ])

            for fund in qualified_funds:
                writer.writerow([
                    fund['process_order'],
                    fund['fund_code'],
                    fund['fund_name'],
                    fund.get('company', ''),  # 基金公司
                    fund.get('ftype', ''),  # 基金类型
                    format_fund_size(fund.get('fund_size', 0)),  # 基金规模
                    fund.get('latest_date', 'N/A'),
                    f"{fund.get('daily_change', 0.0):.2f}%",
                    f"{fund['ma5']:.4f}" if fund['ma5'] is not None else 'N/A',
                    f"{fund['ma10']:.4f}" if fund['ma10'] is not None else 'N/A',
                    f"{fund['recent_change']:.2f}%",
                    fund.get('match_reason', '未知原因'),
                    fund.get('industry_name', ''),  # 🆕 所属行业（批量获取后添加）
                    # 🆕 估值相关数据
                    f"{fund.get('estimate_value', 0):.4f}" if fund.get('estimate_value', 0) > 0 else '',  # 实时估算净值
                    f"{fund.get('estimate_growth', 0):+.2f}%" if fund.get('estimate_value', 0) > 0 else '',  # 实时估算涨跌幅
                    fund.get('estimate_time', '') if fund.get('has_estimate', False) else '',  # 实时估值时间
                    fund.get('estimate_value_date', '') if fund.get('has_estimate', False) else '',  # 实时净值日期
                    f"{fund.get('estimate_unit_value', 0):.4f}" if fund.get('estimate_unit_value', 0) > 0 else '',  # 实时单位净值
                    "是" if fund.get('has_estimate', False) else "否",  # 有估值数据
                    # 🆕 添加收益率数据
                    f"{fund.get('hy_syl', '')}%" if fund.get('hy_syl') else '未知',  # 近半年收益率
                    f"{fund.get('year_syl', '')}%" if fund.get('year_syl') else '未知',  # 近一年收益率
                    f"{fund.get('try_syl', '')}%" if fund.get('try_syl') else '未知',  # 近三年收益率
                    f"{fund.get('sy_syl', '')}%" if fund.get('sy_syl') else '未知'  # 今年来收益率
                ])

    # 计算总耗时
    total_time = time.time() - start_time
    total_time_str = f"{int(total_time // 60)}分{int(total_time % 60)}秒" if total_time >= 60 else f"{total_time:.1f}秒"
    
    logger.info(f"🎯 调试完成！处理了 {len(fund_codes)} 个基金，发现 {len(qualified_funds)} 个符合条件")
    logger.info(f"📄 详细日志已保存")
    logger.info(f"⏱️ 总耗时: {total_time_str}")

    return {
        'total_processed': len(fund_codes),
        'qualified_count': len(qualified_funds),
        'qualified_funds': qualified_funds,
        'processing_log': processing_log
    }



def get_single_fund_estimate(fund_code, proxy=None, max_retries=2):
    """
    获取单个基金的估值数据（使用天天基金网接口）
    🔧 优化：添加重试机制和更好的错误处理

    参数:
    - fund_code: 基金代码，例如 '161725'
    - proxy: 代理配置
    - max_retries: 最大重试次数，默认2次

    返回:
    - 估值数据字典或None
    """
    if not fund_code:
        return None

    # 使用天天基金网的估值接口（更稳定）
    api_url = f"http://fundgz.1234567.com.cn/js/{fund_code}.js"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'http://fund.eastmoney.com/',
        'Accept': 'application/javascript, */*',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    }

    # 🔄 重试机制
    for attempt in range(max_retries + 1):
        try:
            # 添加时间戳避免缓存
            import time
            timestamp = int(time.time() * 1000)
            params = {'rt': timestamp}

            # 🔧 优化超时设置：连接超时10秒，读取超时15秒（减少超时错误）
            timeout = (10, 15)

            # 发送请求
            if proxy:
                response = requests.get(api_url, params=params, headers=headers, proxies=proxy, timeout=timeout)
            else:
                response = requests.get(api_url, params=params, headers=headers, timeout=timeout)

            if response.status_code == 200:
                # 🔧 检查响应内容长度，避免截断问题
                response_text = response.text.strip()

                # 🔧 验证响应完整性，但要区分空数据和真正的网络问题
                if len(response_text) < 10:  # 正常响应应该至少有10字符
                    if attempt < max_retries:
                        # 基金{fund_code}第{attempt+1}次尝试响应过短({len(response_text)}字符)，重试...
                        time.sleep(1)
                        continue
                    else:
                        print(f"❌ 基金{fund_code}响应过短: {response_text}")
                        return None
                
                # 🔧 检查是否为空估值数据（jsonpgz(); 格式）
                if response_text.strip() == 'jsonpgz();':
                    return {
                        'fund_code': fund_code,
                        'fund_name': '',
                        'jzrq': '',  # 净值日期
                        'dwjz': 0,  # 单位净值
                        'gsz': 0,  # 估算净值
                        'gszzl': 0,  # 估算涨跌幅
                        'gztime': '',  # 估值时间
                        'no_estimate_today': True  # 标记今日无估值
                    }

                # 处理可能的换行符和特殊字符，但保留%符号（可能在数据中）
                response_text = response_text.replace('\n', '').strip()

                if response_text.startswith('jsonpgz(') and response_text.endswith(');'):
                    json_str = response_text[8:-2]  # 去除 'jsonpgz(' 和 ');'

                    # 🔧 修复：处理空的JSONP响应（基金无估值数据）
                    if not json_str.strip():
                        # 返回空数据表示该基金今日无估值
                        return {
                            'fund_code': fund_code,
                            'fund_name': '',
                            'jzrq': '',  # 净值日期
                            'dwjz': 0,  # 单位净值
                            'gsz': 0,  # 估算净值
                            'gszzl': 0,  # 估算涨跌幅
                            'gztime': '',  # 估值时间
                            'no_estimate_today': True  # 标记今日无估值
                        }

                    try:
                        import json
                        data = json.loads(json_str)

                        # 🎯 成功解析，返回数据
                        return {
                            'fund_code': data.get('fundcode', fund_code),
                            'fund_name': data.get('name', ''),
                            'jzrq': data.get('jzrq', ''),  # 净值日期
                            'dwjz': float(data.get('dwjz', 0)) if data.get('dwjz') else 0,  # 单位净值
                            'gsz': float(data.get('gsz', 0)) if data.get('gsz') else 0,  # 估算净值
                            'gszzl': float(data.get('gszzl', 0)) if data.get('gszzl') else 0,  # 估算涨跌幅
                            'gztime': data.get('gztime', ''),  # 估值时间
                            'no_estimate_today': False  # 标记有估值数据
                        }
                    except json.JSONDecodeError as e:
                        if attempt < max_retries:
                            # 基金{fund_code}第{attempt+1}次尝试JSON解析失败: {e}，重试...
                            print(f"   响应内容: {response_text[:100]}...")
                            time.sleep(1)
                            continue
                        else:
                            print(f"❌ 基金{fund_code}JSON解析失败: {e}")
                            print(f"   响应内容: {response_text[:100]}...")
                            return None
                else:
                    if attempt < max_retries:
                        # 基金{fund_code}第{attempt+1}次尝试格式异常，重试...
                        time.sleep(1)
                        continue
                    else:
                        print(f"❌ 基金{fund_code}估值数据格式异常: {response_text[:50]}...")
                        return None
            else:
                if attempt < max_retries:
                    # 基金{fund_code}第{attempt+1}次尝试状态码{response.status_code}，重试...
                    time.sleep(1)
                    continue
                else:
                    print(f"❌ 基金{fund_code}估值请求失败，状态码: {response.status_code}")
                    return None

        except Exception as e:
            error_msg = str(e).lower()

            # 🆕 检查是否为代理相关的严重网络错误，需要立即淘汰代理
            is_critical_proxy_error = any(keyword in error_msg for keyword in [
                'proxyerror', 'remotedisconnected', 'remote end closed connection',
                'unable to connect to proxy', 'proxy connection failed',
                'connection aborted', 'connection refused', 'connection reset',
                'max retries exceeded', 'httpsconnectionpool', 'httpconnectionpool'
            ])

            # 🆕 如果是严重的代理错误，直接抛出异常让上层处理（立即换代理）
            if is_critical_proxy_error and proxy:
                print(f"🚫 基金{fund_code}代理严重错误: {e}")
                # 直接抛出异常，让get_single_fund_estimate_with_proxy处理代理切换
                raise e

            # 其他错误按原有逻辑处理
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试异常: {e}，重试...
                time.sleep(1)
                continue
            else:
                print(f"❌ 基金{fund_code}获取估值数据异常: {e}")
                # 🆕 如果是代理相关错误但已达到最大重试次数，也要抛出异常
                if is_critical_proxy_error and proxy:
                    raise e
                return None

    # 🛑 所有重试都失败
    return None



def get_fund_tiantian_metrics(fund_code, proxy=None, max_retries=2):
    """
    获取基金核心指标数据（天天基金接口）

    参数:
    - fund_code: 基金代码，例如 '009941'
    - proxy: 代理配置
    - max_retries: 最大重试次数，默认2次

    返回:
    - 核心指标数据字典，包含STDDEV1(波动率/年)、MAXRETRA1(最大回撤/年)、SHARP1(夏普比率/年)
    """
    if not fund_code:
        return None

    # 天天基金核心指标接口
    api_url = "https://dgs.tiantianfunds.com/merge/m/api/jjxqy1_2"

    headers = {
        'Accept': '*/*',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Host': 'dgs.tiantianfunds.com',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    # 请求数据（同时获取核心指标和主题信息）
    data = {
        'cfhFundFInfo_fields': 'INVESTMENTIDEAR',
        'companyFields': 'TOTALSCALE',
        'fcode': fund_code,
        'fields': 'FCODE',
        'fundUniqueInfo_fIELDS': 'STDDEV1,MAXRETRA1,SHARP1',
        'fundUniqueInfo_fLFIELDS': 'FCODE',
        'indexfields': '_id',
        'relateThemeFields': 'FCODE,SEC_CODE,SEC_NAME,CORR_1Y,OL2TOP',
        'themeFields': 'SEC_CODE,SEC_NAME,RISK_ALL,CHANCE_ALL,ISSHOW,ISSHOWSCORE,CORR_1Y'
    }

    # 重试机制
    for attempt in range(max_retries + 1):
        try:
            # 🔧 优化超时设置：连接超时10秒，读取超时15秒（减少超时错误）
            timeout = (10, 15)

            # 发送POST请求
            if proxy:
                response = requests.post(api_url, headers=headers, data=data, proxies=proxy, timeout=timeout)
            else:
                response = requests.post(api_url, headers=headers, data=data, timeout=timeout)

            if response.status_code == 200:
                try:
                    response_data = response.json()

                    # 检查响应结构
                    if (response_data.get('success') and
                        response_data.get('data') and
                        response_data['data'].get('uniqueInfo')):

                        unique_info = response_data['data']['uniqueInfo']

                        # 处理核心指标数据
                        result = {'fund_code': fund_code}
                        
                        if unique_info and len(unique_info) > 0:
                            # 获取第一个元素的数据
                            metrics = unique_info[0]
                            result.update({
                                'stddev1': metrics.get('STDDEV1'),  # 波动率/年
                                'maxretra1': metrics.get('MAXRETRA1'),  # 最大回撤/年
                                'sharp1': metrics.get('SHARP1'),  # 夏普比率/年
                                'has_metrics': True
                            })
                        else:
                            # 无核心指标数据
                            result.update({
                                'stddev1': None,
                                'maxretra1': None,
                                'sharp1': None,
                                'has_metrics': False
                            })
                        
                        # 🆕 处理主题信息数据（作为行业信息的备用方案）
                        fund_relate_theme_info = response_data['data'].get('fundRelateThemeInfo')
                        if fund_relate_theme_info and len(fund_relate_theme_info) > 0:
                            # 获取第一个主题作为主要行业信息
                            theme_info = fund_relate_theme_info[0]
                            result.update({
                                'theme_industry_name': theme_info.get('SEC_NAME', ''),
                                'theme_industry_code': theme_info.get('SEC_CODE', ''),
                                'has_theme_industry': True
                            })
                        else:
                            # 无主题信息
                            result.update({
                                'theme_industry_name': '',
                                'theme_industry_code': '',
                                'has_theme_industry': False
                            })
                        
                        return result
                    else:
                        if attempt < max_retries:
                            # 基金{fund_code}第{attempt+1}次尝试响应结构异常，重试...
                            time.sleep(1)
                            continue
                        else:
                            print(f"❌ 基金{fund_code}响应结构异常")
                            return None

                except json.JSONDecodeError as e:
                    if attempt < max_retries:
                        # 基金{fund_code}第{attempt+1}次尝试JSON解析失败: {e}，重试...
                        time.sleep(1)
                        continue
                    else:
                        print(f"❌ 基金{fund_code}JSON解析失败: {e}")
                        return None
            else:
                if attempt < max_retries:
                    # 基金{fund_code}第{attempt+1}次尝试状态码{response.status_code}，重试...
                    time.sleep(1)
                    continue
                else:
                    print(f"❌ 基金{fund_code}天天基金指标请求失败，状态码: {response.status_code}")
                    return None

        except requests.exceptions.ReadTimeout as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试读取超时，重试...
                time.sleep(2)  # 超时后延迟更长时间
                continue
            else:
                print(f"❌ 基金{fund_code}读取超时: {e}")
                return None
        except requests.exceptions.ConnectTimeout as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试连接超时，重试...
                time.sleep(2)  # 超时后延迟更长时间
                continue
            else:
                print(f"❌ 基金{fund_code}连接超时: {e}")
                return None
        except Exception as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试异常: {e}，重试...
                time.sleep(1)
                continue
            else:
                print(f"❌ 基金{fund_code}获取天天基金指标异常: {e}")
                return None

    return None


def get_fund_core_metrics(fund_code, proxy=None, max_retries=2):
    """
    获取基金核心指标数据（新浪财经接口）

    参数:
    - fund_code: 基金代码，例如 '009941'
    - proxy: 代理配置
    - max_retries: 最大重试次数，默认2次

    返回:
    - 核心指标数据字典，包含最大回撤、夏普比率、波动率的最后一个时间周期数据
    """
    if not fund_code:
        return None

    # 新浪财经核心指标接口
    api_url = f"https://fund.sina.cn/api/core_metrics?symbol={fund_code}"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://fund.sina.cn/',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    }

    # 重试机制
    for attempt in range(max_retries + 1):
        try:
            # 添加时间戳避免缓存
            import time
            timestamp = int(time.time() * 1000)
            params = {'_t': timestamp}

            # 🔧 优化超时设置：连接超时10秒，读取超时15秒（减少超时错误）
            timeout = (10, 15)

            # 发送请求
            if proxy:
                response = requests.get(api_url, params=params, headers=headers, proxies=proxy, timeout=timeout)
            else:
                response = requests.get(api_url, params=params, headers=headers, timeout=timeout)

            if response.status_code == 200:
                try:
                    data = response.json()

                    # 检查响应结构
                    if (data.get('result') and
                        data['result'].get('data') and
                        'coreMetrics' in data['result']['data']):

                        core_metrics = data['result']['data']['coreMetrics']
                        result = {'fund_code': fund_code}

                        # 检查是否有核心指标数据
                        if not core_metrics:
                            # 基金没有核心指标数据，返回空值但标记为成功
                            result.update({
                                'max_drawdown_value': '',
                                'max_drawdown_period': '',
                                'sharpe_ratio_value': '',
                                'sharpe_ratio_period': '',
                                'volatility_value': '',
                                'volatility_period': '',
                                'no_core_metrics': True
                            })
                            return result

                        # 处理每个核心指标
                        for metric in core_metrics:
                            metric_name = metric.get('text', '')
                            metric_type = metric.get('type', '')
                            lists = metric.get('lists', [])

                            if lists:
                                # 获取最后一个元素的value值
                                last_item = lists[-1]
                                period_name = last_item.get('text', '')
                                value = last_item.get('value', '')

                                # 根据指标类型设置字段名
                                if metric_type == 'zdhc':  # 最大回撤
                                    result[f'{metric_name}_{period_name}'] = value
                                    result['max_drawdown_value'] = value
                                    result['max_drawdown_period'] = period_name
                                elif metric_type == 'xpbl':  # 夏普比率
                                    result[f'{metric_name}_{period_name}'] = value
                                    result['sharpe_ratio_value'] = value
                                    result['sharpe_ratio_period'] = period_name
                                elif metric_type == 'bdl':  # 波动率
                                    result[f'{metric_name}_{period_name}'] = value
                                    result['volatility_value'] = value
                                    result['volatility_period'] = period_name

                        return result
                    else:
                        if attempt < max_retries:
                            # 基金{fund_code}第{attempt+1}次尝试响应结构异常，重试...
                            time.sleep(1)
                            continue
                        else:
                            print(f"❌ 基金{fund_code}响应结构异常")
                            return None

                except json.JSONDecodeError as e:
                    if attempt < max_retries:
                        # 基金{fund_code}第{attempt+1}次尝试JSON解析失败: {e}，重试...
                        time.sleep(1)
                        continue
                    else:
                        print(f"❌ 基金{fund_code}JSON解析失败: {e}")
                        return None
            else:
                if attempt < max_retries:
                    # 基金{fund_code}第{attempt+1}次尝试状态码{response.status_code}，重试...
                    time.sleep(1)
                    continue
                else:
                    print(f"❌ 基金{fund_code}核心指标请求失败，状态码: {response.status_code}")
                    return None

        except Exception as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试异常: {e}，重试...
                time.sleep(1)
                continue
            else:
                print(f"❌ 基金{fund_code}获取核心指标异常: {e}")
                return None

    return None


def get_fund_industry(fund_code, proxy=None, max_retries=2):
    """
    获取基金所属行业信息（新浪财经接口 + 天天基金主题信息备用方案）

    参数:
    - fund_code: 基金代码，例如 '015967'
    - proxy: 代理配置
    - max_retries: 最大重试次数，默认2次

    返回:
    - 行业信息字典，包含行业名称和代码
    """
    if not fund_code:
        return None

    # 首先尝试新浪财经行业信息接口
    sina_result = _get_fund_industry_from_sina(fund_code, proxy, max_retries)
    
    # 如果新浪接口获取到了行业信息，直接返回
    if sina_result and sina_result.get('has_industry'):
        return sina_result
    
    # 🆕 如果新浪接口没有获取到行业信息，尝试从天天基金主题信息获取
    tiantian_result = _get_fund_industry_from_tiantian_theme(fund_code, proxy, max_retries)
    
    if tiantian_result and tiantian_result.get('has_industry'):
        return tiantian_result
    
    # 都没有获取到，返回空值
    return {
        'fund_code': fund_code,
        'industry_name': '',
        'industry_code': '',
        'industry_vi_code': '',
        'has_industry': False,
        'source': 'none'
    }

def _get_fund_industry_from_sina(fund_code, proxy=None, max_retries=2):
    """
    从新浪财经接口获取基金行业信息
    """
    # 新浪财经行业信息接口
    api_url = f"https://quotes.sina.cn/app/api/openapi.php/ClientCnHqService.getHqPageIndex"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://quotes.sina.cn/',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    }

    # 重试机制
    for attempt in range(max_retries + 1):
        try:
            # 请求参数
            params = {
                'market': 'fund',
                'symbol': fund_code,
                'version': '8.15.0'
            }

            # 🔧 优化超时设置：连接超时10秒，读取超时15秒（减少超时错误）
            timeout = (10, 15)

            # 发送请求
            if proxy:
                response = requests.get(api_url, params=params, headers=headers, proxies=proxy, timeout=timeout)
            else:
                response = requests.get(api_url, params=params, headers=headers, timeout=timeout)

            if response.status_code == 200:
                try:
                    data = response.json()

                    # 检查响应结构
                    if (data.get('result') and
                        data['result'].get('data')):

                        industry_data = data['result']['data'].get('industry')

                        if industry_data and industry_data.get('name'):
                            # 有行业信息
                            return {
                                'fund_code': fund_code,
                                'industry_name': industry_data.get('name', ''),
                                'industry_code': industry_data.get('code', ''),
                                'industry_vi_code': industry_data.get('vi_code', ''),
                                'has_industry': True,
                                'source': 'sina'
                            }
                        else:
                            # 无行业信息
                            return {
                                'fund_code': fund_code,
                                'industry_name': '',
                                'industry_code': '',
                                'industry_vi_code': '',
                                'has_industry': False,
                                'source': 'sina_empty'
                            }
                    else:
                        if attempt < max_retries:
                            # 基金{fund_code}第{attempt+1}次尝试响应结构异常，重试...
                            time.sleep(1)
                            continue
                        else:
                            print(f"❌ 基金{fund_code}响应结构异常")
                            return None

                except json.JSONDecodeError as e:
                    if attempt < max_retries:
                        # 基金{fund_code}第{attempt+1}次尝试JSON解析失败: {e}，重试...
                        time.sleep(1)
                        continue
                    else:
                        print(f"❌ 基金{fund_code}JSON解析失败: {e}")
                        return None
            else:
                if attempt < max_retries:
                    # 基金{fund_code}第{attempt+1}次尝试状态码{response.status_code}，重试...
                    time.sleep(1)
                    continue
                else:
                    print(f"❌ 基金{fund_code}行业信息请求失败，状态码: {response.status_code}")
                    return None

        except requests.exceptions.ReadTimeout as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试行业信息读取超时，重试...
                time.sleep(2)  # 超时后延迟更长时间
                continue
            else:
                print(f"❌ 基金{fund_code}行业信息读取超时: {e}")
                return None
        except requests.exceptions.ConnectTimeout as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试行业信息连接超时，重试...
                time.sleep(2)  # 超时后延迟更长时间
                continue
            else:
                print(f"❌ 基金{fund_code}行业信息连接超时: {e}")
                return None
        except Exception as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试异常: {e}，重试...
                time.sleep(1)
                continue
            else:
                print(f"❌ 基金{fund_code}获取行业信息异常: {e}")
                return None

    return None


def _get_fund_industry_from_tiantian_theme(fund_code, proxy=None, max_retries=2):
    """
    从天天基金主题信息接口获取基金行业信息（作为备用方案）
    
    参数:
    - fund_code: 基金代码
    - proxy: 代理配置
    - max_retries: 最大重试次数，默认2次
    
    返回:
    - 行业信息字典，如果获取到主题信息则作为行业信息返回
    """
    if not fund_code:
        return None

    # 天天基金主题信息接口
    api_url = "https://dgs.tiantianfunds.com/merge/m/api/jjxqy1_2"

    headers = {
        'Accept': '*/*',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Host': 'dgs.tiantianfunds.com',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    # 重试机制
    for attempt in range(max_retries + 1):
        try:
            # 请求参数（使用更完整的参数来获取主题信息）
            data = {
                'cfhFundFInfo_fields': 'INVESTMENTIDEAR',
                'companyFields': 'TOTALSCALE',
                'fcode': fund_code,
                'fields': 'FCODE',
                'fundUniqueInfo_fIELDS': 'STDDEV1,MAXRETRA1,SHARP1',
                'fundUniqueInfo_fLFIELDS': 'FCODE',
                'indexfields': '_id',
                'relateThemeFields': 'FCODE,SEC_CODE,SEC_NAME,CORR_1Y,OL2TOP',
                'themeFields': 'SEC_CODE,SEC_NAME,RISK_ALL,CHANCE_ALL,ISSHOW,ISSHOWSCORE,CORR_1Y'
            }

            # 🔧 优化超时设置：连接超时10秒，读取超时15秒（减少超时错误）
            timeout = (10, 15)

            # 发送POST请求
            if proxy:
                response = requests.post(api_url, headers=headers, data=data, proxies=proxy, timeout=timeout)
            else:
                response = requests.post(api_url, headers=headers, data=data, timeout=timeout)

            if response.status_code == 200:
                try:
                    response_data = response.json()

                    # 检查响应结构
                    if (response_data.get('success') and
                        response_data.get('data')):

                        # 获取主题信息
                        fund_relate_theme_info = response_data['data'].get('fundRelateThemeInfo')
                        
                        if fund_relate_theme_info and len(fund_relate_theme_info) > 0:
                            # 获取第一个主题作为行业信息
                            theme_info = fund_relate_theme_info[0]
                            theme_name = theme_info.get('SEC_NAME', '')
                            theme_code = theme_info.get('SEC_CODE', '')
                            
                            if theme_name:
                                return {
                                    'fund_code': fund_code,
                                    'industry_name': theme_name,
                                    'industry_code': theme_code,
                                    'industry_vi_code': '',  # 天天基金接口没有这个字段
                                    'has_industry': True,
                                    'source': 'tiantian_theme'
                                }
                        
                        # 无主题信息
                        return {
                            'fund_code': fund_code,
                            'industry_name': '',
                            'industry_code': '',
                            'industry_vi_code': '',
                            'has_industry': False,
                            'source': 'tiantian_theme_empty'
                        }
                    else:
                        if attempt < max_retries:
                            # 基金{fund_code}第{attempt+1}次尝试响应结构异常，重试...
                            time.sleep(1)
                            continue
                        else:
                            print(f"❌ 基金{fund_code}响应结构异常")
                            return None

                except json.JSONDecodeError as e:
                    if attempt < max_retries:
                        # 基金{fund_code}第{attempt+1}次尝试JSON解析失败: {e}，重试...
                        time.sleep(1)
                        continue
                    else:
                        print(f"❌ 基金{fund_code}JSON解析失败: {e}")
                        return None
            else:
                if attempt < max_retries:
                    # 基金{fund_code}第{attempt+1}次尝试状态码{response.status_code}，重试...
                    time.sleep(1)
                    continue
                else:
                    print(f"❌ 基金{fund_code}天天基金主题信息请求失败，状态码: {response.status_code}")
                    return None

        except requests.exceptions.ReadTimeout as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试主题信息读取超时，重试...
                time.sleep(2)  # 超时后延迟更长时间
                continue
            else:
                print(f"❌ 基金{fund_code}主题信息读取超时: {e}")
                return None
        except requests.exceptions.ConnectTimeout as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试主题信息连接超时，重试...
                time.sleep(2)  # 超时后延迟更长时间
                continue
            else:
                print(f"❌ 基金{fund_code}主题信息连接超时: {e}")
                return None
        except Exception as e:
            if attempt < max_retries:
                # 基金{fund_code}第{attempt+1}次尝试异常: {e}，重试...
                time.sleep(1)
                continue
            else:
                print(f"❌ 基金{fund_code}获取主题信息异常: {e}")
                return None

    return None


def get_fund_industry_optimized(fund_code, proxy_pool, tiantian_data=None, max_proxy_retries=2):
    """
    优化的基金行业信息获取函数，避免重复调用jjxqy1_2接口
    
    参数:
    - fund_code: 基金代码
    - proxy_pool: 代理池对象
    - tiantian_data: 已获取的天天基金数据（包含主题信息）
    - max_proxy_retries: 最多尝试的代理数量，默认2个
    
    返回:
    - 行业信息字典，包含数据来源信息
    """
    if not fund_code or not proxy_pool:
        return None

    # 首先尝试新浪财经接口，使用性能跟踪
    sina_result = None
    try:
        sina_result = make_request_with_proxy_tracking(
            proxy_pool, 
            _get_fund_industry_from_sina, 
            fund_code, 
            max_retries=1
        )
        if sina_result and sina_result.get('has_industry'):
            return sina_result
    except Exception:
        pass
    
    # 如果第一次失败，再试一次
    if not sina_result or not sina_result.get('has_industry'):
        try:
            sina_result = make_request_with_proxy_tracking(
                proxy_pool, 
                _get_fund_industry_from_sina, 
                fund_code, 
                max_retries=1
            )
            if sina_result and sina_result.get('has_industry'):
                return sina_result
        except Exception:
            pass

    # 🚀 新浪财经接口都失败了，使用已获取的天天基金主题数据作为备用
    if tiantian_data and tiantian_data.get('theme_industry_name'):
        return {
            'fund_code': fund_code,
            'industry_name': tiantian_data['theme_industry_name'],
            'industry_code': tiantian_data.get('theme_industry_code', ''),
            'industry_vi_code': '',  # 天天基金接口没有这个字段
            'has_industry': True,
            'source': 'tiantian_theme_cached'  # 标记为缓存的天天基金主题数据
        }

    # 🛑 两个数据源都没有获取到行业信息
    return {
        'fund_code': fund_code,
        'industry_name': '',
        'industry_code': '',
        'industry_vi_code': '',
        'has_industry': False,
        'source': 'none'
    }


def get_fund_tiantian_metrics_with_proxy(fund_code, proxy_pool, max_proxy_retries=2):
    """
    使用代理池获取基金天天基金指标的包装函数

    参数:
    - fund_code: 基金代码
    - proxy_pool: 代理池对象
    - max_proxy_retries: 最多尝试的代理数量，默认2个

    返回:
    - 天天基金指标数据字典或None
    """
    if not fund_code or not proxy_pool:
        return None

    # 使用性能跟踪包装函数
    try:
        return make_request_with_proxy_tracking(
            proxy_pool, 
            get_fund_tiantian_metrics, 
            fund_code, 
            max_retries=1
        )
    except Exception:
        # 如果第一次失败，再试一次不同的代理
        try:
            return make_request_with_proxy_tracking(
                proxy_pool, 
                get_fund_tiantian_metrics, 
                fund_code, 
                max_retries=1
            )
        except Exception:
            return None


def get_fund_industry_with_proxy(fund_code, proxy_pool, max_proxy_retries=2):
    """
    使用代理池获取基金行业信息的包装函数

    参数:
    - fund_code: 基金代码
    - proxy_pool: 代理池对象
    - max_proxy_retries: 最多尝试的代理数量，默认2个

    返回:
    - 行业信息字典或None
    """
    if not fund_code:
        return None

    # 如果没有代理池，直接使用无代理方式获取
    if not proxy_pool:
        return get_fund_industry(fund_code, proxy=None, max_retries=1)

    current_proxy = None
    for retry_attempt in range(max_proxy_retries):
        # 获取代理
        if retry_attempt == 0:
            proxy = proxy_pool.get_proxy()
        else:
            # 重试时优先获取不同的代理
            proxy = proxy_pool.get_different_proxy(exclude_proxy=current_proxy)
            if not proxy:
                proxy = proxy_pool.get_proxy()

        current_proxy = proxy

        # 尝试获取行业信息
        result = get_fund_industry(fund_code, proxy=proxy, max_retries=1)

        if result:
            return result

        # 如果失败且不是最后一次尝试，继续下一个代理
        if retry_attempt < max_proxy_retries - 1:
            time.sleep(0.5)  # 短暂延迟

    return None


def get_fund_core_metrics_with_proxy(fund_code, proxy_pool, max_proxy_retries=2):
    """
    使用代理池获取基金核心指标的包装函数

    参数:
    - fund_code: 基金代码
    - proxy_pool: 代理池对象
    - max_proxy_retries: 最多尝试的代理数量，默认2个

    返回:
    - 核心指标数据字典或None
    """
    if not fund_code or not proxy_pool:
        return None

    current_proxy = None
    for retry_attempt in range(max_proxy_retries):
        # 获取代理
        if retry_attempt == 0:
            proxy = proxy_pool.get_proxy()
        else:
            # 重试时优先获取不同的代理
            proxy = proxy_pool.get_different_proxy(exclude_proxy=current_proxy)
            if not proxy:
                proxy = proxy_pool.get_proxy()

        current_proxy = proxy

        # 尝试获取核心指标数据
        result = get_fund_core_metrics(fund_code, proxy=proxy, max_retries=1)

        if result:
            return result

        # 如果失败且不是最后一次尝试，继续下一个代理
        if retry_attempt < max_proxy_retries - 1:
            time.sleep(0.5)  # 短暂延迟

    return None


def get_single_fund_estimate_with_proxy(fund_code, proxy_pool, max_proxy_retries=2):
    """
    🆕 使用代理池获取单个基金估值的包装函数
    实现您建议的智能代理替换策略：
    1. 优先从现有代理池中随机选择其他代理
    2. 依次尝试池中的其他代理
    3. 只有当所有代理都不可用时，才一次性重新加载整个代理池

    参数:
    - fund_code: 基金代码
    - proxy_pool: 代理池对象
    - max_proxy_retries: 最大代理重试次数，默认2次

    返回:
    - (fund_code, estimate_data) 元组
    """
    if not proxy_pool:
        estimate_data = get_single_fund_estimate(fund_code, None)
        return fund_code, estimate_data

    # 🆕 智能代理替换策略：先从池中随机选择，最后才重新加载
    current_proxy = None

    for proxy_attempt in range(max_proxy_retries + 1):
        # 🆕 第一次尝试：获取最佳代理
        if proxy_attempt == 0:
            current_proxy = proxy_pool.get_proxy()
        else:
            # 🆕 后续尝试：从池中随机选择不同的代理
            current_proxy = proxy_pool.get_different_proxy(exclude_proxy=current_proxy)

            # 如果没有其他可用代理，触发代理池检查和可能的重新加载
            if not current_proxy:
                print(f"⚠️ 基金{fund_code} 无其他可用代理，检查代理池状态...")
                proxy_pool._check_and_reload_proxy_pool_if_needed()
                current_proxy = proxy_pool.get_proxy()

        if not current_proxy:
            print(f"❌ 基金{fund_code} 无可用代理，终止尝试")
            break

        proxy_id = current_proxy.get('http', '')[:30] if current_proxy else 'Unknown'
        
        try:
            # 尝试获取估值数据，不进行重试（有问题立即换IP）
            estimate_data = get_single_fund_estimate(fund_code, current_proxy, max_retries=0)

            # 只要没有抛出异常，就认为代理工作正常（包括返回None的情况）
            proxy_pool.record_proxy_performance(current_proxy, True, 1.0)  # 标记成功
            return fund_code, estimate_data

        except Exception as e:
            error_msg = str(e).lower()

            # 🚀 检查是否为代理相关的网络错误（排除正常的无估值数据情况）
            is_proxy_error = any(keyword in error_msg for keyword in [
                'timeout', 'connection', 'proxy', 'httperror', 'network',
                'unreachable', 'refused', 'reset', 'read timed out', 'ssl',
                'proxyerror', 'remotedisconnected', 'max retries exceeded',
                'connection aborted', 'connection refused', 'unable to connect'
            ])

            # 🔧 特别检查：如果错误信息包含"jsonpgz();"，说明是正常的无估值数据，不是代理问题
            is_no_estimate_data = 'jsonpgz();' in str(e) or 'no_estimate_today' in error_msg

            if is_proxy_error and not is_no_estimate_data:
                # 真正的代理相关错误，立即淘汰这个代理
                proxy_pool.record_proxy_performance(current_proxy, False, None, is_network_error=True)
                print(f"🚫 基金{fund_code} 代理{proxy_id}发生网络错误: {e}，立即更换代理")

                # 如果不是最后一次尝试，立即换下一个代理
                if proxy_attempt < max_proxy_retries:
                    time.sleep(0.2)  # 短暂延迟后立即换代理
                    continue
            else:
                # 非代理相关错误或正常的无估值数据，记录失败但不立即淘汰代理
                proxy_pool.record_proxy_performance(current_proxy, False, None, is_network_error=False)
                if is_no_estimate_data:
                    print(f"📊 基金{fund_code} 今日无估值数据（非代理问题）")
                else:
                    print(f"⚠️ 基金{fund_code} 代理{proxy_id}发生其他错误: {e}")

        # 如果不是最后一次尝试，继续换代理
        if proxy_attempt < max_proxy_retries:
            time.sleep(0.3)  # 短暂延迟

    # 所有代理都失败
    return fund_code, None

def batch_get_fund_estimates(fund_codes, proxy_pool, max_workers=10):
    """
    多线程获取基金估值数据（单个获取模式，不支持批量API）
    
    参数:
    - fund_codes: 基金代码列表
    - proxy_pool: 代理池对象  
    - max_workers: 最大线程数，默认10个
    
    返回:
    - 所有基金的估值数据字典
    """
    if not fund_codes:
        return {}
    
    print(f"🚀 开始多线程获取估值数据，共{len(fund_codes)}只基金，{max_workers}个线程")
    
    all_estimates = {}
    successful_count = 0
    failed_count = 0
    no_estimate_count = 0  # 🔧 新增：记录无估值数据的基金数量
    
    # 使用线程池并发获取
    with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="EstimateWorker") as executor:
        # 提交所有任务
        future_to_fund = {}
        for fund_code in fund_codes:
            future = executor.submit(get_single_fund_estimate_with_proxy, fund_code, proxy_pool)
            future_to_fund[future] = fund_code
        
        # 处理完成的任务
        print(f"⏳ 已提交{len(fund_codes)}个获取任务，等待完成...")
        
        completed = 0
        for future in as_completed(future_to_fund):
            completed += 1
            fund_code = future_to_fund[future]
            
            try:
                result_fund_code, estimate_data = future.result()
                
                if estimate_data:
                    all_estimates[result_fund_code] = estimate_data
                    if estimate_data.get('no_estimate_today'):
                        no_estimate_count += 1  # 🔧 统计无估值数据的基金
                    successful_count += 1
                else:
                    failed_count += 1
                
                # 单行进度条显示（实时更新）
                success_rate = (successful_count / completed * 100) if completed > 0 else 0
                progress_percent = (completed / len(fund_codes)) * 100

                # 创建进度条
                bar_length = 25
                filled_length = int(bar_length * completed / len(fund_codes))
                bar = '█' * filled_length + '░' * (bar_length - filled_length)

                # 使用 \r 回到行首，覆盖之前的内容
                progress_msg = f"\r📊 估值获取: |{bar}| {completed}/{len(fund_codes)} ({progress_percent:.1f}%) 成功:{successful_count} 失败:{failed_count}"
                print(progress_msg, end='', flush=True)

                # 如果是最后一个任务，换行
                if completed == len(fund_codes):
                    print()  # 换行，完成进度条
                
            except Exception as e:
                failed_count += 1
                # 减少错误输出，避免干扰进度条
    
    # 计算最终统计
    total_count = len(fund_codes)
    success_rate = (successful_count / total_count * 100) if total_count > 0 else 0
    
    print(f"\n📈 估值数据获取完成:")
    print(f"  总基金数: {total_count}")
    print(f"  成功获取: {successful_count}")
    print(f"    - 有估值数据: {successful_count - no_estimate_count}")
    print(f"    - 无估值数据: {no_estimate_count} (基金暂停估值或今日无交易)")
    print(f"  获取失败: {failed_count}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate < 30:
        print(f"⚠️ 估值数据获取成功率较低，可能原因:")
        print("1. 部分基金代码不存在或已停止交易")
        print("2. 网络连接不稳定")
        print("3. 代理服务不稳定")
        print("4. API服务临时限制")
    elif success_rate >= 80:
        print(f"✅ 估值数据获取成功率良好！")
    else:
        print(f"📊 估值数据获取成功率中等")
    
    return all_estimates

if __name__ == "__main__":
    import sys

    # 检查命令行参数
    debug_mode = '--debug' in sys.argv or '-d' in sys.argv
    test_metrics = '--test-metrics' in sys.argv or '-tm' in sys.argv

    if test_metrics:
        print("🧪 核心指标API测试模式")
        print("="*80)
        test_core_metrics_api()
        sys.exit(0)
    elif debug_mode:
        print("🔍 基金均线上穿检测系统 - 调试模式")
        print("="*80)
        print("🎯 调试模式特点:")
        print("  - 只处理少量基金样本（20个）")
        print("  - 单线程处理，避免并发干扰")
        print("  - 详细记录每个处理步骤")
        print("  - 保存完整的调试日志")
        print("  - 消耗最少的代理和时间")
        print("="*80)



        print(f"\n🔧 开始调试模式...")
        result = test_small_sample_debug()

        if result:
            print(f"\n🎯 调试模式完成！")
            print(f"  处理基金: {result['total_processed']} 个")
            print(f"  符合条件: {result['qualified_count']} 个")
            print(f"📄 详细日志和结果文件已保存，可用于分析")
            print(f"\n💡 使用对比工具分析结果:")
            print(f"  python debug_fund_analyzer.py --analyze-logs")

    else:


        # 直接启动高性能模式
        results, stats_info = test_api_fund_list_concurrent()

        print(f"\n🎯 分析完成:")
        print(f"  - 总耗时: {stats_info['total_time_str']}")
        
        # 显示CSV文件保存路径（如果有的话）
        if 'csv_file_path' in stats_info:
            print(f"\n💾 结果已保存到: {stats_info['csv_file_path']}")
        else:
            print(f"\n📊 分析完成，未发现符合条件的基金")
        print(f"{'='*80}")