# 可用API接口汇总文档

## 文档概述

本文档汇总了经过curl测试验证的**可用**第三方API接口。所有接口均已通过实际测试，确保可以正常访问并返回有效数据。

**测试时间**: 2025年6月24日 16:33-17:45，2025年6月26日 RSI接口补充测试
**测试方法**: Python requests库自动化测试 + curl命令验证
**可用接口总数**: 67个
**失败接口数量**: 2个（CoinGecko需要代理）
**测试成功率**: 97.1%

---

## 🟢 基金相关接口 (9个)

### 1. 天天基金-基金实时数据 ✅
- **接口地址**: `https://fundgz.1234567.com.cn/js/{基金代码}.js`
- **请求方式**: GET
- **数据格式**: JSONP
- **示例**: `https://fundgz.1234567.com.cn/js/000001.js`
- **返回示例**: `jsonpgz({"fundcode":"000001","name":"华夏成长混合","jzrq":"2025-06-23","dwjz":"0.8240","gsz":"0.8341","gszzl":"1.23","gztime":"2025-06-24 15:00"});`

### 2. 天天基金-基金详情数据 ✅
- **接口地址**: `https://fund.eastmoney.com/pingzhongdata/{基金代码}.js`
- **请求方式**: GET
- **数据格式**: JavaScript
- **示例**: `https://fund.eastmoney.com/pingzhongdata/000001.js`
- **数据大小**: 714KB（包含完整基金信息）

### 3. 天天基金-基金近期表现 ✅
- **接口地址**: `https://api.fund.eastmoney.com/pinzhong/LJSYLZS`
- **请求方式**: GET
- **参数**: `fundCode=基金代码&indexcode=指数代码&type=时间类型`
- **请求头**: `Referer: https://fund.eastmoney.com/`
- **数据格式**: JSON
- **示例**: `https://api.fund.eastmoney.com/pinzhong/LJSYLZS?fundCode=000001&indexcode=000300&type=m`

### 4. 天天基金-基金估值图片 ✅
- **接口地址**: `https://j4.dfcfw.com/charts/pic6/{基金代码}.png`
- **请求方式**: GET
- **数据格式**: PNG图片
- **示例**: `https://j4.dfcfw.com/charts/pic6/000001.png`

### 5. 天天基金-投资风格图片 ✅
- **接口地址**: `https://j3.dfcfw.com/images/InvestStyle/{基金代码}.png`
- **请求方式**: GET
- **数据格式**: PNG图片
- **示例**: `https://j3.dfcfw.com/images/InvestStyle/000001.png`

### 6. 天天基金-所有基金信息 ✅
- **接口地址**: `https://fund.eastmoney.com/js/fundcode_search.js`
- **请求方式**: GET
- **数据格式**: JavaScript数组
- **数据大小**: 2.7MB（包含所有基金代码和名称）
- **返回格式**: `var r = [["基金代码","简拼","基金名称","基金类型","全拼"],...]`

### 7. 蚂蚁基金-QDII基金信息 ✅
- **接口地址**: `https://www.fund123.cn/matiaria?fundCode={基金代码}`
- **请求方式**: GET
- **数据格式**: HTML
- **示例**: `https://www.fund123.cn/matiaria?fundCode=000001`

### 8. 同花顺-基金RSI指标极值 ✅
- **接口地址**: `https://dq.10jqka.com.cn/fuyao/fund/default/v1/fund/indic`
- **请求方式**: GET
- **参数**: `tradeCodeList=基金代码&typeList=rsiBestLimitDown,rsiBestLimitUp`
- **请求头**:
  - `Accept: */*`
  - `Connection: keep-alive`
  - `User-Agent: PostmanRuntime-ApipostRuntime/1.1.0`
- **数据格式**: JSON
- **用途**: 获取基金RSI指标的高估区和低估区阈值
- **示例**: `https://dq.10jqka.com.cn/fuyao/fund/default/v1/fund/indic?tradeCodeList=018125&typeList=rsiBestLimitDown,rsiBestLimitUp`
- **返回示例**: `{"status_code":0,"data":{"018125":{"rsiBestLimitUp":"0.8","rsiBestLimitDown":"0.4"}},"status_msg":"Success"}`

### 9. 同花顺-基金RSI趋势数据 ✅
- **接口地址**: `https://fund.10jqka.com.cn/quotation/fund/v1/card/info`
- **请求方式**: POST
- **请求头**:
  - `Accept: */*`
  - `Connection: keep-alive`
  - `Content-Type: application/json`
  - `User-Agent: PostmanRuntime-ApipostRuntime/1.1.0`
- **数据格式**: JSON
- **用途**: 获取基金RSI指标的历史趋势数据和当前估值
- **请求体示例**:
```json
{
  "cardList": [
    {
      "ext": {
        "timeType": "1",
        "startTime": "20241201",
        "endTime": "20241226",
        "codeList": ["018125"]
      },
      "cardEnum": "RSI_TREND_V1"
    }
  ]
}
```
- **返回数据包含**:
  - `rsiValuation`: 当前RSI估值
  - `trendList`: RSI历史趋势数据（日期和数值）
  - `fundName`: 基金名称

---

## 🟢 股票相关接口 (8个)

### 1. 东方财富-指数行情 ✅
- **接口地址**: `https://push2.eastmoney.com/api/qt/stock/get`
- **请求方式**: GET
- **参数**: `secid=证券ID&fields=字段列表&_=时间戳`
- **数据格式**: JSON
- **示例**: `https://push2.eastmoney.com/api/qt/stock/get?secid=1.000001&fields=f43,f44,f45,f46,f57,f58,f60,f86,f107,f168,f169,f170,f171&_=时间戳`

### 2. 东方财富-股票K线数据 ✅
- **接口地址**: `https://push2his.eastmoney.com/api/qt/stock/kline/get`
- **请求方式**: GET
- **参数**: `secid=证券ID&fields1=字段组1&fields2=字段组2&klt=K线类型&fqt=复权类型&beg=开始日期&end=结束日期`
- **数据格式**: JSON
- **示例**: `https://push2his.eastmoney.com/api/qt/stock/kline/get?secid=1.000001&fields1=f1,f2,f3,f4,f5&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61&klt=101&fqt=1&beg=20240101&end=20241231`

### 3. 腾讯股票-日线数据 ✅
- **接口地址**: `https://web.ifzq.gtimg.cn/appstock/app/fqkline/get`
- **请求方式**: GET
- **参数**: `param=股票代码,day,,结束日期,数据条数,qfq`
- **数据格式**: JSON
- **示例**: `https://web.ifzq.gtimg.cn/appstock/app/fqkline/get?param=sh000001,day,,2025-01-28,10,qfq`

### 4. 腾讯股票-周线数据 ✅
- **接口地址**: `https://web.ifzq.gtimg.cn/appstock/app/fqkline/get`
- **请求方式**: GET
- **参数**: `param=股票代码,week,,结束日期,数据条数,qfq`
- **数据格式**: JSON
- **示例**: `https://web.ifzq.gtimg.cn/appstock/app/fqkline/get?param=sh000001,week,,2025-01-28,10,qfq`

### 5. 腾讯股票-月线数据 ✅
- **接口地址**: `https://web.ifzq.gtimg.cn/appstock/app/fqkline/get`
- **请求方式**: GET
- **参数**: `param=股票代码,month,,结束日期,数据条数,qfq`
- **数据格式**: JSON
- **示例**: `https://web.ifzq.gtimg.cn/appstock/app/fqkline/get?param=sh000001,month,,2025-01-28,10,qfq`

### 6. 腾讯股票-1分钟线 ✅
- **接口地址**: `https://ifzq.gtimg.cn/appstock/app/kline/mkline`
- **请求方式**: GET
- **参数**: `param=股票代码,m1,,数据条数`
- **数据格式**: JSON
- **示例**: `https://ifzq.gtimg.cn/appstock/app/kline/mkline?param=sh000001,m1,,10`

### 7. 腾讯股票-5分钟线 ✅
- **接口地址**: `https://ifzq.gtimg.cn/appstock/app/kline/mkline`
- **请求方式**: GET
- **参数**: `param=股票代码,m5,,数据条数`
- **数据格式**: JSON
- **示例**: `https://ifzq.gtimg.cn/appstock/app/kline/mkline?param=sh000001,m5,,10`

### 8. 新浪财经-K线数据含均线 ✅
- **接口地址**: `http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData`
- **请求方式**: GET
- **参数**: `symbol=股票代码&scale=时间周期&ma=均线参数&datalen=数据条数`
- **数据格式**: JSON
- **编码**: GBK
- **示例**: `http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData?symbol=sh000001&scale=240&ma=5&datalen=10`

---

## 🟢 新闻资讯接口 (5个)

### 1. 东方财富-股市直播 ✅
- **接口地址**: `https://newsapi.eastmoney.com/kuaixun/v1/getlist_zhiboall_ajaxResult_70_1_.html`
- **请求方式**: GET
- **参数**: `_=时间戳`
- **数据格式**: JavaScript
- **数据大小**: 88KB
- **示例**: `https://newsapi.eastmoney.com/kuaixun/v1/getlist_zhiboall_ajaxResult_70_1_.html?_=时间戳`

### 2. 东方财富-外汇新闻 ✅
- **接口地址**: `https://newsapi.eastmoney.com/kuaixun/v1/getlist_107_ajaxResult_50_1_.html`
- **请求方式**: GET
- **参数**: `_=时间戳`
- **数据格式**: JavaScript
- **数据大小**: 57KB

### 3. 东方财富-债券新闻 ✅
- **接口地址**: `https://newsapi.eastmoney.com/kuaixun/v1/getlist_108_ajaxResult_50_1_.html`
- **请求方式**: GET
- **参数**: `_=时间戳`
- **数据格式**: JavaScript
- **数据大小**: 61KB

### 4. 东方财富-基金新闻 ✅
- **接口地址**: `https://newsapi.eastmoney.com/kuaixun/v1/getlist_109_ajaxResult_50_1_.html`
- **请求方式**: GET
- **参数**: `_=时间戳`
- **数据格式**: JavaScript
- **数据大小**: 67KB

### 5. 东方财富-中国央行新闻 ✅
- **接口地址**: `https://newsapi.eastmoney.com/kuaixun/v1/getlist_118_ajaxResult_50_1_.html`
- **请求方式**: GET
- **参数**: `_=时间戳`
- **数据格式**: JavaScript
- **数据大小**: 66KB

---

## 🟢 行情数据接口 (6个)

### 1. 东方财富-板块行情 ✅
- **接口地址**: `https://push2.eastmoney.com/api/qt/clist/get`
- **请求方式**: GET
- **参数**: `fs=筛选条件&fields=字段列表&pn=页码&pz=页大小&po=排序方向&np=分页参数&fltt=过滤类型&invt=投资类型&fid=排序字段`
- **数据格式**: JSON
- **示例**: `https://push2.eastmoney.com/api/qt/clist/get?fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&pn=1&pz=20&po=1&np=1&fltt=2&invt=2&fid=f3`

### 2. 东方财富-外汇数据 ✅
- **接口地址**: `https://73.push2.eastmoney.com/api/qt/clist/get`
- **请求方式**: GET
- **参数**: `fs=m:133&fields=字段列表&_=时间戳`
- **数据格式**: JSON
- **示例**: `https://73.push2.eastmoney.com/api/qt/clist/get?pn=1&pz=10&po=1&fltt=2&invt=2&fid=f3&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&fs=m:133&_=时间戳`

### 3. 东方财富-全球债券 ✅
- **接口地址**: `https://quote.eastmoney.com/center/api/qqzq.js`
- **请求方式**: GET
- **参数**: `_=时间戳`
- **数据格式**: JavaScript
- **示例**: `https://quote.eastmoney.com/center/api/qqzq.js?_=时间戳`

### 4. 东方财富-热门主题 ✅
- **接口地址**: `https://quote.eastmoney.com/zhuti/api/hottheme`
- **请求方式**: GET
- **参数**: `startIndex=起始索引&pageSize=页大小`
- **数据格式**: JSON
- **示例**: `https://quote.eastmoney.com/zhuti/api/hottheme?startIndex=0&pageSize=20`

### 5. 东方财富-最近热点 ✅
- **接口地址**: `https://quote.eastmoney.com/zhuti/api/recenthot`
- **请求方式**: GET
- **参数**: `startIndex=起始索引&pageSize=页大小`
- **数据格式**: JSON
- **示例**: `https://quote.eastmoney.com/zhuti/api/recenthot?startIndex=0&pageSize=20`

### 6. 东方财富-风格指数 ✅
- **接口地址**: `https://quote.eastmoney.com/zhuti/api/fenggeindex`
- **请求方式**: GET
- **数据格式**: JSON
- **数据大小**: 5KB
- **示例**: `https://quote.eastmoney.com/zhuti/api/fenggeindex`

---

## 🟢 基金管理接口 (4个)

### 1. 东方财富-基金搜索API ✅
- **接口地址**: `http://fundsuggest.eastmoney.com/FundSearch/api/FundSearchAPI.ashx`
- **请求方式**: GET
- **参数**: `callback=回调函数&m=1&key=搜索关键词`
- **数据格式**: JSON
- **示例**: `http://fundsuggest.eastmoney.com/FundSearch/api/FundSearchAPI.ashx?callback=&m=1&key=华夏`

### 2. 东方财富-基金持仓数据API ✅
- **接口地址**: `http://fundf10.eastmoney.com/FundArchivesDatas.aspx`
- **请求方式**: GET
- **参数**: `type=持仓类型&code=基金代码&topline=返回条数&year=年份&month=月份`
- **请求头**: `Referer: http://fundf10.eastmoney.com/ccmx_{基金代码}.html`
- **数据格式**: HTML/JavaScript
- **数据大小**: 88KB
- **示例**: `http://fundf10.eastmoney.com/FundArchivesDatas.aspx?type=jjcc&code=000001&topline=10&year=2024&month=12`

### 3. 东方财富-基金公告API ✅
- **接口地址**: `http://api.fund.eastmoney.com/f10/JJGG`
- **请求方式**: GET
- **参数**: `callback=回调函数&fundcode=基金代码&pageIndex=页码&pageSize=每页条数&type=公告类型`
- **请求头**: `Referer: http://fundf10.eastmoney.com/jjgg_{基金代码}_3.html`
- **数据格式**: JSON
- **示例**: `http://api.fund.eastmoney.com/f10/JJGG?callback=&fundcode=000001&pageIndex=1&pageSize=20&type=3`

---

## 🟢 efinance项目接口 (17个)

### 1. efinance-股票实时行情列表 ✅
- **接口地址**: `http://push2.eastmoney.com/api/qt/clist/get`
- **请求方式**: GET
- **参数**: `pn=页码&pz=每页数量&po=排序方式&np=分页参数&fltt=过滤参数&invt=投资类型&fid=排序字段&fs=市场筛选&fields=返回字段列表`
- **数据格式**: JSON
- **示例**: `http://push2.eastmoney.com/api/qt/clist/get?pn=1&pz=5&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m:0%20t:6,m:0%20t:80,m:1%20t:2,m:1%20t:23&fields=f1,f2,f3,f4,f5,f6,f12,f14`

### 2. efinance-股票历史K线数据 ✅
- **接口地址**: `https://push2his.eastmoney.com/api/qt/stock/kline/get`
- **请求方式**: GET
- **参数**: `fields1=基础字段&fields2=K线字段&ut=用户标识&klt=K线类型&fqt=复权类型&secid=证券ID&beg=开始日期&end=结束日期`
- **数据格式**: JSON
- **数据大小**: 480KB
- **示例**: `https://push2his.eastmoney.com/api/qt/stock/kline/get?fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61&ut=7eea3edcaed734bea9cbfc24409ed989&klt=101&fqt=1&secid=1.600519&beg=0&end=20500101`

### 3. efinance-股票基本信息 ✅
- **接口地址**: `http://push2.eastmoney.com/api/qt/stock/get`
- **请求方式**: GET
- **参数**: `ut=用户标识&invt=投资类型&fltt=过滤参数&fields=返回字段&secid=证券ID`
- **数据格式**: JSON
- **示例**: `http://push2.eastmoney.com/api/qt/stock/get?ut=7eea3edcaed734bea9cbfc24409ed989&invt=2&fltt=2&fields=f57,f58,f162,f167,f127,f116,f117,f198,f173,f187,f105,f186&secid=1.600519`

### 4. efinance-股票成交明细 ✅
- **接口地址**: `https://push2.eastmoney.com/api/qt/stock/details/get`
- **请求方式**: GET
- **参数**: `ut=用户标识&fields1=基础字段&fields2=明细字段&secid=证券ID&pos=位置`
- **数据格式**: JSON
- **数据大小**: 110KB
- **示例**: `https://push2.eastmoney.com/api/qt/stock/details/get?ut=7eea3edcaed734bea9cbfc24409ed989&fields1=f1,f2,f3,f4&fields2=f51,f52,f53,f54,f55&secid=1.600519&pos=-0`

### 5. efinance-股票资金流向历史 ✅
- **接口地址**: `http://push2his.eastmoney.com/api/qt/stock/fflow/daykline/get`
- **请求方式**: GET
- **参数**: `lmt=限制条数&klt=K线类型&secid=证券ID&fields1=基础字段&fields2=资金流向字段`
- **数据格式**: JSON
- **示例**: `http://push2his.eastmoney.com/api/qt/stock/fflow/daykline/get?lmt=0&klt=101&secid=1.600519&fields1=f1,f2,f3,f7&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63,f64,f65`

### 6. efinance-股票分时资金流向 ✅
- **接口地址**: `http://push2.eastmoney.com/api/qt/stock/fflow/kline/get`
- **请求方式**: GET
- **参数**: `lmt=限制条数&klt=K线类型&secid=证券ID&fields1=基础字段&fields2=资金流向字段`
- **数据格式**: JSON
- **示例**: `http://push2.eastmoney.com/api/qt/stock/fflow/kline/get?lmt=0&klt=1&secid=1.600519&fields1=f1,f2,f3,f7&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63`

### 7. efinance-股票分时走势 ✅
- **接口地址**: `http://push2his.eastmoney.com/api/qt/stock/trends2/get`
- **请求方式**: GET
- **参数**: `fields1=基础字段&fields2=走势字段&ut=用户标识&ndays=天数&iscr=是否复权&secid=证券ID`
- **数据格式**: JSON
- **示例**: `http://push2his.eastmoney.com/api/qt/stock/trends2/get?fields1=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13&fields2=f51,f52,f53,f54,f55,f56,f57,f58&ut=7eea3edcaed734bea9cbfc24409ed989&ndays=1&iscr=1&secid=1.600519`

### 8. efinance-多只股票最新行情 ✅
- **接口地址**: `https://push2.eastmoney.com/api/qt/ulist.np/get`
- **请求方式**: GET
- **参数**: `fltt=过滤类型&secids=证券ID列表&fields=返回字段`
- **数据格式**: JSON
- **示例**: `https://push2.eastmoney.com/api/qt/ulist.np/get?fltt=2&secids=1.600519,0.000001&fields=f1,f2,f3,f4,f12,f13,f14`

### 9. efinance-基金净值信息 ✅
- **接口地址**: `https://fundmobapi.eastmoney.com/FundMNewApi/FundMNFInfo`
- **请求方式**: GET
- **参数**: `FCODE=基金代码&deviceid=设备ID&plat=平台&product=产品&version=版本号`
- **特殊请求头**: 需要设置移动端User-Agent和GTOKEN
- **数据格式**: JSON
- **示例**: `https://fundmobapi.eastmoney.com/FundMNewApi/FundMNFInfo?FCODE=161725&deviceid=test&plat=Iphone&product=EFund&version=6.2.8`

### 10. efinance-基金基本信息 ✅
- **接口地址**: `https://fundmobapi.eastmoney.com/FundMNewApi/FundMNNBasicInformation`
- **请求方式**: GET
- **参数**: `FCODE=基金代码&deviceid=设备ID&plat=平台&product=产品&version=版本号`
- **特殊请求头**: 需要设置移动端User-Agent和GTOKEN
- **数据格式**: JSON
- **数据大小**: 3KB

### 11. efinance-基金持仓信息 ✅
- **接口地址**: `https://fundmobapi.eastmoney.com/FundMNewApi/FundMNIVInfoMultiple`
- **请求方式**: GET
- **参数**: `FCODE=基金代码&deviceid=设备ID&plat=平台&product=产品&version=版本号`
- **特殊请求头**: 需要设置移动端User-Agent和GTOKEN
- **数据格式**: JSON

### 12. efinance-基金阶段涨幅 ✅
- **接口地址**: `https://fundmobapi.eastmoney.com/FundMNewApi/FundMNPeriodIncrease`
- **请求方式**: GET
- **参数**: `FCODE=基金代码&deviceid=设备ID&plat=平台&product=产品&version=版本号`
- **特殊请求头**: 需要设置移动端User-Agent和GTOKEN
- **数据格式**: JSON

### 13. efinance-基金经理信息 ✅
- **接口地址**: `http://fundf10.eastmoney.com/jjjl_{基金代码}.html`
- **请求方式**: GET
- **数据格式**: HTML
- **数据大小**: 59KB
- **示例**: `http://fundf10.eastmoney.com/jjjl_161725.html`

### 14. efinance-可转债实时行情 ✅
- **接口地址**: `http://push2.eastmoney.com/api/qt/clist/get`
- **请求方式**: GET
- **参数**: `fs=b:MK0354&其他参数与股票行情相同`
- **数据格式**: JSON
- **示例**: `http://push2.eastmoney.com/api/qt/clist/get?pn=1&pz=3&po=1&np=1&fltt=2&invt=2&fid=f12&fs=b:MK0354&fields=f1,f2,f3,f4,f12,f14`

### 15. efinance-全部可转债信息 ✅
- **接口地址**: `http://datacenter-web.eastmoney.com/api/data/v1/get`
- **请求方式**: GET
- **参数**: `reportName=RPT_BOND_CB_LIST&columns=ALL&source=WEB&client=WEB&sortColumns=PUBLIC_START_DATE&sortTypes=-1&pageSize=50&pageNumber=1`
- **数据格式**: JSON
- **数据大小**: 325KB
- **示例**: `http://datacenter-web.eastmoney.com/api/data/v1/get?sortColumns=PUBLIC_START_DATE&sortTypes=-1&pageSize=50&pageNumber=1&reportName=RPT_BOND_CB_LIST&columns=ALL&source=WEB&client=WEB`

### 16. efinance-期货实时行情 ✅
- **接口地址**: `http://push2.eastmoney.com/api/qt/clist/get`
- **请求方式**: GET
- **参数**: `fs=m:113,m:114,m:115,m:8,m:142,m:225&其他参数与股票行情相同`
- **数据格式**: JSON
- **示例**: `http://push2.eastmoney.com/api/qt/clist/get?pn=1&pz=3&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m:113,m:114,m:115,m:8,m:142,m:225&fields=f1,f2,f3,f4,f12,f14`

### 17. efinance-搜索股票代码 ✅
- **接口地址**: `https://searchapi.eastmoney.com/api/suggest/get`
- **请求方式**: GET
- **参数**: `input=搜索关键词&type=搜索类型&token=访问令牌&count=返回数量`
- **数据格式**: JSON
- **示例**: `https://searchapi.eastmoney.com/api/suggest/get?input=贵州茅台&type=14&token=D43BF722C8E33BDC906FB84D85E326E8&count=5`

---

## 🟢 AKShare项目接口 (11个)

### 1. AKShare-百度股市通热搜股票 ✅
- **接口地址**: `https://finance.pae.baidu.com/selfselect/listsugrecomm`
- **请求方式**: GET
- **参数**: `bizType=wisexmlnew&dsp=iphone&product=search&style=tablelist&market=市场类型&type=时间类型&day=日期&hour=小时&pn=页码&rn=每页数量&finClientType=pc`
- **数据格式**: JSON
- **数据大小**: 2KB
- **示例**: `https://finance.pae.baidu.com/selfselect/listsugrecomm?bizType=wisexmlnew&dsp=iphone&product=search&style=tablelist&market=ab&type=今日&day=20250624&hour=10&pn=0&rn=5&finClientType=pc`

### 2. AKShare-东方财富股票实时行情 ✅
- **接口地址**: `https://push2.eastmoney.com/api/qt/stock/get`
- **请求方式**: GET
- **参数**: `fltt=2&invt=2&fields=字段列表&secid=证券ID`
- **数据格式**: JSON
- **示例**: `https://push2.eastmoney.com/api/qt/stock/get?fltt=2&invt=2&fields=f43,f57,f58,f169,f170,f46,f44,f51&secid=0.000001`

### 3. AKShare-金十数据比特币持仓报告 ✅
- **接口地址**: `https://datacenter-api.jin10.com/bitcoin_treasuries/list`
- **请求方式**: GET
- **特殊请求头**: `X-App-Id: lnFP5lxse24wPgtY`, `X-Version: 1.0.0`
- **数据格式**: JSON
- **数据大小**: 6KB
- **示例**: `https://datacenter-api.jin10.com/bitcoin_treasuries/list`

### 4. AKShare-东方财富期货交易所数据 ✅
- **接口地址**: `https://futsse-static.eastmoney.com/redis`
- **请求方式**: GET
- **参数**: `msgid=gnweb`
- **数据格式**: JSON
- **示例**: `https://futsse-static.eastmoney.com/redis?msgid=gnweb`

### 5. AKShare-东方财富期权市场 ✅
- **接口地址**: `https://23.push2.eastmoney.com/api/qt/clist/get`
- **请求方式**: GET
- **参数**: `pn=页码&pz=每页数量&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:10,m:12,m:140,m:141,m:151,m:163,m:226&fields=字段列表`
- **数据格式**: JSON
- **数据大小**: 5KB
- **示例**: `https://23.push2.eastmoney.com/api/qt/clist/get?pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:10,m:12,m:140,m:141,m:151,m:163,m:226&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21`

### 6. AKShare-东方财富外汇历史行情 ✅
- **接口地址**: `https://push2his.eastmoney.com/api/qt/stock/kline/get`
- **请求方式**: GET
- **参数**: `secid=证券ID&klt=K线类型&fqt=复权类型&lmt=数据条数&end=结束时间&iscca=1&fields1=字段组1&fields2=字段组2&ut=用户标识&forcect=1`
- **数据格式**: JSON
- **示例**: `https://push2his.eastmoney.com/api/qt/stock/kline/get?secid=133.USDCNH&klt=101&fqt=1&lmt=10&end=20500000&iscca=1&fields1=f1,f2,f3,f4,f5,f6,f7,f8&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63,f64&ut=f057cbcbce2a86e2866ab8877db1d059&forcect=1`

### 7. AKShare-新浪财经指数成分股 ✅
- **接口地址**: `https://vip.stock.finance.sina.com.cn/corp/go.php/vII_NewestComponent/indexid/{指数代码}.phtml`
- **请求方式**: GET
- **数据格式**: HTML
- **数据大小**: 32KB
- **示例**: `https://vip.stock.finance.sina.com.cn/corp/go.php/vII_NewestComponent/indexid/399639.phtml`

### 8. AKShare-金十数据上海黄金交易所报告 ✅
- **接口地址**: `https://cdn.jin10.com/data_center/reports/sge.json`
- **请求方式**: GET
- **数据格式**: JSON
- **数据大小**: 2.9MB
- **示例**: `https://cdn.jin10.com/data_center/reports/sge.json`

### 9. AKShare-东方财富外汇实时行情 ✅
- **接口地址**: `https://push2.eastmoney.com/api/qt/clist/get`
- **请求方式**: GET
- **参数**: `np=1&fltt=2&invt=2&fs=m:119,m:120,m:133&fields=字段列表&fid=f3&pn=页码&pz=每页数量&po=1&dect=1&wbp2u=|0|0|0|web`
- **数据格式**: JSON
- **示例**: `https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=2&invt=2&fs=m:119,m:120,m:133&fields=f12,f13,f14,f1,f2,f4,f3,f152,f17,f18,f15,f16&fid=f3&pn=1&pz=5&po=1&dect=1&wbp2u=|0|0|0|web`

### 10. AKShare-新浪财经期权历史数据 ✅
- **接口地址**: `https://stock.finance.sina.com.cn/futures/api/jsonp_v2.php//StockOptionDaylineService.getSymbolInfo`
- **请求方式**: GET
- **参数**: `symbol=期权代码`
- **特殊请求头**: `referer: https://stock.finance.sina.com.cn/option/quotes.html`
- **数据格式**: JSONP
- **数据大小**: 2KB
- **示例**: `https://stock.finance.sina.com.cn/futures/api/jsonp_v2.php//StockOptionDaylineService.getSymbolInfo?symbol=CON_OP_10003889`

### 11. AKShare-新浪财经美股历史数据 ✅
- **接口地址**: `https://finance.sina.com.cn/staticdata/us/{股票代码}`
- **请求方式**: GET
- **数据格式**: 文本/CSV
- **数据大小**: 103KB
- **示例**: `https://finance.sina.com.cn/staticdata/us/AAPL`

---

## 🟢 第三方数据源接口 (7个)

### 1. 百度股市通-股票K线数据 ✅
- **接口地址**: `https://finance.pae.baidu.com/selfselect/getstockquotation`
- **请求方式**: GET
- **参数**: `all=1&isIndex=false&isBk=false&isBlock=false&isFutures=false&isStock=true&newFormat=1&group=quotation_kline_ab&finClientType=pc&code=股票代码&start_time=开始时间&ktype=K线类型`
- **数据格式**: JSON
- **数据大小**: 182KB
- **示例**: `https://finance.pae.baidu.com/selfselect/getstockquotation?all=1&isIndex=false&isBk=false&isBlock=false&isFutures=false&isStock=true&newFormat=1&group=quotation_kline_ab&finClientType=pc&code=000001&start_time=2020-01-01%2000:00:00&ktype=1`

### 2. 百度股市通-股票分时数据 ✅
- **接口地址**: `https://finance.pae.baidu.com/selfselect/getstockquotation`
- **请求方式**: GET
- **参数**: `all=1&isIndex=false&isBk=false&isBlock=false&isFutures=false&isStock=true&newFormat=1&group=quotation_minute_ab&finClientType=pc&code=股票代码`
- **数据格式**: JSON
- **数据大小**: 105KB
- **示例**: `https://finance.pae.baidu.com/selfselect/getstockquotation?all=1&isIndex=false&isBk=false&isBlock=false&isFutures=false&isStock=true&newFormat=1&group=quotation_minute_ab&finClientType=pc&code=000001`

### 3. 新浪财经-股票搜索 ✅
- **接口地址**: `https://suggest3.sinajs.cn/suggest/key={关键词}`
- **请求方式**: GET
- **数据格式**: JavaScript (GBK编码)
- **数据大小**: 5KB
- **示例**: `https://suggest3.sinajs.cn/suggest/key=平安银行`

### 4. 腾讯证券-智能搜索 ✅
- **接口地址**: `https://smartbox.gtimg.cn/s3/`
- **请求方式**: GET
- **参数**: `v=2&q=关键词&t=all&c=1`
- **数据格式**: JavaScript (Unicode编码)
- **示例**: `https://smartbox.gtimg.cn/s3/?v=2&q=招商银行&t=all&c=1`

### 5. 亿牛网-历史股价 ✅
- **接口地址**: `https://eniu.com/chart/pricea/{股票代码}/t/all`
- **请求方式**: GET
- **数据格式**: JSON
- **数据大小**: 151KB
- **示例**: `https://eniu.com/chart/pricea/sz000001/t/all`
- **返回格式**: `{"date":["1991-04-03","1991-04-04",...],"price":[...]}`

### 6. 中国债券信息网-债券曲线树 ✅
- **接口地址**: `https://yield.chinabond.com.cn/cbweb-mn/yc/queryTree`
- **请求方式**: GET
- **参数**: `locale=zh_CN`
- **数据格式**: JSON
- **数据大小**: 53KB
- **示例**: `https://yield.chinabond.com.cn/cbweb-mn/yc/queryTree?locale=zh_CN`

### 7. 同花顺-问财首页 ✅
- **接口地址**: `http://www.iwencai.com/unifiedwap/home/<USER>
- **请求方式**: GET
- **数据格式**: HTML
- **数据大小**: 13KB
- **用途**: 获取服务器时间等基础信息

---

## ❌ 不可用接口 (2个)

### 1. CoinGecko-价格数据 ❌
- **接口地址**: `https://api.coingecko.com/api/v3/simple/price`
- **失败原因**: 超时（需要代理访问）
- **错误类型**: Timeout

### 2. CoinGecko-币种详情 ❌
- **接口地址**: `https://api.coingecko.com/api/v3/coins/bitcoin`
- **失败原因**: 超时（需要代理访问）
- **错误类型**: Timeout

---

## 📊 接口统计汇总

| 接口分类 | 可用数量 | 总数量 | 可用率 |
|---------|---------|--------|--------|
| 基金相关接口 | 9 | 9 | 100% |
| 股票相关接口 | 8 | 8 | 100% |
| 新闻资讯接口 | 5 | 5 | 100% |
| 行情数据接口 | 6 | 6 | 100% |
| 基金管理接口 | 4 | 4 | 100% |
| efinance项目接口 | 17 | 17 | 100% |
| AKShare项目接口 | 11 | 11 | 100% |
| 第三方数据源接口 | 7 | 7 | 100% |
| 虚拟货币接口 | 0 | 2 | 0% |
| **总计** | **67** | **69** | **97.1%** |

---

## 🔧 使用建议

### 1. 推荐数据源优先级
1. **东方财富**: 接口最稳定，数据最全面，推荐作为主要数据源
2. **腾讯证券**: 股票K线数据质量高，适合技术分析
3. **新浪财经**: 搜索功能强大，K线数据含均线
4. **百度股市通**: 数据丰富，适合作为补充数据源
5. **其他数据源**: 特定用途的专业数据

### 2. 接口调用注意事项
- **请求频率**: 建议每个接口请求间隔至少1秒
- **User-Agent**: 设置合适的浏览器标识
- **Referer**: 部分接口需要设置来源页面
- **编码处理**: 新浪接口返回GBK编码，需要转换
- **错误重试**: 实现合理的重试机制

### 3. 数据格式处理
- **JSON格式**: 大部分接口，直接解析
- **JSONP格式**: 需要提取JSON部分
- **JavaScript格式**: 需要执行或正则提取
- **图片格式**: 二进制数据，可转base64

### 4. 性能优化建议
- **并发控制**: 限制同时请求数量
- **数据缓存**: 对不常变化的数据进行缓存
- **连接复用**: 使用Session保持连接
- **压缩传输**: 启用gzip压缩

---

## 📝 curl测试命令示例

```bash
# 基金实时数据
curl "https://fundgz.1234567.com.cn/js/000001.js"

# 股票K线数据
curl "https://push2his.eastmoney.com/api/qt/stock/kline/get?secid=1.000001&fields1=f1,f2,f3,f4,f5&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61&klt=101&fqt=1&beg=20240101&end=20241231"

# 股市新闻
curl "https://newsapi.eastmoney.com/kuaixun/v1/getlist_zhiboall_ajaxResult_70_1_.html?_=$(date +%s)000"

# 基金搜索
curl "http://fundsuggest.eastmoney.com/FundSearch/api/FundSearchAPI.ashx?callback=&m=1&key=华夏"

# 股票搜索
curl "https://suggest3.sinajs.cn/suggest/key=平安银行"

# 基金RSI指标极值
curl -X GET "https://dq.10jqka.com.cn/fuyao/fund/default/v1/fund/indic?tradeCodeList=018125&typeList=rsiBestLimitDown,rsiBestLimitUp" \
  -H "Accept: */*" \
  -H "Connection: keep-alive" \
  -H "User-Agent: PostmanRuntime-ApipostRuntime/1.1.0" \
  --compressed

# 基金RSI趋势数据
curl -X POST "https://fund.10jqka.com.cn/quotation/fund/v1/card/info" \
  -H "Accept: */*" \
  -H "Connection: keep-alive" \
  -H "Content-Type: application/json" \
  -H "User-Agent: PostmanRuntime-ApipostRuntime/1.1.0" \
  --compressed \
  -d '{
    "cardList": [
      {
        "ext": {
          "timeType": "1",
          "startTime": "20241201",
          "endTime": "20241226",
          "codeList": ["018125"]
        },
        "cardEnum": "RSI_TREND_V1"
      }
    ]
  }'
```

---

**文档生成时间**: 2025年6月24日  
**测试环境**: macOS  
**Python版本**: 3.x  
**测试工具**: requests库  
**文档版本**: v1.0 (仅包含可用接口)

*注意：本文档中的接口仅供学习和研究使用，请遵守各网站的使用条款，不得用于商业用途。*
