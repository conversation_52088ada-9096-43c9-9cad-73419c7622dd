#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动邮箱购买和监控系统
- 自动购买777kami邮箱
- 自动获取邮箱配置
- 启动邮件监控
"""

import os
import sys
import time
import json
import requests
from colorama import Fore, Style, init
from datetime import datetime
import threading
import imaplib
import email
from email.header import decode_header
import re

# 初始化colorama
init(autoreset=True)

# EMOJI 字典
EMOJI = {
    "START": "🚀",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "WARNING": "⚠️",
    "INFO": "ℹ️",
    "WAIT": "⏳",
    "MAIL": "📧",
    "MONEY": "💰",
    "SETTINGS": "⚙️",
    "MONITOR": "📡",
    "STOP": "🛑"
}

class AutoEmailSystem:
    def __init__(self):
        self.username = None
        self.password = None
        self.client_id = None
        self.refresh_token = None
        self.proxies = self.get_proxy_config()
        self.seen_email_ids = set()
        self.monitoring = False
        # 添加Token缓存机制
        self.cached_access_token = None
        self.token_cache_time = None
        self.token_cache_duration = 3600  # Token缓存1小时
        
    def get_proxy_config(self):
        """获取代理配置 - 邮箱购买功能强制不使用代理"""
        print(f"{Fore.BLUE}{EMOJI['INFO']} 邮箱购买功能已配置为直连模式（不使用代理）{Style.RESET_ALL}")
        return None

    def purchase_email_account(self):
        """自动购买777kami邮箱账号 - 直接检查余额并购买"""
        print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['MONEY']} 777kami邮箱自动购买系统{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")

        try:
            # 固定API密钥
            api_key = "e25c40f5-96d8-46ae-8f2c-58492788a3a6"

            if not api_key:
                print(f"{Fore.RED}{EMOJI['ERROR']} 请在代码中设置真实的777kami API密钥{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}{EMOJI['INFO']} 使用演示模式，生成模拟邮箱账号...{Style.RESET_ALL}")
                return self.create_demo_account()

            # 检查余额
            balance, currency = self.get_account_balance(api_key)

            if balance is None:
                print(f"{Fore.RED}{EMOJI['ERROR']} 无法获取账户余额{Style.RESET_ALL}")
                return False

            # 检查余额是否大于0.03
            min_balance = 0.03
            if balance <= min_balance:
                print(f"{Fore.RED}{EMOJI['ERROR']} 余额不足！当前 {balance} {currency} <= {min_balance} {currency}{Style.RESET_ALL}")
                return False

            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 余额充足 ({balance} {currency})，开始购买...{Style.RESET_ALL}")

            # 调用777kami API购买邮箱
            purchased_account = self.call_777kami_purchase_api()

            if purchased_account:
                self.username = purchased_account["username"]
                self.password = purchased_account["password"]
                self.client_id = purchased_account["client_id"]
                self.refresh_token = purchased_account["refresh_token"]

                # 不保存到文件，直接使用
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 邮箱购买成功！{Style.RESET_ALL}")
                print(f"{Fore.CYAN}  📧 邮箱账号: {self.username}{Style.RESET_ALL}")
                print(f"{Fore.CYAN}  🔑 Client ID: {self.client_id}{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} 邮箱购买失败{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 邮箱购买/配置过程出错: {str(e)}{Style.RESET_ALL}")
            return False

    def create_demo_account(self):
        """创建演示账号"""
        try:
            # 演示模式：模拟余额检查
            print(f"{Fore.CYAN}{EMOJI['INFO']} 演示模式 - 模拟余额检查...{Style.RESET_ALL}")
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 演示账户余额: 100.00 USD (> 0.03){Style.RESET_ALL}")
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 余额充足，可以购买！{Style.RESET_ALL}")

            # 演示模式：生成模拟的邮箱账号信息
            import random
            import string

            demo_username = f"demo{''.join(random.choices(string.digits, k=7))}@outlook.com"
            demo_password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
            demo_client_id = "dbc8e03a-b00c-46bd-ae65-b683e7707cb0"  # 固定的Client ID
            demo_refresh_token = "DEMO_TOKEN_" + ''.join(random.choices(string.ascii_letters + string.digits + "!@#$%^&*", k=200))

            self.username = demo_username
            self.password = demo_password
            self.client_id = demo_client_id
            self.refresh_token = demo_refresh_token

            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 演示邮箱生成成功！{Style.RESET_ALL}")
            print(f"{Fore.CYAN}  📧 邮箱账号: {self.username}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}  🔑 Client ID: {self.client_id}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 注意：这是演示账号，实际使用请配置真实的777kami API密钥{Style.RESET_ALL}")

            return True

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 创建演示账号失败: {str(e)}{Style.RESET_ALL}")
            return False

    def get_account_balance(self, api_key):
        """获取账户余额"""
        try:
            balance_api_url = "https://www.777kami.com/api/v1/balance"
            headers = {
                "Authorization": api_key,
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }

            response = requests.get(balance_api_url, headers=headers, timeout=15, proxies=self.proxies)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    balance = data.get("data", 0)
                    currency = "USD"
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 账户余额: {balance} {currency}{Style.RESET_ALL}")
                    return balance, currency
                else:
                    print(f"{Fore.RED}{EMOJI['ERROR']} 获取余额失败: {data.get('msg', '未知错误')}{Style.RESET_ALL}")
                    return None, None
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} 余额查询失败，状态码: {response.status_code}{Style.RESET_ALL}")
                return None, None

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 查询余额出错: {str(e)}{Style.RESET_ALL}")
            return None, None

    def get_service_price(self, api_key, service_type="outlook", duration="1month"):
        """获取服务价格"""
        try:
            print(f"{Fore.CYAN}{EMOJI['INFO']} 正在查询服务价格...{Style.RESET_ALL}")
            
            price_api_url = "https://www.777kami.com/api/v1/products"  # 产品/价格查询API
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            params = {
                "service": service_type,
                "duration": duration
            }
            
            response = requests.get(
                price_api_url,
                headers=headers,
                params=params,
                timeout=15,
                proxies=self.proxies
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    price = data.get("price", 0)
                    currency = data.get("currency", "USD")
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 服务价格: {service_type}({duration}) = {price} {currency}{Style.RESET_ALL}")
                    return price, currency
                else:
                    print(f"{Fore.RED}{EMOJI['ERROR']} 获取价格失败: {data.get('message', '未知错误')}{Style.RESET_ALL}")
                    return None, None
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} 价格查询请求失败，状态码: {response.status_code}{Style.RESET_ALL}")
                return None, None
                
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 查询价格时出错: {str(e)}{Style.RESET_ALL}")
            return None, None

    def call_777kami_purchase_api(self):
        """调用777kami API购买邮箱 - 简化版本，余额已在外部检查"""
        try:
            print(f"{Fore.CYAN}{EMOJI['MONEY']} 正在调用777kami API购买邮箱...{Style.RESET_ALL}")

            # 777kami购买API配置
            api_url = "https://www.777kami.com/api/v1/order"  # 订单提交API
            api_key = "e25c40f5-96d8-46ae-8f2c-58492788a3a6"

            if not api_key:
                # 这种情况应该不会发生，因为在purchase_email_account中已经处理了
                print(f"{Fore.RED}{EMOJI['ERROR']} API密钥未设置{Style.RESET_ALL}")
                return None

            # 真实API调用 - 直接购买Outlook短效邮箱
            product_id = 1003  # Outlook短效-支持oauth2取件
            buy_id = 1003      # 使用相同的ID作为buy_id
            quantity = 1
            price = 0.03       # 已知价格

            # 购买邮箱
            headers = {
                "Authorization": api_key,
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }

            payload = {
                "pid": product_id,
                "buy_id": buy_id,
                "num": quantity
            }

            response = requests.post(
                api_url,
                headers=headers,
                json=payload,
                timeout=30,
                proxies=self.proxies
            )

            if response.status_code == 200:
                data = response.json()

                if data.get("code") == 200:
                    order_data = data.get("data", {})

                    # 解析卡密信息
                    if isinstance(order_data, dict) and "data" in order_data:
                        card_info = order_data["data"].strip()

                        # 解析卡密格式：账号----密码----clientId----授权令牌
                        parts = card_info.split("----")
                        if len(parts) >= 4:
                            username = parts[0].strip()
                            password = parts[1].strip()
                            client_id = parts[2].strip()
                            refresh_token = parts[3].strip()

                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 邮箱购买成功: {username}{Style.RESET_ALL}")

                            return {
                                "username": username,
                                "password": password,
                                "client_id": client_id,
                                "refresh_token": refresh_token
                            }

                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 订单数据格式异常{Style.RESET_ALL}")
                    return None
                else:
                    error_msg = data.get('msg', '未知错误')
                    print(f"{Fore.RED}{EMOJI['ERROR']} API返回错误: {error_msg}{Style.RESET_ALL}")
                    
                    # 如果是库存不足错误，尝试购买商品ID 1007
                    if "暂无库存" in error_msg or "库存不足" in error_msg or "缺货" in error_msg:
                        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 检测到库存不足，尝试购买商品ID 1007...{Style.RESET_ALL}")
                        return self.try_purchase_alternative_product(api_key, headers)
                    
                    return None
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} API请求失败，状态码: {response.status_code}{Style.RESET_ALL}")
                return None

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 调用购买API时出错: {str(e)}{Style.RESET_ALL}")
            return None

    def try_purchase_alternative_product(self, api_key, headers):
        """尝试购买备选商品ID 1007"""
        try:
            print(f"{Fore.CYAN}{EMOJI['MONEY']} 正在尝试购买备选商品 (ID: 1007)...{Style.RESET_ALL}")
            
            api_url = "https://www.777kami.com/api/v1/order"
            
            # 备选商品配置
            alternative_product_id = 1007
            alternative_buy_id = 1007
            quantity = 1

            payload = {
                "pid": alternative_product_id,
                "buy_id": alternative_buy_id,
                "num": quantity
            }

            response = requests.post(
                api_url,
                headers=headers,
                json=payload,
                timeout=30,
                proxies=self.proxies
            )

            if response.status_code == 200:
                data = response.json()

                if data.get("code") == 200:
                    order_data = data.get("data", {})

                    # 解析卡密信息
                    if isinstance(order_data, dict) and "data" in order_data:
                        card_info = order_data["data"].strip()

                        # 解析卡密格式：账号----密码----clientId----授权令牌
                        parts = card_info.split("----")
                        if len(parts) >= 4:
                            username = parts[0].strip()
                            password = parts[1].strip()
                            client_id = parts[2].strip()
                            refresh_token = parts[3].strip()

                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 备选邮箱购买成功 (ID: 1007): {username}{Style.RESET_ALL}")

                            return {
                                "username": username,
                                "password": password,
                                "client_id": client_id,
                                "refresh_token": refresh_token
                            }

                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 备选商品订单数据格式异常{Style.RESET_ALL}")
                    return None
                else:
                    print(f"{Fore.RED}{EMOJI['ERROR']} 备选商品API返回错误: {data.get('msg', '未知错误')}{Style.RESET_ALL}")
                    return None
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} 备选商品API请求失败，状态码: {response.status_code}{Style.RESET_ALL}")
                return None

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 购买备选商品时出错: {str(e)}{Style.RESET_ALL}")
            return None

    def save_account_to_file(self, account):
        """保存账号信息到outlooks.txt文件"""
        try:
            log_dir = "log"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            outlooks_file = os.path.join(log_dir, "outlooks.txt")
            account_line = f"{account['username']}----{account['password']}----{account['client_id']}----{account['refresh_token']}\n"
            
            with open(outlooks_file, "w", encoding="utf-8") as f:
                f.write(account_line)
            
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 账号信息已保存到 {outlooks_file}{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 保存账号信息失败: {str(e)}{Style.RESET_ALL}")
            return False

    def get_access_token(self):
        """获取访问令牌 - 使用缓存机制避免重复获取"""
        # 检查是否有缓存的Token且未过期
        if self.cached_access_token and self.token_cache_time:
            current_time = time.time()
            if current_time - self.token_cache_time < self.token_cache_duration:
                return self.cached_access_token
        
        # 缓存过期或不存在，获取新Token
        print(f"{Fore.CYAN}{EMOJI['WAIT']} 正在获取新的访问令牌...{Style.RESET_ALL}")
        try:
            headers = {
                'Host': 'login.microsoftonline.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
            }
            data = {
                "client_id": self.client_id,
                "refresh_token": self.refresh_token,
                "grant_type": "refresh_token",
            }

            token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"

            response = requests.post(token_url, headers=headers, data=data, timeout=10, proxies=self.proxies)
            response.raise_for_status()
            token_data = response.json()

            if "access_token" in token_data:
                access_token = token_data["access_token"]
                new_refresh_token = token_data.get("refresh_token")
                
                # 缓存新Token
                self.cached_access_token = access_token
                self.token_cache_time = time.time()
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 新Token获取成功并已缓存{Style.RESET_ALL}")
                
                # 更新refresh_token
                if new_refresh_token and new_refresh_token != self.refresh_token:
                    self.refresh_token = new_refresh_token
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} Refresh Token已更新{Style.RESET_ALL}")
                    
                return access_token
            elif "error" in token_data:
                error_msg = token_data.get("error_description", "获取access_token失败")
                print(f"{Fore.RED}{EMOJI['ERROR']} Token错误: {error_msg}{Style.RESET_ALL}")
                return None
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} Token响应格式不正确{Style.RESET_ALL}")
                return None

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 获取访问令牌失败: {str(e)}{Style.RESET_ALL}")
            return None

    def connect_imap(self):
        """连接IMAP服务器 - 使用与read_outlook_inbox.py相同的方法"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return None, "无法获取访问令牌"

            # IMAP OAuth2认证
            auth_string = f"user={self.username}\1auth=Bearer {access_token}\1\1"

            # 首先尝试商业版IMAP服务器
            try:
                mail = imaplib.IMAP4_SSL("outlook.office365.com", timeout=15)
                mail.authenticate("XOAUTH2", lambda x: auth_string.encode())
                return mail, None
            except imaplib.IMAP4.error as e_office:
                # 尝试个人版IMAP服务器
                try:
                    mail_alt = imaplib.IMAP4_SSL("imap-mail.outlook.com", timeout=15)
                    mail_alt.authenticate("XOAUTH2", lambda x: auth_string.encode())
                    return mail_alt, None
                except imaplib.IMAP4.error as e_personal:
                    error_msg = f"IMAP认证失败: {e_office}"
                    print(f"{Fore.RED}{EMOJI['ERROR']} {error_msg}{Style.RESET_ALL}")
                    return None, error_msg

        except Exception as e:
            error_msg = f"IMAP连接异常: {str(e)}"
            print(f"{Fore.RED}{EMOJI['ERROR']} {error_msg}{Style.RESET_ALL}")
            return None, error_msg

    def parse_email_details(self, mail, email_id):
        """解析邮件详情"""
        try:
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            if status != 'OK':
                return None
            
            msg = email.message_from_bytes(msg_data[0][1])
            
            # 解析主题
            subject = msg['subject']
            if subject:
                decoded_subject = decode_header(subject)[0]
                if decoded_subject[1]:
                    subject = decoded_subject[0].decode(decoded_subject[1])
                else:
                    subject = str(decoded_subject[0])
            else:
                subject = "无主题"
            
            # 解析发件人
            sender = msg['from']
            if sender:
                decoded_sender = decode_header(sender)[0]
                if decoded_sender[1]:
                    sender = decoded_sender[0].decode(decoded_sender[1])
                else:
                    sender = str(decoded_sender[0])
            else:
                sender = "未知发件人"
            
            # 解析日期
            date_str = msg['date']
            
            # 获取邮件内容
            content = ""
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        try:
                            content = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                            break
                        except:
                            continue
            else:
                try:
                    content = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                except:
                    content = str(msg.get_payload())
            
            # 提取验证码
            verification_code = self.extract_verification_code(content)
            
            return {
                'id': email_id.decode() if isinstance(email_id, bytes) else str(email_id),
                'subject': subject,
                'sender': sender,
                'date': date_str,
                'content': content,
                'verification_code': verification_code
            }
            
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 解析邮件失败: {str(e)}{Style.RESET_ALL}")
            return None

    def extract_verification_code(self, content):
        """从邮件内容中提取验证码"""
        if not content:
            return None
        
        # 多种验证码模式
        patterns = [
            r'verification code[:\s]*(\d{6})',
            r'verify code[:\s]*(\d{6})',
            r'验证码[：:\s]*(\d{6})',
            r'code[:\s]*(\d{6})',
            r'(\d{6})',  # 6位数字
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                return matches[0]
        
        return None

    def print_email_details(self, email_details):
        """打印邮件详情"""
        print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{EMOJI['MAIL']} 收到新邮件！ [{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['MAIL']} 邮箱账号: {self.username}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}📌 邮件主题: {email_details['subject']}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}👤 发件人: {email_details['sender']}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}📅 发送时间: {email_details['date']}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}🆔 邮件ID: {email_details['id']}{Style.RESET_ALL}")
        
        if email_details['verification_code']:
            print(f"{Fore.YELLOW}🎯 验证码: {email_details['verification_code']}{Style.RESET_ALL}")
        else:
            print(f"{Fore.GRAY}🎯 验证码: 未检测到{Style.RESET_ALL}")
        
        # 显示邮件内容前100个字符
        content_preview = email_details['content'][:200] if email_details['content'] else "无内容"
        print(f"\n{Fore.CYAN}📄 邮件内容:{Style.RESET_ALL}")
        print(f"{Fore.WHITE}{'─'*25}{Style.RESET_ALL}")
        print(f"{Fore.WHITE}{content_preview}{'...' if len(content_preview) >= 200 else ''}{Style.RESET_ALL}")
        print(f"{Fore.WHITE}{'─'*25}{Style.RESET_ALL}")

    def initialize_seen_emails(self):
        """初始化已见过的邮件ID列表，并显示最新邮件"""
        print(f"{Fore.YELLOW}{EMOJI['SETTINGS']} 正在初始化已有邮件列表...{Style.RESET_ALL}")
        
        mail, error = self.connect_imap()
        if error:
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化失败: {error}{Style.RESET_ALL}")
            return False
        
        try:
            # 首先选择收件箱
            status, _ = mail.select('inbox')
            if status != 'OK':
                print(f"{Fore.RED}{EMOJI['ERROR']} 无法选择收件箱{Style.RESET_ALL}")
                if mail:
                    try:
                        mail.logout()
                    except:
                        pass
                return False

            status, messages = mail.search(None, 'ALL')
            if status == 'OK':
                email_ids = messages[0].split()
                
                if email_ids:
                    # 获取最新邮件并显示
                    latest_email_id = email_ids[-1]
                    print(f"{Fore.CYAN}{EMOJI['MAIL']} 显示最新邮件（避免错过）:{Style.RESET_ALL}")
                    
                    latest_email_details = self.parse_email_details(mail, latest_email_id)
                    if latest_email_details:
                        self.print_email_details(latest_email_details)
                    
                    # 标记所有现有邮件为已见
                    for email_id in email_ids:
                        self.seen_email_ids.add(email_id.decode())
                    
                    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 已标记 {len(email_ids)} 封现有邮件{Style.RESET_ALL}")
                else:
                    print(f"{Fore.BLUE}{EMOJI['INFO']} 收件箱为空{Style.RESET_ALL}")
            
            mail.logout()
            return True
            
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化过程中出错: {str(e)}{Style.RESET_ALL}")
            if mail:
                try:
                    mail.logout()
                except:
                    pass
            return False

    def check_for_new_emails(self):
        """检查新邮件"""
        mail, error = self.connect_imap()
        if error:
            print(f"{Fore.RED}{EMOJI['ERROR']} 连接失败: {error}{Style.RESET_ALL}")
            return False
        
        try:
            # 首先选择收件箱
            status, _ = mail.select('inbox')
            if status != 'OK':
                print(f"{Fore.RED}{EMOJI['ERROR']} 无法选择收件箱{Style.RESET_ALL}")
                if mail:
                    try:
                        mail.logout()
                    except:
                        pass
                return False

            status, messages = mail.search(None, 'ALL')
            if status == 'OK':
                email_ids = messages[0].split()
                new_emails = []
                
                for email_id in email_ids:
                    email_id_str = email_id.decode()
                    if email_id_str not in self.seen_email_ids:
                        email_details = self.parse_email_details(mail, email_id)
                        if email_details:
                            new_emails.append(email_details)
                            self.seen_email_ids.add(email_id_str)
                
                if new_emails:
                    for email_details in new_emails:
                        self.print_email_details(email_details)
                
            mail.logout()
            return True
            
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 检查邮件时出错: {str(e)}{Style.RESET_ALL}")
            if mail:
                try:
                    mail.logout()
                except:
                    pass
            return False

    def start_monitoring(self):
        """开始邮件监控 - 使用共享的监听模块（持续监听模式）"""
        print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['MONITOR']} 邮箱监控系统{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['MAIL']} 监控账号: {self.username}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}🔑 账号密码: {self.password}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}⏰ 检查间隔: 5 秒 | 💡 按 Ctrl+C 停止{Style.RESET_ALL}")
        
        try:
            # 使用共享的邮箱监听模块
            from shared_email_monitor import SharedEmailMonitor
            
            # 创建监听器实例
            monitor = SharedEmailMonitor(
                username=self.username,
                client_id=self.client_id,
                refresh_token=self.refresh_token,
                proxies=self.proxies
            )
            
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 开始使用共享监听系统（持续监听模式）...{Style.RESET_ALL}")
            
            # 使用持续监听模式 - 即使找到验证码也不停止监听
            result = monitor.monitor_for_verification_code(
                max_attempts=0,  # 持续模式下此参数无效
                interval=5,
                continuous_mode=True  # 启用持续监听模式
            )
            
            # 如果监听被中断，显示结果
            if result:
                print(f"\n{Fore.GREEN}{EMOJI['SUCCESS']} 监听期间获取到验证码: {result}{Style.RESET_ALL}")
            
        except ImportError:
            print(f"{Fore.RED}{EMOJI['ERROR']} 无法导入共享邮箱监听模块，使用原始方法...{Style.RESET_ALL}")
            # 回退到原始监听方法
            self._start_monitoring_fallback()
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}{EMOJI['STOP']} 用户停止监控{Style.RESET_ALL}")
        except Exception as e:
            print(f"\n{Fore.RED}{EMOJI['ERROR']} 监控过程中出错: {str(e)}{Style.RESET_ALL}")
        finally:
            self.monitoring = False
            print(f"{Fore.BLUE}{EMOJI['INFO']} 邮件监控已停止{Style.RESET_ALL}")

    def _start_monitoring_fallback(self):
        """原始监听方法（作为后备方案）"""
        # 初始化已有邮件
        if not self.initialize_seen_emails():
            print(f"{Fore.RED}{EMOJI['ERROR']} 初始化失败{Style.RESET_ALL}")
            return False
        
        self.monitoring = True
        
        try:
            while self.monitoring:
                start_time = time.time()
                
                print(f"{Fore.CYAN}🔍 [{datetime.now().strftime('%H:%M:%S')}] 检查新邮件...{Style.RESET_ALL}", end='\r')
                
                self.check_for_new_emails()
                
                # 精确控制检查间隔
                elapsed_time = time.time() - start_time
                sleep_time = max(0, 5 - elapsed_time)
                time.sleep(sleep_time)
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}{EMOJI['STOP']} 用户停止监控{Style.RESET_ALL}")
        except Exception as e:
            print(f"\n{Fore.RED}{EMOJI['ERROR']} 监控过程中出错: {str(e)}{Style.RESET_ALL}")
        finally:
            self.monitoring = False
            print(f"{Fore.BLUE}{EMOJI['INFO']} 邮件监控已停止{Style.RESET_ALL}")

    def run(self):
        """运行完整的自动化流程 - 直接获取余额并购买新邮箱"""
        print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['START']} 自动邮箱购买和监控系统{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}{EMOJI['INFO']} 新流程：直接检查余额 > 0.03 并购买新邮箱{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")

        # 步骤1: 直接购买新邮箱（内部会检查余额）
        if not self.purchase_email_account():
            print(f"{Fore.RED}{EMOJI['ERROR']} 邮箱购买失败，程序退出{Style.RESET_ALL}")
            return False

        # 步骤2: 开始监控
        print(f"\n{Fore.GREEN}{EMOJI['SUCCESS']} 邮箱购买完成，即将开始监控...{Style.RESET_ALL}")
        time.sleep(2)

        self.start_monitoring()
        return True

def main():
    """主函数"""
    try:
        system = AutoEmailSystem()
        system.run()
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 程序运行出错: {str(e)}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 