from DrissionPage import ChromiumOptions, Chromium
import sys
import os
import logging
from dotenv import load_dotenv
import requests
import json

load_dotenv()


class BrowserManager:
    # 单例模式实现
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BrowserManager, cls).__new__(cls)
            cls._instance.browser = None
            cls._instance.cached_proxy = None
        return cls._instance

    def __init__(self):
        # __init__ 方法可能会被多次调用，但实例只有一个
        # 我们已经在 __new__ 中初始化了必要的属性
        pass

    def init_browser(self, user_agent=None):
        """初始化浏览器并返回浏览器实例和使用的代理信息"""
        co = self._get_browser_options(user_agent)
        self.browser = Chromium(co)
        # 返回浏览器实例和缓存的代理地址
        # 代理地址可能为 None 或 'no_proxy' 或 代理字符串
        return self.browser, self.cached_proxy

    def _get_dynamic_proxy(self, proxy_url):
        """从指定的URL获取动态代理"""
        try:
            logging.info(f"尝试从 {proxy_url} 获取动态代理...")
            response = requests.get(proxy_url, timeout=10)
            response.raise_for_status()  # 检查请求是否成功
            data = response.json()
            if data.get("code") == 200 and data.get("data") and data["data"].get("proxy_list"):
                proxy_info = data["data"]["proxy_list"][0]
                proxy_address = proxy_info.split(',')[0]  # 提取 IP:端口 部分
                logging.info(f"成功获取动态代理: {proxy_address}")
                # DrissionPage 需要的格式是 '协议://IP:端口' 或 'IP:端口'
                # 通常代理是 http 或 https，这里假设 http
                if not proxy_address.startswith(('http://', 'https://')):
                    proxy_address = f"http://{proxy_address}"
                return proxy_address
            else:
                logging.warning(f"从 {proxy_url} 获取代理失败，响应格式不符合预期: {data}")
                return None
        except requests.RequestException as e:
            logging.error(f"请求动态代理URL {proxy_url} 失败: {e}")
            return None
        except (json.JSONDecodeError, KeyError, IndexError) as e:
            logging.error(f"解析动态代理响应失败: {e}")
            return None

    def _get_browser_options(self, user_agent=None):
        """获取浏览器配置"""
        co = ChromiumOptions()
        co.set_argument('--window-size=300,800') # 设置默认的窗口大小
        co.set_argument('--window-position=750,45') # 尝试将窗口置于屏幕右侧边缘
        try:
            extension_path = self._get_extension_path()
            co.add_extension(extension_path)
        except FileNotFoundError as e:
            logging.warning(f"警告: {e}")

        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")

        # 检查缓存
        if self.cached_proxy:
            logging.info(f"使用缓存的代理: {self.cached_proxy}")
            co.set_proxy(self.cached_proxy)
        else:
            # 优先使用动态代理URL
            proxy_url = os.getenv("PROXY_URL")
            dynamic_proxy = None
            if proxy_url:
                dynamic_proxy = self._get_dynamic_proxy(proxy_url)

            if dynamic_proxy:
                logging.info(f"使用动态代理: {dynamic_proxy}")
                self.cached_proxy = dynamic_proxy
                co.set_proxy(self.cached_proxy)
            else:
                # 如果动态代理获取失败或未配置，则尝试使用 BROWSER_PROXY
                proxy = os.getenv("BROWSER_PROXY")
                if proxy:
                    logging.info(f"使用静态代理: {proxy}")
                    self.cached_proxy = proxy
                    co.set_proxy(self.cached_proxy)
                else:
                    logging.info("未配置代理")
                    # 可选：缓存未配置代理的状态，避免重复检查环境变量
                    # self.cached_proxy = 'no_proxy' # 使用特殊值标记

        co.auto_port()
        if user_agent:
            co.set_user_agent(user_agent)

        co.headless(
            os.getenv("BROWSER_HEADLESS", "True").lower() == "true"
        )  # 生产环境使用无头模式

        # Mac 系统特殊处理
        if sys.platform == "darwin":
            co.set_argument("--disable-gpu")

        return co

    def _get_extension_path(self):
        """获取插件路径"""
        root_dir = os.getcwd()
        extension_path = os.path.join(root_dir, "turnstilePatch")

        if hasattr(sys, "_MEIPASS"):
            extension_path = os.path.join(sys._MEIPASS, "turnstilePatch")

        if not os.path.exists(extension_path):
            raise FileNotFoundError(f"插件不存在: {extension_path}")

        return extension_path

    def quit(self):
        """关闭浏览器"""
        if self.browser:
            try:
                self.browser.quit()
            except:
                pass
