import requests
import logging
from colorama import Fore, Style
from urllib.parse import unquote

# 设置emoji和颜色常量
EMOJI = {
    "ERROR": "❌",
    "WARNING": "⚠️",
    "INFO": "ℹ️",
    "SUCCESS": "✅",
}

def get_token_from_cookie(cookie_value, translator=None):
    """
    从登录后的cookie中提取token信息
    
    Args:
        cookie_value (str): 包含认证信息的cookie值
        translator: 翻译器实例，用于多语言支持
        
    Returns:
        str: 提取出的token值，提取失败则返回None
    """
    try:
        # 尝试从cookie中解析token
        if not cookie_value:
            print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('token.cookie_empty') if translator else 'Cookie值为空，无法提取token'}{Style.RESET_ALL}")
            return None
        
        # WorkosCursorSessionToken可能需要URL解码
        decoded_cookie = unquote(cookie_value)
        
        # 通常token格式是 userID::token
        if "::" in decoded_cookie:
            parts = decoded_cookie.split("::")
            if len(parts) >= 2:
                token = parts[1]
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {translator.get('token.extracted') if translator else '成功从cookie提取token'}{Style.RESET_ALL}")
                return token
        
        # 如果没有分隔符，可能整个cookie就是token
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('token.no_separator') if translator else 'Cookie中未找到分隔符，使用整个cookie值作为token'}{Style.RESET_ALL}")
        return cookie_value
        
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} {translator.get('token.extract_error', error=str(e)) if translator else f'从cookie提取token时出错: {str(e)}'}{Style.RESET_ALL}")
        return None 