import uuid
import requests
import json
from http.server import BaseHTTPRequestHandler, HTTPServer
import time
from cursor_login import generate_pkce_pair, query_auth_poll
from urllib.parse import urlparse, parse_qs # For parsing GET query parameters

# EMOJI for logging (optional, but consistent with your style)
EMOJI = {
    "INFO": "ℹ️",
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "ERROR": "❌",
    "WAIT": "⏳"
}

def refresh_token_with_session(workos_session_token: str, proxies: dict = None, verbose: bool = True):
    """
    Refreshes the Cursor token using an existing WorkosCursorSessionToken.

    Args:
        workos_session_token: The WorkosCursorSessionToken cookie value.
        proxies: Optional dictionary of proxies for requests.
        verbose: If True, print progress messages.

    Returns:
        dict: The JSON data returned by the auth poll endpoint upon success, or None otherwise.
    """
    if verbose:
        print(f"{EMOJI['INFO']} 开始使用 WorkosCursorSessionToken 刷新 Token...")

    # 1. 生成 PKCE 对和 UUID
    pkce_pair = generate_pkce_pair()
    verifier = pkce_pair["verifier"]
    challenge = pkce_pair["challenge"]
    uuid_str = str(uuid.uuid4())
    if verbose:
        print(f"{EMOJI['INFO']} PKCE Verifier: {verifier[:10]}...")
        print(f"{EMOJI['INFO']} PKCE Challenge: {challenge[:10]}...")
        print(f"{EMOJI['INFO']} UUID: {uuid_str}")

    # 2. 模拟 loginDeepCallbackControl 请求
    if verbose:
        print(f"{EMOJI['INFO']} 尝试发起深度认证请求 (loginDeepCallbackControl)...")
    try:
        auth_response = requests.post(
            "https://www.cursor.com/api/auth/loginDeepCallbackControl",
            headers={
                "Accept": "*/*",
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cursor/0.48.6 Chrome/132.0.6834.210 Electron/34.3.4 Safari/537.36", # Example UA
                "Cookie": f"WorkosCursorSessionToken=user_{workos_session_token}"
            },
            json={
                "uuid": uuid_str,
                "challenge": challenge
            },
            timeout=10,
            proxies=proxies
        )
        if verbose:
            print(f"{EMOJI['INFO']} 深度认证请求状态码: {auth_response.status_code}")
        if auth_response.status_code == 200:
            if verbose:
                print(f"{EMOJI['SUCCESS']} 深度认证请求成功。")
        else:
            if verbose:
                print(f"{EMOJI['WARNING']} 深度认证请求返回非 200 状态码: {auth_response.status_code}")
            # 根据您的原始逻辑，即使这里失败，也继续尝试 poll
    except requests.exceptions.RequestException as e:
        if verbose:
            print(f"{EMOJI['ERROR']} 深度认证请求失败: {str(e)}")
        # 继续尝试 poll

    # 3. 轮询获取认证信息
    retry_attempts = 20  # 与 cursor_pro_keep_alive.py 中的逻辑一致
    if verbose:
        print(f"{EMOJI['INFO']} 开始轮询认证结果 (最多 {retry_attempts} 次)...")
    
    for i in range(retry_attempts):
        if verbose:
            print(f"{EMOJI['WAIT']} 等待登录中... ({i + 1}/{retry_attempts})")
        
        data = query_auth_poll(uuid_str, verifier, proxies=proxies)
        
        if data:
            if verbose:
                print(f"{EMOJI['SUCCESS']} 轮询成功，获取到认证数据。")
                # print(f"{EMOJI['INFO']} 完整响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data
        
        # time.sleep(1) # cursor_login.py 的 main 函数中有 sleep(5)，但 cursor_pro_keep_alive.py 中是 sleep(1)
                        # 为了与您的参考代码 (cursor_pro_keep_alive) 更接近，这里也假设 poll 内部处理或外部调用 sleep
                        # 原始的 query_auth_poll 内部没有 sleep

    if verbose:
        print(f"{EMOJI['ERROR']} 轮询超时或失败，未能获取 Token。")
    return None

# --- API Server Implementation ---
class TokenRefreshAPIHandler(BaseHTTPRequestHandler):
    def _send_response(self, status_code, data):
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode('utf-8'))

    def do_GET(self):
        parsed_path = urlparse(self.path)
        if parsed_path.path == '/refresh_token':
            query_params = parse_qs(parsed_path.query)
            
            workos_token = query_params.get('workos_session_token', [None])[0]
            proxies_str = query_params.get('proxies', [None])[0]
            
            proxies_data = None
            if proxies_str:
                try:
                    proxies_data = json.loads(proxies_str)
                    if not isinstance(proxies_data, dict):
                        self._send_response(400, {"error": "'proxies' parameter must be a valid JSON object string"})
                        return
                except json.JSONDecodeError:
                    self._send_response(400, {"error": "Invalid JSON string in 'proxies' parameter"})
                    return

            if not workos_token:
                self._send_response(400, {"error": "'workos_session_token' query parameter is required"})
                return
            
            print(f"{EMOJI['INFO']} API (GET): Received request for /refresh_token. Token: {workos_token[:20]}... Proxies: {proxies_data}")

            token_data = refresh_token_with_session(workos_token, proxies=proxies_data, verbose=False)

            if token_data:
                print(f"{EMOJI['SUCCESS']} API (GET): Token refresh successful.")
                self._send_response(200, token_data)
            else:
                print(f"{EMOJI['ERROR']} API (GET): Token refresh failed.")
                self._send_response(500, {"error": "Token refresh failed internally"})
        else:
            self._send_response(404, {"error": "Endpoint not found"})

    # Remove or comment out do_POST if only GET is supported
    # def do_POST(self):
    #     if self.path == '/refresh_token':
    #         content_length = int(self.headers.get('Content-Length', 0))
    #         if content_length == 0:
    #             self._send_response(400, {"error": "Request body is empty"})
    #             return

    #         post_data_bytes = self.rfile.read(content_length)
            
    #         try:
    #             post_data = json.loads(post_data_bytes.decode('utf-8'))
    #         except json.JSONDecodeError:
    #             self._send_response(400, {"error": "Invalid JSON in request body"})
    #             return

    #         workos_token = post_data.get('workos_session_token')
    #         proxies_data = post_data.get('proxies') # This should be a dict if provided

    #         if not workos_token:
    #             self._send_response(400, {"error": "'workos_session_token' is required in JSON body"})
    #             return
            
    #         print(f"{EMOJI['INFO']} API (POST): Received request for /refresh_token. Token: {workos_token[:20]}... Proxies: {proxies_data}")

    #         # Call the core logic, disable verbose for API context unless specifically enabled
    #         token_data = refresh_token_with_session(workos_token, proxies=proxies_data, verbose=False) 

    #         if token_data:
    #             print(f"{EMOJI['SUCCESS']} API (POST): Token refresh successful.")
    #             self._send_response(200, token_data)
    #         else:
    #             print(f"{EMOJI['ERROR']} API (POST): Token refresh failed.")
    #             self._send_response(500, {"error": "Token refresh failed internally"})
    #     else:
    #         self._send_response(404, {"error": "Endpoint not found"})

def run_api_server(server_class=HTTPServer, handler_class=TokenRefreshAPIHandler, port=8000):
    server_address = ('', port)
    httpd = server_class(server_address, handler_class)
    print(f"{EMOJI['INFO']} Starting Cursor Token Refresh API server on port {port}...")
    print(f"{EMOJI['INFO']} Send GET requests to: http://localhost:{port}/refresh_token?workos_session_token=YOUR_TOKEN[&proxies={{...}}] ")
    print(f"{EMOJI['INFO']} Example 'proxies' parameter (URL encoded): &proxies=%7B%22http%22%3A%22http%3A//yourproxy%3Aport%22%7D")
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        pass
    httpd.server_close()
    print(f"\n{EMOJI['INFO']} Server stopped.")

if __name__ == "__main__":
    import argparse
    import sys

    parser = argparse.ArgumentParser(description='Cursor Token 刷新工具')
    parser.add_argument('--token', '-t', required=False, help='WorkosCursorSessionToken值')
    parser.add_argument('--server', '-s', action='store_true', help='启动API服务器模式')
    parser.add_argument('--port', '-p', type=int, default=8000, help='API服务器端口 (默认: 8000)')
    parser.add_argument('--proxies', help='代理配置 (JSON格式)')

    args = parser.parse_args()

    if args.server:
        # 启动API服务器
        run_api_server(port=args.port)
    elif args.token:
        # 直接刷新token
        proxies_data = None
        if args.proxies:
            try:
                proxies_data = json.loads(args.proxies)
            except json.JSONDecodeError:
                print(f"{EMOJI['ERROR']} 代理配置JSON格式错误")
                sys.exit(1)

        print(f"{EMOJI['INFO']} 开始刷新Cursor Token...")
        token_data = refresh_token_with_session(args.token, proxies=proxies_data, verbose=True)

        if token_data:
            access_token = token_data.get("accessToken")
            print(f"\n{EMOJI['SUCCESS']} Token刷新成功!")
            print(f"{EMOJI['INFO']} 新的Access Token: {access_token}")

            # 可选：保存到文件
            with open("refreshed_token.txt", "w") as f:
                f.write(access_token)
            print(f"{EMOJI['INFO']} Token已保存到 refreshed_token.txt")
        else:
            print(f"{EMOJI['ERROR']} Token刷新失败")
            sys.exit(1)
    else:
        # 显示帮助信息
        print("=== Cursor Token 刷新工具 ===")
        print("使用方法:")
        print("1. 直接刷新token:")
        print("   python refresh_cursor_token.py --token YOUR_WORKOS_SESSION_TOKEN")
        print("2. 启动API服务器:")
        print("   python refresh_cursor_token.py --server")
        print("3. 带代理刷新:")
        print('   python refresh_cursor_token.py --token YOUR_TOKEN --proxies \'{"http":"http://proxy:port"}\'')
        print("\n使用 --help 查看详细参数说明")
        parser.print_help()