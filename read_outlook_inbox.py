import re
import sys
import json
# from msal import PublicClientApplication # MSAL不再需要
import requests # 用于调用 Graph API 和 token 端点
import imaplib # 用于 IMAP 操作
import email # 用于解析邮件内容
from email.header import decode_header # 用于解码邮件头
from email.utils import parsedate_to_datetime # +++ 用于解析邮件日期 +++
from datetime import datetime, timezone, timedelta # +++ 用于处理时间和时区 +++
import os # 新增os模块用于路径处理

# 邮件过期检查已禁用
# VERIFICATION_CODE_MAX_AGE_MINUTES = 1

def parse_account_info(line):
    """解析单行账户信息"""
    parts = line.strip().split('----')
    if len(parts) == 4:
        return {
            "username": parts[0], # email address
            "password": parts[1], # 密码在此新流程中可能不直接使用，但保留以防万一或用于其他目的
            "clientId": parts[2],
            "refresh_token": parts[3] # 第四部分现在是 refresh_token
        }
    return None

def get_access_token_from_refresh_token(refresh_token, client_id, proxies=None):
    """
    使用 refresh_token 和 client_id 获取 access_token。
    参考: https://www.chenxutan.com/d/1228.html
    """
    headers = {
        'Host': 'login.microsoftonline.com',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', # User-Agent可以根据需要更新
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
    }
    data = {
        "client_id": client_id,
        "refresh_token": refresh_token,
        "grant_type": "refresh_token",
    }
    
    # 根据参考资料，端点是 v2.0。标准的MS OAuth2 token端点。
    token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
    
    access_token = None
    new_refresh_token = None
    error_message = None

    try:
        response = requests.post(token_url, headers=headers, data=data, timeout=10, proxies=proxies)
        response.raise_for_status() # 检查 HTTP 错误
        token_data = response.json()

        if "access_token" in token_data:
            access_token = token_data["access_token"]
            new_refresh_token = token_data.get("refresh_token") # 有些流程会返回新的refresh_token
        elif "error" in token_data:
            error_message = token_data.get("error_description", "获取 access_token 失败")
        else:
            error_message = "获取 access_token 响应格式不正确。"
    except requests.exceptions.HTTPError as e:
        error_message = f"HTTP error: {e.response.status_code} - {e.response.text}"
    except requests.exceptions.RequestException as e:
        error_message = f"网络错误: {e}"
    except json.JSONDecodeError as e:
        error_message = f"JSON 解析错误: {e}"

    return access_token, new_refresh_token, error_message


def imap_authenticate_with_oauth2(username, access_token):
    """使用 OAuth2 (XOAUTH2) 方式通过 IMAP 连接到 Outlook。"""
    try:
        auth_string = f"user={username}\1auth=Bearer {access_token}\1\1"
        # outlook.office365.com 是商业版的IMAP服务器，个人版可能是 outlook.live.com 或其他
        # 鉴于txt中是 outlook.com 结尾，先尝试 outlook.office365.com
        # 如果不行，可能需要根据账户类型调整为例如 imap-mail.outlook.com
        mail = imaplib.IMAP4_SSL("outlook.office365.com", timeout=15)
        mail.authenticate("XOAUTH2", lambda x: auth_string.encode()) # XOAUTH2需要字节串
        return mail, None
    except imaplib.IMAP4.error as e_office:
        try:
            mail_alt = imaplib.IMAP4_SSL("imap-mail.outlook.com", timeout=15)
            mail_alt.authenticate("XOAUTH2", lambda x: auth_string.encode())
            return mail_alt, None
        except imaplib.IMAP4.error as e_personal:
            return None, f"IMAP 认证失败 for {username} on outlook.office365.com: {e_office}, Outlook.com: {e_personal}"
    except Exception as e_generic:
        return None, f"Generic IMAP connection/auth error for {username}: {e_generic}"


def decode_mail_header(header_string):
    """解码邮件头，处理可能的编码问题"""
    if not header_string:
        return ""
    decoded_parts = []
    for part, charset in decode_header(header_string):
        if isinstance(part, bytes):
            try:
                decoded_parts.append(part.decode(charset or 'utf-8', errors='replace'))
            except LookupError: # 如果charset未知
                decoded_parts.append(part.decode('utf-8', errors='replace'))
        else:
            decoded_parts.append(part)
    return "".join(decoded_parts)

def get_email_text_body(email_message):
    """从 email.message 对象中提取纯文本正文。"""
    text_body = ""
    if email_message.is_multipart():
        for part in email_message.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))
            # 优先获取 text/plain 部分，且不是附件
            if content_type == "text/plain" and "attachment" not in content_disposition:
                try:
                    payload = part.get_payload(decode=True)
                    charset = part.get_content_charset() or 'utf-8'
                    text_body = payload.decode(charset, errors='replace')
                    break # 找到第一个纯文本部分后即可停止
                except Exception:
                    # Minimal logging: print(f"解码邮件部分时出错: {e}")
                    continue # 继续尝试其他部分
    else: # 非 multipart 邮件，直接获取 payload
        content_type = email_message.get_content_type()
        if content_type == "text/plain":
            try:
                payload = email_message.get_payload(decode=True)
                charset = email_message.get_content_charset() or 'utf-8'
                text_body = payload.decode(charset, errors='replace')
            except Exception:
                # Minimal logging: print(f"解码邮件正文时出错: {e}")
                pass # Keep it silent
    return text_body.strip()

def extract_verification_code(email_body_text):
    """
    从邮件纯文本内容中提取常见的数字验证码。
    返回找到的第一个验证码 (字符串) 或 None。
    """
    if not email_body_text:
        return None

    # 优先匹配更明确的验证码模式
    # 0. 处理数字间带空格的验证码 (例如: 1 2 3 4 5 6), 提取并拼接数字
    match_with_spaces = re.search(r"(?:验证码|verification code|your code|code is|OTP|动态密码|校验码|激活码|pin is|security code|is your code|access code)[是:：\s]*((?:\b\d\b\s*){4,8})", email_body_text, re.IGNORECASE)
    if match_with_spaces:
        code_str = match_with_spaces.group(1)
        return "".join(re.findall(r'\d', code_str)) # 提取所有数字并连接

    # 0.1 另一种匹配数字间带空格的验证码，后面可能跟有文字 (更宽松)
    # 匹配 "is:" 或类似结构后跟的一串数字和空格，然后提取数字
    match_spaced_digits_after_keyword = re.search(r"(?:is|是|为):\s*((?:\d\s*)+)", email_body_text, re.IGNORECASE)
    if match_spaced_digits_after_keyword:
        code_str = match_spaced_digits_after_keyword.group(1)
        extracted_digits = "".join(re.findall(r'\d', code_str))
        if 4 <= len(extracted_digits) <= 8: # 验证提取出的数字长度
             return extracted_digits
             
    # 0.2 匹配单独一行，数字间有空格的情况，例如 "1 2 3 4 5 6" 在新行
    match_line_with_spaces = re.search(r"^\s*((?:\d\s*){4,8})\s*$", email_body_text, re.MULTILINE | re.IGNORECASE)
    if match_line_with_spaces:
        code_str = match_line_with_spaces.group(1)
        return "".join(re.findall(r'\d', code_str))


    # 1. 关键词后跟4-8位数字 (忽略大小写)
    match = re.search(r"(?:验证码|verification code|your code|code is|OTP|动态密码|校验码|激活码|pin is|security code|is your code|access code)[是:：\s]*(\b\d{4,8}\b)", email_body_text, re.IGNORECASE)
    if match:
        return match.group(1)

    # 2. 数字在特定短语之间 (例如: code: 123456)
    match = re.search(r":\s*(\b\d{4,8}\b)", email_body_text) # 冒号后跟数字
    if match:
        return match.group(1)
    
    # 3. 单独一行的6位数字 (常见验证码格式)
    # ^ 和 $ 匹配行的开始和结束，re.MULTILINE 使其作用于每一行
    match = re.search(r"^\s*(\d{6})\s*$", email_body_text, re.MULTILINE)
    if match:
        return match.group(1)

    # 4. 单独一行的4-8位数字
    match = re.search(r"^\s*(\d{4,8})\s*$", email_body_text, re.MULTILINE)
    if match:
        return match.group(1)

    # 5. 덜 구체적인 패턴: 4-8자리 숫자 뒤에 키워드가 오는 경우
    match = re.search(r"(\b\d{4,8}\b)(?:[\s]*(?:是|is your|is the|is|为|code|OTP))", email_body_text, re.IGNORECASE)
    if match:
        return match.group(1)
        
    # 6. 备用：简单匹配一个独立的6位数字（如果前面更精确的模式都失败了）
    match = re.search(r"\b(\d{6})\b", email_body_text)
    if match:
        return match.group(1)

    # 7. 备用：简单匹配一个独立的4-8位数字
    match = re.search(r"\b(\d{4,8})\b", email_body_text)
    if match:
        return match.group(1)
            
    return None

def fetch_latest_verification_code(username, current_refresh_token, client_id, proxies=None):
    """
    获取指定账户最新邮件中的验证码。

    Args:
        username (str): 邮箱地址。
        current_refresh_token (str): 当前的刷新令牌。
        client_id (str): OAuth2 客户端ID。

    Returns:
        tuple: (verification_code, updated_refresh_token)
               verification_code 为字符串或 None。
               updated_refresh_token 是可能已更新的刷新令牌。
    """
    print(f"--- 开始为账户 {username} 获取验证码 ---")
    access_token, new_refresh_token, token_error = get_access_token_from_refresh_token(current_refresh_token, client_id, proxies=proxies)

    if token_error:
        print(f"获取 Access Token 失败: {token_error}")
        # Return original refresh token if new one wasn't obtained before error
        return None, new_refresh_token or current_refresh_token, f"Token获取失败: {token_error}"
    
    if not access_token:
        print("未能获取 Access Token，但没有明确的错误信息。")
        return None, new_refresh_token or current_refresh_token, "未能获取Access Token"

    print("Access Token 获取成功。")
    if new_refresh_token and new_refresh_token != current_refresh_token:
        print(f"获取到新的 Refresh Token (建议更新): {new_refresh_token}")
    else:
        new_refresh_token = current_refresh_token # Ensure we return a valid token

    mail, imap_error = imap_authenticate_with_oauth2(username, access_token)
    if imap_error:
        print(f"IMAP 认证失败: {imap_error}")
        return None, new_refresh_token, f"IMAP认证失败: {imap_error}"
    if not mail:
        print("IMAP 连接对象为空，认证失败。")
        return None, new_refresh_token, "IMAP连接对象为空"

    print("IMAP 认证成功。正在读取收件箱...")
    verification_code_found = None
    final_error_message = None

    try:
        status, _ = mail.select("inbox")
        if status != 'OK':
            final_error_message = "无法选择INBOX"
            print(final_error_message)
            mail.logout()
            return None, new_refresh_token, final_error_message

        status, messages = mail.search(None, 'ALL') # 搜索所有邮件以获取ID列表
        if status != 'OK':
            final_error_message = "搜索邮件失败"
            print(final_error_message)
            mail.logout()
            return None, new_refresh_token, final_error_message

        email_ids = messages[0].split()
        if not email_ids:
            print("收件箱中没有邮件。")
            mail.logout()
            return None, new_refresh_token, "收件箱为空"
            
        latest_mail_id = email_ids[-1] # 获取最新邮件的ID
        print(f"正在获取最新邮件 (ID: {latest_mail_id.decode()})...")

        status, msg_data = mail.fetch(latest_mail_id, '(RFC822)')
        
        if status == 'OK':
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # Print email details for debugging
            subject = decode_mail_header(email_message["Subject"])
            from_ = decode_mail_header(email_message["From"])
            date_str = email_message["Date"] # 获取原始Date字符串
            parsed_date = None
            if date_str:
                try:
                    parsed_date = parsedate_to_datetime(date_str)
                except Exception as e_date:
                    print(f"无法解析邮件日期 '{date_str}': {e_date}")
            
            print(f"  最新邮件主题: {subject}")
            print(f"  最新邮件发件人: {from_}")
            print(f"  最新邮件原始日期: {date_str}")
            if parsed_date:
                print(f"  最新邮件解析后日期 (UTC): {parsed_date.astimezone(timezone.utc).strftime('%Y-%m-%d %H:%M:%S %Z')}")

                # 邮件时效性检查已禁用
                # now_utc = datetime.now(timezone.utc)
                # email_age = now_utc - parsed_date.astimezone(timezone.utc)
                # print(f"  当前UTC时间: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                # print(f"  邮件年龄: {email_age}")

                # 邮件过期检查已禁用，直接处理邮件
            else:
                print("警告: 未能解析邮件日期，无法判断验证码时效性，将尝试使用。")

            email_body = get_email_text_body(email_message)
            if email_body:
                print(f"    --- 邮件纯文本正文开始 ---")
                print(email_body)
                print(f"    --- 邮件纯文本正文结束 ---")
                code = extract_verification_code(email_body)
                if code:
                    verification_code_found = code
                    print(f"提取到验证码: {verification_code_found}")
                else:
                    print("在最新邮件中未找到验证码。")
            else:
                print("未能从最新邮件中提取到纯文本正文。")
        else:
            final_error_message = f"获取邮件内容失败 (ID: {latest_mail_id.decode()})"
            print(final_error_message)
        
        mail.logout()
    except Exception as e:
        final_error_message = f"处理邮件时发生错误: {str(e)}"
        print(final_error_message)
        if mail:
            try:
                mail.logout()
            except:
                pass
    
    print(f"--- 账户 {username} 处理完毕 ---")
    return verification_code_found, new_refresh_token, final_error_message

def main():
    outlook_file_path = os.path.join("log", "outlooks.txt") # 使用 log/outlooks.txt
    
    print(f"开始测试 {outlook_file_path} 的第一行记录...")

    if not os.path.exists(outlook_file_path):
        print(f"错误: 文件 {outlook_file_path} 未找到。")
        sys.exit(1)

    with open(outlook_file_path, 'r', encoding='utf-8') as f:
        first_line = f.readline().strip()

    if not first_line:
        print(f"错误: 文件 {outlook_file_path} 为空或第一行为空。")
        sys.exit(1)
    
    account = parse_account_info(first_line)
    if not account:
        print(f"错误: 解析第一行账户信息失败: {first_line}")
        sys.exit(1)

    username = account["username"]
    client_id = account["clientId"]
    initial_refresh_token = account["refresh_token"]

    print(f"正在测试账户: {username}")
    print(f"Client ID: {client_id}")
    print(f"初始 Refresh Token: {initial_refresh_token[:20]}...") # 打印部分以确认

    proxies = None
    http_proxy = os.getenv('HTTP_PROXY')
    https_proxy = os.getenv('HTTPS_PROXY')
    
    if http_proxy or https_proxy:
        proxies = {}
        if http_proxy:
            proxies['http'] = http_proxy
            print(f"检测到 HTTP_PROXY: {http_proxy}")
        if https_proxy:
            proxies['https'] = https_proxy
            print(f"检测到 HTTPS_PROXY: {https_proxy}")
        if proxies: # Ensure it's not an empty dict if only one was None but parsed as empty string by mistake
             print(f"将尝试使用代理: {proxies}")
        else: # Should not happen if logic is correct and http_proxy or https_proxy was non-empty
            proxies = None 

    verification_code, final_refresh_token, error_details = fetch_latest_verification_code(
        username, 
        initial_refresh_token, 
        client_id,
        proxies=proxies
    )

    print("\n--- 测试结果 ---")
    if verification_code:
        print(f"成功提取到验证码: {verification_code}")
    else:
        print("未能提取到验证码。")

    if error_details:
        print(f"错误信息: {error_details}")
    
    if final_refresh_token and final_refresh_token != initial_refresh_token:
        print(f"获取到的新 Refresh Token: {final_refresh_token}")
        print(f"注意: 如果获取到新的 Refresh Token，建议使用它更新 {outlook_file_path} 中的记录。")
    elif final_refresh_token:
        print(f"Refresh Token 未改变: {final_refresh_token[:20]}...")
    else: # Should not happen if initial_refresh_token was valid
        print("最终 Refresh Token 为空，可能在获取过程中发生严重错误。")

if __name__ == '__main__':
    # 检查 requests 库，imaplib 和 email 是标准库
    try:
        import requests
    except ImportError:
        print("错误: 必需的 'requests' 库未安装。请运行: pip install requests", file=sys.stderr)
        sys.exit(1)
        
    main() 