# Cursor Pro 自动化工具

## 环境要求
- Python 3.8 或更高版本
- Git

## 安装步骤

1. 克隆项目
```bash
git clone https://github.com/yourusername/cursor-auto-pro.git
cd cursor-auto-pro
```

2. 创建并激活虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# Windows 激活虚拟环境
venv\Scripts\activate

# macOS/Linux 激活虚拟环境
source venv/bin/activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑 .env 文件，填入必要的配置信息
```

## 启动方式

### 方式一：直接启动
```bash
# 确保在项目根目录下且虚拟环境已激活
python cursor_pro_keep_alive.py
```

### 方式二：快捷启动（推荐）
在 `~/.zshrc`（macOS/Linux）或 `~/.bashrc`（Linux）中添加以下别名配置：

```bash
# Cursor Auto Pro
alias cursor-auto="cd /path/to/your/cursor-auto-pro && source venv/bin/activate && python cursor_pro_keep_alive.py"
```

配置后执行 `source ~/.zshrc` 或 `source ~/.bashrc` 使配置生效。之后可以在终端中任意位置输入 `cursor-auto` 来启动项目。

### 方式三：虚拟环境启动
```bash
# 确保在项目根目录下且虚拟环境已激活
./venv/bin/python cursor_pro_keep_alive.py
```

## 注意事项
- 首次使用需要完整执行安装步骤
- 确保 `.env` 文件中的配置信息正确
- 使用过程中遇到问题可以查看在线文档或加入交流群

## 在线文档
[cursor-auto-free-doc.vercel.app](https://cursor-auto-free-doc.vercel.app)

## 功能说明

### 机器码重置（已升级）
本工具包含全面的机器码重置功能，能够彻底更新Cursor的机器标识，防止试用期到期。最新版本支持：

- 重置storage.json中的所有机器标识
- 更新SQLite数据库中的机器标识记录
- 重写machineId文件
- 针对不同操作系统（Windows/macOS/Linux）提供专用处理
- 针对Cursor 0.45.0以上版本提供getMachineId函数修补

使用方法：
```bash
# 直接运行重置脚本
python reset_machine.py

# 或者通过主程序选择"仅重置机器码"选项
python cursor_pro_keep_alive.py  # 然后选择选项1
```
