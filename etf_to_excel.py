#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF基金列表获取并导出Excel工具
基于新浪财经接口获取ETF数据并导出为Excel
"""

import requests
import pandas as pd
import json
from datetime import datetime

class ETFToExcelExporter:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://quotes.sina.com.cn/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })
    
    def get_etf_from_sina(self):
        """从新浪财经获取ETF排行榜数据（支持自动分页）"""
        url = "https://quotes.sina.com.cn/cn/api/openapi.php/CN_ETFService.getETFRankList"
        all_etf_list = []
        page_index = 1
        page_size = 2000  # 每页数量（设置较大值以提高效率）
        total_count = 0
        
        print("正在从新浪财经获取ETF数据...")
        
        while True:
            params = {
                'pageIndex': page_index,
                'pageSize': page_size,
                'asc': 0,
                'sort': 'change',
                'filter': '',
                'type': 'all'
            }
            
            try:
                print(f"正在获取第 {page_index} 页数据...")
                response = self.session.get(url, params=params, timeout=15)
                response.raise_for_status()
                
                data = response.json()
                
                if 'result' not in data or 'data' not in data['result']:
                    print(f"第 {page_index} 页API返回格式异常: {data}")
                    break
                
                result_data = data['result']['data']
                
                # 获取总数量（第一页时）
                if page_index == 1:
                    total_count = result_data.get('total', 0)
                    print(f"发现总共 {total_count} 个ETF基金")
                
                # 获取当前页数据
                if 'data' in result_data:
                    current_page_data = result_data['data']
                else:
                    current_page_data = result_data
                
                if not current_page_data or len(current_page_data) == 0:
                    print(f"第 {page_index} 页无数据，获取完成")
                    break
                
                all_etf_list.extend(current_page_data)
                print(f"第 {page_index} 页获取到 {len(current_page_data)} 个ETF，累计 {len(all_etf_list)} 个")
                
                # 如果当前页数据少于page_size，说明已经是最后一页
                if len(current_page_data) < page_size:
                    print("已获取完所有数据")
                    break
                
                # 如果已获取数量达到总数，停止获取
                if len(all_etf_list) >= total_count:
                    print("已获取完所有数据")
                    break
                
                page_index += 1
                
            except Exception as e:
                print(f"获取第 {page_index} 页数据失败: {e}")
                break
        
        print(f"最终获取到 {len(all_etf_list)} 个ETF基金（总数: {total_count}）")
        return all_etf_list
    
    def parse_etf_data(self, raw_data):
        """解析ETF数据"""
        parsed_data = []
        
        for item in raw_data:
            try:
                # 处理可能为字符串"--"的数据
                def safe_float(value, default=0):
                    if value == '--' or value is None or value == '':
                        return default
                    try:
                        return float(value)
                    except:
                        return default
                
                etf_info = {
                    '基金代码': item.get('symbol', ''),
                    '基金名称': item.get('name', ''),
                    '现价': safe_float(item.get('price')),
                    '涨跌幅(%)': safe_float(item.get('change')),
                    '成交额': safe_float(item.get('amount')),
                    '换手率(%)': safe_float(item.get('change_speed')),
                    '溢价率(%)': safe_float(item.get('premium_rate')) if item.get('premium_rate') != '--' else '',
                    '净申购': safe_float(item.get('net_subscription')),
                    '净买入': safe_float(item.get('net_purchase')),
                    '总规模': safe_float(item.get('total_scale')),
                    '单位净值': safe_float(item.get('net_unit_value')),
                    '增长率(%)': safe_float(item.get('increase_rate')),
                    '最大回撤': safe_float(item.get('maximum_pullback')) if item.get('maximum_pullback') != '--' else '',
                    '夏普比率': safe_float(item.get('sharpe_ratio')),
                    '跟踪误差': safe_float(item.get('tracking_error')) if item.get('tracking_error') != '--' else '',
                    '基金经理': item.get('fund_manager', ''),
                    '托管人': item.get('custodian', ''),
                    '评级': item.get('grade', ''),
                }
                
                # 处理标志位
                flags = item.get('flags', {})
                if flags:
                    etf_info['融资融券'] = '是' if flags.get('rzrq', 0) == 1 else '否'
                    etf_info['沪深港通'] = '是' if flags.get('hs_gt', 0) == 1 else '否'
                    etf_info['T+0交易'] = '是' if flags.get('t0', 0) == 1 else '否'
                
                # 只添加有基金代码的数据
                if etf_info['基金代码']:
                    parsed_data.append(etf_info)
                
            except Exception as e:
                print(f"解析数据时出错: {e}")
                continue
        
        return parsed_data
    
    def export_to_excel(self, data, filename=None):
        """导出数据到Excel"""
        if not data:
            print("没有数据可导出")
            return None
        
        if filename is None:
            filename = f"ETF基金列表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        try:
            df = pd.DataFrame(data)
            
            # 按涨跌幅排序
            if '涨跌幅(%)' in df.columns:
                df = df.sort_values('涨跌幅(%)', ascending=False)
            
            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='ETF基金列表', index=False)
                
                # 获取工作表以设置格式
                worksheet = writer.sheets['ETF基金列表']
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"数据已导出到: {filename}")
            print(f"共导出 {len(df)} 个ETF基金")
            
            return filename
            
        except Exception as e:
            print(f"导出Excel时出错: {e}")
            return None
    
    def run(self):
        """执行主要流程"""
        print("=" * 50)
        print("ETF基金列表获取工具")
        print("数据源: 新浪财经")
        print("=" * 50)
        
        # 获取ETF数据
        sina_data = self.get_etf_from_sina()
        if not sina_data:
            print("未获取到ETF数据")
            return
        
        # 解析数据
        parsed_data = self.parse_etf_data(sina_data)
        if not parsed_data:
            print("数据解析失败")
            return
        
        # 导出到Excel
        filename = self.export_to_excel(parsed_data)
        
        if filename:
            print(f"\n任务完成！Excel文件已保存: {filename}")
        else:
            print("\nExcel导出失败")

def main():
    """主函数"""
    try:
        exporter = ETFToExcelExporter()
        exporter.run()
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n程序执行异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()