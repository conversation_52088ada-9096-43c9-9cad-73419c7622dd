import os
import sys
import json
import requests
import sqlite3
from typing import Dict, Optional
import platform
from colorama import Fore, Style, init
import logging
import re

# Initialize colorama
init()

# Setup logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define emoji constants
EMOJI = {
    "USER": "👤",
    "USAGE": "📊",
    "PREMIUM": "⭐",
    "BASIC": "📝",
    "SUBSCRIPTION": "💳",
    "INFO": "ℹ️",
    "ERROR": "❌",
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "TIME": "🕒"
}

class Config:
    """Config"""
    NAME_LOWER = "cursor"
    NAME_CAPITALIZE = "Cursor"
    BASE_HEADERS = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "application/json",
        "Content-Type": "application/json"
    }

class UsageManager:
    """Usage Manager"""
    
    @staticmethod
    def get_proxy():
        """get proxy"""
        # from config import get_config
        proxy = os.environ.get("HTTP_PROXY") or os.environ.get("HTTPS_PROXY")
        if proxy:
            return {"http": proxy, "https": proxy}
        return None
    
    @staticmethod
    def get_usage(token: str) -> Optional[Dict]:
        """get usage"""
        url = f"https://www.{Config.NAME_LOWER}.com/api/usage"
        headers = Config.BASE_HEADERS.copy()
        # 如果token以user_开头，直接使用token作为完整的cookie值
        if token.startswith("user_"):
            headers.update({"Cookie": f"Workos{Config.NAME_CAPITALIZE}SessionToken={token}"})
        else:
            # 否则，将token作为cookie值的一部分，添加前缀
            headers.update({"Cookie": f"Workos{Config.NAME_CAPITALIZE}SessionToken=user_01OOOOOOOOOOOOOOOOOOOOOOOO%3A%3A{token}"})
        try:
            proxies = UsageManager.get_proxy()
            response = requests.get(url, headers=headers, timeout=10, proxies=proxies)
            response.raise_for_status()
            data = response.json()
            #print(f"{Fore.MAGENTA}DEBUG: get_usage response:{Style.RESET_ALL}")
            #print(f"{Fore.MAGENTA}{json.dumps(data, indent=2, ensure_ascii=False)}{Style.RESET_ALL}")   
            
            # get Premium usage and limit
            gpt4_data = data.get("gpt-4", {})
            premium_usage = gpt4_data.get("numRequestsTotal", 0)
            max_premium_usage = gpt4_data.get("maxRequestUsage", 999)
            
            # get Basic usage, but set limit to "No Limit"
            gpt35_data = data.get("gpt-3.5-turbo", {})
            basic_usage = gpt35_data.get("numRequestsTotal", 0)
            
            return {
                'premium_usage': premium_usage, 
                'max_premium_usage': max_premium_usage, 
                'basic_usage': basic_usage, 
                'max_basic_usage': "No Limit"  # set Basic limit to "No Limit"
            }
        except requests.RequestException as e:
            # only log error
            logger.debug(f"Get usage info failed: {str(e)}")
            return None
        except Exception as e:
            # catch all other exceptions
            logger.debug(f"Get usage info failed: {str(e)}")
            return None

    @staticmethod
    def get_stripe_profile(token: str) -> Optional[Dict]:
        """get user subscription info"""
        url = f"https://api2.{Config.NAME_LOWER}.sh/auth/full_stripe_profile"
        headers = Config.BASE_HEADERS.copy()
        headers.update({"Authorization": f"Bearer {token}"})
        try:
            proxies = UsageManager.get_proxy()
            response = requests.get(url, headers=headers, timeout=10, proxies=proxies)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.debug(f"Get subscription info failed: {str(e)}")
            return None

    @staticmethod
    def get_membership_info(workos_session_token: str) -> Optional[Dict]:
        """获取会员信息，包括会员类型、试用剩余天数等"""
        url = f"https://www.{Config.NAME_LOWER}.com/api/auth/stripe"
        headers = Config.BASE_HEADERS.copy()
        headers.update({"Cookie": f"WorkosCursorSessionToken={workos_session_token}"})
        try:
            proxies = UsageManager.get_proxy()
            response = requests.get(url, headers=headers, timeout=10, proxies=proxies)
            response.raise_for_status()
            data = response.json()
            #print(f"{Fore.CYAN}{EMOJI['INFO']} 会员信息获取成功:{Style.RESET_ALL}")
            #print(f"{Fore.CYAN}{json.dumps(data, indent=2, ensure_ascii=False)}{Style.RESET_ALL}")
            return data
        except requests.RequestException as e:
            logger.debug(f"获取会员信息失败: {str(e)}")
            return None
        except Exception as e:
            logger.debug(f"获取会员信息时发生异常: {str(e)}")
            return None

def get_cursor_paths():
    """Determine Cursor related paths based on OS."""
    system = platform.system()
    paths = {}
    try:
        if system == "Windows":
            app_data = os.getenv("APPDATA")
            local_app_data = os.getenv("LOCALAPPDATA")
            if not app_data or not local_app_data:
                 raise OSError("Could not determine AppData paths.")
            paths['storage_path'] = os.path.join(local_app_data, "Programs", "Cursor", "resources", "app", "storage.json")
            paths['sqlite_path'] = os.path.join(app_data, "Cursor", "User", "globalStorage", "state.vscdb")
            paths['session_path'] = os.path.join(app_data, "Cursor", "Session Storage")
        elif system == "Darwin":  # macOS
            home = os.path.expanduser("~")
            paths['storage_path'] = "/Applications/Cursor.app/Contents/Resources/app/storage.json" # Adjust if installed elsewhere
            paths['sqlite_path'] = os.path.join(home, "Library/Application Support/Cursor/User/globalStorage/state.vscdb")
            paths['session_path'] = os.path.join(home, "Library/Application Support/Cursor/Session Storage")
        elif system == "Linux":
            home = os.path.expanduser("~")
            # Try common installation paths for AppImage or deb/rpm
            possible_storage_paths = [
                 os.path.join(home, ".local/share/cursor/resources/app/storage.json"), # Typical user install
                 "/opt/Cursor/resources/app/storage.json", # System-wide install (example)
                 # Add other potential paths if needed
            ]
            paths['storage_path'] = next((p for p in possible_storage_paths if os.path.exists(p)), None)
            if not paths['storage_path']:
                 logger.warning("Could not automatically determine storage.json path on Linux.")
            paths['sqlite_path'] = os.path.join(home, ".config/Cursor/User/globalStorage/state.vscdb")
            paths['session_path'] = os.path.join(home, ".config/Cursor/Session Storage")
        else:
            logger.error(f"Unsupported operating system: {system}")
            return None

        # Check if paths exist
        for key, path in paths.items():
            if path and not os.path.exists(os.path.dirname(path) if key == 'storage_path' or key == 'sqlite_path' else path):
                 logger.warning(f"Path does not seem to exist for {key}: {path}")

    except Exception as e:
        logger.error(f"Error determining Cursor paths: {str(e)}")
        return None

    return paths

def get_token_from_storage(storage_path):
    """get token from storage.json"""
    if not os.path.exists(storage_path):
        return None
        
    try:
        with open(storage_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # try to get accessToken
            if 'cursorAuth/accessToken' in data:
                return data['cursorAuth/accessToken']
            
            # try other possible keys
            for key in data:
                if 'token' in key.lower() and isinstance(data[key], str) and len(data[key]) > 20:
                    return data[key]
    except Exception as e:
        logger.error(f"get token from storage.json failed: {str(e)}")
    
    return None

def get_token_from_sqlite(sqlite_path):
    """get token from sqlite"""
    if not os.path.exists(sqlite_path):
        return None
        
    try:
        conn = sqlite3.connect(sqlite_path)
        cursor = conn.cursor()
        cursor.execute("SELECT value FROM ItemTable WHERE key LIKE '%token%'")
        rows = cursor.fetchall()
        conn.close()
        
        for row in rows:
            try:
                value = row[0]
                if isinstance(value, str) and len(value) > 20:
                    return value
                # try to parse JSON
                data = json.loads(value)
                if isinstance(data, dict) and 'token' in data:
                    return data['token']
            except:
                continue
    except Exception as e:
        logger.error(f"get token from sqlite failed: {str(e)}")
    
    return None

def get_token_from_session(session_path):
    """get token from session"""
    if not os.path.exists(session_path):
        return None
        
    try:
        # try to find all possible session files
        for file in os.listdir(session_path):
            if file.endswith('.log'):
                file_path = os.path.join(session_path, file)
                try:
                    with open(file_path, 'rb') as f:
                        content = f.read().decode('utf-8', errors='ignore')
                        # find token pattern
                        token_match = re.search(r'"token":"([^"]+)"', content)
                        if token_match:
                            return token_match.group(1)
                except:
                    continue
    except Exception as e:
        logger.error(f"get token from session failed: {str(e)}")
    
    return None

def get_token():
    """get Cursor token"""
    # get path based on OS
    paths = get_cursor_paths()
    if not paths:
        logger.error("无法确定 Cursor 相关路径。")
        return None

    # try to get token from different locations
    token = None
    if paths.get('storage_path'):
        token = get_token_from_storage(paths['storage_path'])
    if token:
        # logger.info("从 storage.json 获取 Token 成功。") # 注释掉此行
        return token

    if paths.get('sqlite_path'):
        token = get_token_from_sqlite(paths['sqlite_path'])
    if token:
        # logger.info("从 state.vscdb 获取 Token 成功。") # 注释掉此行
        return token

    if paths.get('session_path'):
        token = get_token_from_session(paths['session_path'])
    if token:
        # logger.info("从 Session Storage 获取 Token 成功。") # 注释掉此行
        return token

    logger.warning("未能从任何已知位置找到有效的 Token。")
    return None

def format_subscription_type(subscription_data: Dict) -> str:
    """format subscription type"""
    if not subscription_data:
        return "Free"
    
    # handle new API response format
    if "membershipType" in subscription_data:
        membership_type = subscription_data.get("membershipType", "").lower()
        subscription_status = subscription_data.get("subscriptionStatus", "").lower()
        
        if subscription_status == "active":
            if membership_type == "pro":
                return "Pro"
            elif membership_type == "free_trial":
                return "Free Trial"
            elif membership_type == "pro_trial":
                return "Pro Trial"
            elif membership_type == "team":
                return "Team"
            elif membership_type == "enterprise":
                return "Enterprise"
            elif membership_type:
                return membership_type.capitalize()
            else:
                return "Active Subscription"
        elif subscription_status:
            return f"{membership_type.capitalize()} ({subscription_status})"
    
    # compatible with old API response format
    subscription = subscription_data.get("subscription")
    if subscription:
        plan = subscription.get("plan", {}).get("nickname", "Unknown")
        status = subscription.get("status", "unknown")
        
        if status == "active":
            if "pro" in plan.lower():
                return "Pro"
            elif "pro_trial" in plan.lower():
                return "Pro Trial"
            elif "free_trial" in plan.lower():
                return "Free Trial"
            elif "team" in plan.lower():
                return "Team"
            elif "enterprise" in plan.lower():
                return "Enterprise"
            else:
                return plan
        else:
            return f"{plan} ({status})"
    
    return "Free"

def get_email_from_storage(storage_path):
    """get email from storage.json"""
    if not os.path.exists(storage_path):
        return None
        
    try:
        with open(storage_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # try to get email
            if 'cursorAuth/cachedEmail' in data:
                return data['cursorAuth/cachedEmail']
            
            # try other possible keys
            for key in data:
                if 'email' in key.lower() and isinstance(data[key], str) and '@' in data[key]:
                    return data[key]
    except Exception as e:
        logger.error(f"get email from storage.json failed: {str(e)}")
    
    return None

def get_email_from_sqlite(sqlite_path):
    """get email from sqlite"""
    if not os.path.exists(sqlite_path):
        return None
        
    try:
        conn = sqlite3.connect(sqlite_path)
        cursor = conn.cursor()
        # try to query records containing email
        cursor.execute("SELECT value FROM ItemTable WHERE key LIKE '%email%' OR key LIKE '%cursorAuth%'")
        rows = cursor.fetchall()
        conn.close()
        
        for row in rows:
            try:
                value = row[0]
                # if it's a string and contains @, it might be an email
                if isinstance(value, str) and '@' in value:
                    return value
                
                # try to parse JSON
                try:
                    data = json.loads(value)
                    if isinstance(data, dict):
                        # check if there's an email field
                        if 'email' in data:
                            return data['email']
                        # check if there's a cachedEmail field
                        if 'cachedEmail' in data:
                            return data['cachedEmail']
                except:
                    pass
            except:
                continue
    except Exception as e:
        logger.error(f"get email from sqlite failed: {str(e)}")
    
    return None

def display_account_info(translator=None, accounts_count=0):
    """Display account info with layout and colors like cursor-free-vip-main."""
    print(f"\n{Fore.CYAN}{'─' * 70}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['USER']} 账户信息 ({accounts_count}){Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'─' * 70}{Style.RESET_ALL}")

    # Get token
    token = get_token()
    if not token:
        print(f"{Fore.RED}{EMOJI['ERROR']} 未能获取到有效的用户 Token。请确保 Cursor 已登录。{Style.RESET_ALL}")
        return

    # Get paths using the new function
    paths = get_cursor_paths()
    email = None
    if paths:
        if paths.get('storage_path'):
            email = get_email_from_storage(paths['storage_path'])
        if not email and paths.get('sqlite_path'):
            email = get_email_from_sqlite(paths['sqlite_path'])

    # Get subscription info - fetch first to potentially get email if missing
    subscription_info = UsageManager.get_stripe_profile(token)

    # Try getting email from subscription info if not found locally
    if not email and subscription_info:
        if 'customer' in subscription_info and 'email' in subscription_info['customer']:
             email = subscription_info['customer']['email']
        # Add check for the newer email field directly in the response
        elif 'email' in subscription_info:
             email = subscription_info['email']

    # Get usage info - handle potential errors
    usage_info = UsageManager.get_usage(token)


    # Prepare left and right info lists
    left_info = []
    right_info = []

    # --- Populate Left Info ---
    if email:
        left_info.append(f"{Fore.GREEN}{EMOJI['USER']} 邮箱: {Fore.WHITE}{email}{Style.RESET_ALL}")
    else:
        left_info.append(f"{Fore.YELLOW}{EMOJI['WARNING']} 未能自动获取邮箱地址{Style.RESET_ALL}")

    # Show subscription type
    if subscription_info:
        subscription_type = format_subscription_type(subscription_info)
        left_info.append(f"{Fore.GREEN}{EMOJI['SUBSCRIPTION']} {translator.get('account_info.subscription') if translator else '订阅'}: {Fore.WHITE}{subscription_type}{Style.RESET_ALL}")

        # Show remaining trial days using daysRemainingOnTrial if available
        days_remaining = subscription_info.get("daysRemainingOnTrial")
        # Check if days_remaining is a valid positive number
        if days_remaining is not None and isinstance(days_remaining, (int, float)) and days_remaining > 0:
            trial_days_str = f"{Fore.GREEN}{EMOJI['TIME']} 剩余试用: {Fore.WHITE}{int(days_remaining)} 天{Style.RESET_ALL}"
            left_info.append(trial_days_str)
        # If days_remaining is None, or not positive, display "无"
        else:
             trial_days_str = f"{Fore.GREEN}{EMOJI['TIME']} 剩余试用: {Fore.WHITE}无{Style.RESET_ALL}"
             left_info.append(trial_days_str)

    else:
        left_info.append(f"{Fore.YELLOW}{EMOJI['WARNING']} {translator.get('account_info.subscription_not_found') if translator else 'Subscription information not found'}{Style.RESET_ALL}")

    # --- Populate Right Info ---
    if usage_info:
        right_info.append(f"{Fore.GREEN}{EMOJI['USAGE']} 使用量:{Style.RESET_ALL}")

        # Premium usage (Fast Response)
        premium_usage = usage_info.get('premium_usage', 0)
        max_premium_usage = usage_info.get('max_premium_usage', "No Limit")

        if premium_usage is None: premium_usage = 0

        premium_display = ""
        premium_color = Fore.GREEN

        if isinstance(max_premium_usage, str) and max_premium_usage == "No Limit":
            premium_display = f"{premium_usage}/{max_premium_usage}"
            premium_color = Fore.GREEN
        elif isinstance(max_premium_usage, (int, float)) and max_premium_usage > 0:
            premium_percentage = (premium_usage / max_premium_usage) * 100
            if premium_percentage > 90: premium_color = Fore.RED
            elif premium_percentage > 70: premium_color = Fore.YELLOW
            premium_display = f"{premium_usage}/{int(max_premium_usage)} ({premium_percentage:.1f}%)"
        else:
            max_premium_usage_disp = max_premium_usage if max_premium_usage is not None else '?'
            premium_display = f"{premium_usage}/{max_premium_usage_disp}"
            premium_color = Fore.YELLOW

        right_info.append(f"{Fore.YELLOW}{EMOJI['PREMIUM']} 高级使用量: {premium_color}{premium_display}{Style.RESET_ALL}")

        # Basic usage (Slow Response)
        basic_usage = usage_info.get('basic_usage', 0)
        max_basic_usage = usage_info.get('max_basic_usage', "No Limit")

        if basic_usage is None: basic_usage = 0

        basic_color = Fore.GREEN
        basic_display = f"{basic_usage}/{max_basic_usage}"

        right_info.append(f"{Fore.BLUE}{EMOJI['BASIC']} 基础使用量: {basic_color}{basic_display}{Style.RESET_ALL}")

    else:
        # Optionally show a message if usage info failed
        right_info.append(f"{Fore.YELLOW}{EMOJI['WARNING']} 未能获取使用量信息{Style.RESET_ALL}")


    # --- Calculate Layout and Print ---
    max_left_width = 0
    for item in left_info:
        width = get_display_width(item)
        max_left_width = max(max_left_width, width)

    fixed_spacing = 4
    right_start = max_left_width + fixed_spacing

    # Print lines
    max_rows = max(len(left_info), len(right_info))
    for i in range(max_rows):
        left_item = left_info[i] if i < len(left_info) else ""
        right_item = right_info[i] if i < len(right_info) else ""

        left_width = get_display_width(left_item)
        spaces = right_start - left_width

        # Ensure minimum 1 space if right item exists, handle potential negative spacing if left is too wide
        actual_spaces = max(1, spaces) if right_item else spaces

        print(f"{left_item}{' ' * actual_spaces}{right_item}")

    print(f"{Fore.CYAN}{'─' * 70}{Style.RESET_ALL}")


def get_display_width(s):
    """Calculate the display width of a string, considering CJK characters and emojis"""
    # Remove ANSI escape codes using the regex from cursor-free-vip-main
    ansi_escape = re.compile(r'\x1B(?:[@-_]|\[[0-?]*[ -\/]*[@-~])')  # 修正后的正则表达式
    clean_s = ansi_escape.sub('', s)

    width = 0
    for char in clean_s:
        # Basic check for wide characters (common CJK range and Hangul Jamo)
        if (0x4E00 <= ord(char) <= 0x9FFF or
            0x3040 <= ord(char) <= 0x30FF or
            0xAC00 <= ord(char) <= 0xD7A3 or
            0xFF01 <= ord(char) <= 0xFF60 or
            0x1100 <= ord(char) <= 0x11FF):
            width += 2
        # Check for common emojis (this is not exhaustive)
        elif (0x1F300 <= ord(char) <= 0x1F5FF or
              0x1F600 <= ord(char) <= 0x1F64F or
              0x1F680 <= ord(char) <= 0x1F6FF or
              0x2600 <= ord(char) <= 0x26FF or
              0x2700 <= ord(char) <= 0x27BF or
              0xFE00 <= ord(char) <= 0xFE0F or
              0x1FA70 <= ord(char) <= 0x1FAFF):
            width += 2
        # Most other non-ASCII characters might be wide.
        elif ord(char) > 127:
             width += 2
        else:
            width += 1
    return width

# Ensure main function or entry point calls display_account_info correctly
# If this script is run directly, call the function
if __name__ == "__main__":
    display_account_info() 