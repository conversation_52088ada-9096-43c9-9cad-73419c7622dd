#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF列表获取工具
基于新浪财经ETF排行榜接口
原始接口: https://quotes.sina.com.cn/cn/api/openapi.php/CN_ETFService.getETFRankList
参考实现: /Users/<USER>/Documents/webWorkspace/TiantianFundApi-main/src/module/etfRanking.js
"""

import requests
import json
import time
import random
import re
import threading
import os
from datetime import datetime
from urllib.parse import urlencode
from concurrent.futures import ThreadPoolExecutor, as_completed


class ETFListGetter:
    def __init__(self, proxy=None, thread_safe=True):
        # 新浪财经ETF排行榜API地址
        self.base_url = "https://quotes.sina.com.cn/cn/api/openapi.php/CN_ETFService.getETFRankList"

        # 代理配置
        self.proxy = proxy

        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://finance.sina.com.cn/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }

        # 分时数据API地址
        self.minline_url = "https://cn.finance.sina.com.cn/minline/getMinlineData"

        # 🚀 优化多线程环境下的智能频率控制
        self.thread_safe = thread_safe
        if thread_safe:
            import threading
            self.lock = threading.Lock()

        self.last_request_time = 0
        self.success_count = 0   # 连续成功次数
        self.error_count = 0     # 连续错误次数
        self.base_delay = 0.05   # 🚀 进一步减少基础延迟
        self.max_delay = 0.3     # 🚀 减少最大延迟

        # 🚀 如果没有提供代理，尝试自动获取
        if proxy is None:
            self.proxy = self.get_single_proxy()

    def get_single_proxy(self):
        """获取单个高质量代理"""
        proxy_url = os.getenv("PROXY_URL")
        if not proxy_url:
            print("⚠️ 未配置代理URL，将使用直连")
            return None

        try:
            print("🔄 正在获取代理...")
            response = requests.get(proxy_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200 and data.get("data") and data["data"].get("proxy_list"):
                    proxy_info = data["data"]["proxy_list"][0]
                    proxy_address = proxy_info.split(',')[0]  # 提取 IP:端口 部分

                    proxy = {
                        'http': f'http://{proxy_address}',
                        'https': f'http://{proxy_address}'
                    }

                    print(f"✅ 成功获取代理: {proxy_address}")
                    return proxy
                else:
                    print("❌ 代理响应格式异常")
            else:
                print(f"❌ 获取代理失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ 获取代理异常: {e}")

        print("⚠️ 代理获取失败，将使用直连")
        return None



    def _calculate_smart_delay(self):
        """
        智能计算请求延迟时间
        - 连续成功时减少延迟
        - 出现错误时增加延迟
        - 添加随机性避免规律性请求
        - 🚀 针对多线程环境优化
        """
        # 基于成功/失败情况调整延迟
        if self.error_count > 0:
            # 有错误时增加延迟
            delay_factor = 1 + (self.error_count * 0.2)  # 🚀 减少错误延迟增量
            base_delay = min(self.base_delay * delay_factor, self.max_delay)
        elif self.success_count > 5:
            # 连续成功5次以上时减少延迟
            delay_factor = max(0.5, 1 - (self.success_count - 5) * 0.05)  # 🚀 更快减少延迟
            base_delay = self.base_delay * delay_factor
        else:
            base_delay = self.base_delay

        # 添加随机性，避免规律性请求
        random_factor = random.uniform(0.7, 1.3)  # 🚀 增加随机性范围
        final_delay = base_delay * random_factor

        return max(0.05, min(final_delay, self.max_delay))  # 🚀 最小延迟降到0.05秒

    def _handle_delay_logic(self, current_time):
        """
        处理延迟逻辑（线程安全）
        """
        if self.last_request_time > 0:
            elapsed = current_time - self.last_request_time
            smart_delay = self._calculate_smart_delay()
            if elapsed < smart_delay:
                return smart_delay - elapsed
        return 0

    def _wait_if_needed(self):
        """
        控制请求频率，避免过于频繁的请求
        """
        current_time = time.time()

        if self.thread_safe:
            with self.lock:
                delay_needed = self._handle_delay_logic(current_time)
        else:
            delay_needed = self._handle_delay_logic(current_time)

        if delay_needed > 0:
            time.sleep(delay_needed)

        self.last_request_time = time.time()

    def get_etf_ranking(self):
        """
        获取ETF排行榜数据
        使用固定参数: pageIndex=1&pageSize=999&filter=t0&asc=0&sort=change&type=all
        """

        # 固定参数
        fixed_params = {
            'pageIndex': 1,
            'pageSize': 999,
            'filter': 't0',
            'asc': 0,  # 降序
            'sort': 'change',  # 按涨跌幅排序
            'type': 'all'  # 全部类型
        }

        try:
            # 控制请求频率
            self._wait_if_needed()

            print(f"正在获取ETF排行榜数据...")
            print(f"请求URL: {self.base_url}")
            print(f"请求参数: {fixed_params}")

            # 发送GET请求
            response = requests.get(
                self.base_url,
                params=fixed_params,
                headers=self.headers,
                proxies=self.proxy,
                timeout=10
            )

            print(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ 成功获取ETF数据")

                    # 请求成功，更新计数器
                    if self.thread_safe:
                        with self.lock:
                            self.success_count += 1
                            self.error_count = 0  # 重置错误计数
                    else:
                        self.success_count += 1
                        self.error_count = 0

                    # 包装返回数据，添加请求参数信息
                    return {
                        'code': 200,
                        'message': 'success',
                        'data': data,
                        'requestParams': fixed_params,
                        'timestamp': int(time.time() * 1000)
                    }
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"响应内容: {response.text[:500]}...")
                    # JSON解析失败，更新计数器
                    if self.thread_safe:
                        with self.lock:
                            self.error_count += 1
                            self.success_count = 0
                    else:
                        self.error_count += 1
                        self.success_count = 0
                    return None
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:500]}...")
                # 请求失败，更新计数器
                if self.thread_safe:
                    with self.lock:
                        self.error_count += 1
                        self.success_count = 0
                else:
                    self.error_count += 1
                    self.success_count = 0
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            return None
        except Exception as e:
            print(f"❌ 未知异常: {e}")
            # 异常，更新计数器
            if self.thread_safe:
                with self.lock:
                    self.error_count += 1
                    self.success_count = 0
            else:
                self.error_count += 1
                self.success_count = 0
            return None

    def convert_symbol_format(self, symbol):
        """
        转换ETF代码格式为分时数据API所需的格式

        参数：
        - symbol: 原始ETF代码，如 sz159919

        返回：
        - 转换后的代码，如 sz159919
        """
        if not symbol:
            return None

        # 如果已经是正确格式，直接返回
        if re.match(r'^(sh|sz)\d{6}$', symbol.lower()):
            return symbol.lower()

        # 尝试转换其他格式
        symbol = symbol.lower()
        if symbol.startswith('1'):  # 深圳ETF
            return f'sz{symbol}'
        elif symbol.startswith('5'):  # 上海ETF
            return f'sh{symbol}'
        else:
            # 默认返回原始格式
            return symbol

    def get_etf_minline_data(self, symbol):
        """
        获取单个ETF的分时数据

        参数：
        - symbol: ETF代码

        返回：
        - dict: 分时数据
        """
        # 转换代码格式
        formatted_symbol = self.convert_symbol_format(symbol)
        if not formatted_symbol:
            return None

        # 请求参数
        params = {
            'symbol': formatted_symbol,
            'version': '8.15.0',
            'dpc': '1'
        }

        try:
            # 🚀 线程安全的智能延迟控制
            current_time = time.time()

            if self.thread_safe:
                with self.lock:
                    delay_needed = self._handle_delay_logic(current_time)
            else:
                delay_needed = self._handle_delay_logic(current_time)

            if delay_needed > 0:
                time.sleep(delay_needed)

            self.last_request_time = time.time()

            # 发送GET请求
            response = requests.get(
                self.minline_url,
                params=params,
                headers=self.headers,
                proxies=self.proxy,
                timeout=10
            )

            if response.status_code == 200:
                try:
                    data = response.json()

                    # 检查返回状态
                    if (data.get('result') and
                        data['result'].get('status') and
                        data['result']['status'].get('code') != 0):
                        # 请求失败，更新计数器
                        if self.thread_safe:
                            with self.lock:
                                self.error_count += 1
                                self.success_count = 0
                        else:
                            self.error_count += 1
                            self.success_count = 0
                        return None

                    # 请求成功，更新计数器
                    if self.thread_safe:
                        with self.lock:
                            self.success_count += 1
                            self.error_count = 0
                    else:
                        self.success_count += 1
                        self.error_count = 0

                    return {
                        'symbol': symbol,
                        'formatted_symbol': formatted_symbol,
                        'data': data,
                        'timestamp': int(time.time() * 1000)
                    }

                except json.JSONDecodeError:
                    # JSON解析失败，更新计数器
                    if self.thread_safe:
                        with self.lock:
                            self.error_count += 1
                            self.success_count = 0
                    else:
                        self.error_count += 1
                        self.success_count = 0
                    return None
            else:
                # 请求失败，更新计数器
                if self.thread_safe:
                    with self.lock:
                        self.error_count += 1
                        self.success_count = 0
                else:
                    self.error_count += 1
                    self.success_count = 0
                return None

        except requests.exceptions.RequestException:
            # 网络异常，更新计数器
            if self.thread_safe:
                with self.lock:
                    self.error_count += 1
                    self.success_count = 0
            else:
                self.error_count += 1
                self.success_count = 0
            return None
        except Exception:
            # 其他异常，更新计数器
            if self.thread_safe:
                with self.lock:
                    self.error_count += 1
                    self.success_count = 0
            else:
                self.error_count += 1
                self.success_count = 0
            return None

    def get_etf_minline_data_batch(self, etf_list, max_workers=20, show_progress=True):
        """
        批量获取ETF分时数据（多线程）- 单代理高并发优化版本

        参数：
        - etf_list: ETF列表
        - max_workers: 最大线程数，默认20
        - show_progress: 是否显示详细进度，默认True

        返回：
        - dict: 包含成功和失败的结果
        """
        if not etf_list:
            return {'success': [], 'failed': []}

        print(f"\n� 开始高性能并发获取全部 {len(etf_list)} 个ETF的分时数据...")
        print(f"📊 使用 {max_workers} 个线程并发处理（最佳性能配置）")
        print(f"🎯 目标速度: 30-35 ETF/秒")
        print(f"{'='*60}")

        start_time = time.time()
        success_results = []
        failed_results = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_etf = {}
            for etf in etf_list:
                symbol = etf.get('symbol', etf.get('code', ''))
                if symbol:
                    future = executor.submit(self.get_etf_minline_data, symbol)
                    future_to_etf[future] = {'symbol': symbol, 'name': etf.get('name', 'N/A')}

            # 收集结果
            completed = 0
            for future in as_completed(future_to_etf):
                etf_info = future_to_etf[future]
                completed += 1

                try:
                    result = future.result()
                    if result:
                        # 解析分时数据
                        parsed_data = self.parse_minline_data(result)
                        if parsed_data:
                            success_results.append({
                                'etf_info': etf_info,
                                'minline_data': parsed_data
                            })
                            # 根据show_progress参数控制输出
                            if show_progress and (completed <= 10 or completed % 50 == 0):
                                elapsed = time.time() - start_time
                                speed = completed / elapsed if elapsed > 0 else 0
                                print(f"✅ [{completed}/{len(etf_list)}] {etf_info['symbol']} - 当前速度: {speed:.1f} ETF/秒")
                        else:
                            failed_results.append(etf_info)
                            if show_progress and completed <= 10:
                                print(f"❌ [{completed}/{len(etf_list)}] {etf_info['symbol']} - 数据解析失败")
                    else:
                        failed_results.append(etf_info)
                        if show_progress and completed <= 10:
                            print(f"❌ [{completed}/{len(etf_list)}] {etf_info['symbol']} - 获取失败")

                except Exception as e:
                    failed_results.append(etf_info)
                    if show_progress and completed <= 10:
                        print(f"❌ [{completed}/{len(etf_list)}] {etf_info['symbol']} - 异常: {str(e)}")

        total_time = time.time() - start_time
        success_count = len(success_results)
        failed_count = len(failed_results)
        total_count = success_count + failed_count
        final_speed = total_count / total_time if total_time > 0 else 0
        success_speed = success_count / total_time if total_time > 0 else 0

        if show_progress:
            print(f"\n{'='*60}")
            print(f"🎉 分时数据获取完成!")
            print(f"⏱️  总耗时: {total_time:.2f} 秒")
            print(f"📊 总ETF数: {total_count}")
            print(f"✅ 成功: {success_count} ({success_count/total_count*100:.1f}%)")
            print(f"❌ 失败: {failed_count} ({failed_count/total_count*100:.1f}%)")
            print(f"🚀 最终速度: {final_speed:.2f} ETF/秒")
            print(f"💥 成功速度: {success_speed:.2f} ETF/秒")

        return {
            'success': success_results,
            'failed': failed_results,
            'stats': {
                'total_time': total_time,
                'success_count': success_count,
                'failed_count': failed_count,
                'final_speed': final_speed,
                'success_speed': success_speed,
                'success_rate': success_count/total_count*100 if total_count > 0 else 0
            }
        }

    def parse_minline_data(self, data):
        """
        解析分时数据

        参数：
        - data: API返回的原始数据

        返回：
        - dict: 解析后的分时数据
        """
        if not data or 'data' not in data:
            return None

        raw_data = data['data']

        # 提取基本信息
        result = {
            'symbol': data.get('symbol', ''),
            'formatted_symbol': data.get('formatted_symbol', ''),
            'timestamp': data.get('timestamp', 0),
            'minline_data': []
        }

        # 尝试解析分时数据
        try:
            if 'result' in raw_data:
                result_data = raw_data['result']

                if isinstance(result_data, dict) and 'data' in result_data:
                    minline_data_list = result_data['data']

                    # data是分时数据点的列表
                    if isinstance(minline_data_list, list) and len(minline_data_list) > 0:
                        # 计算基本信息
                        first_point = minline_data_list[0]
                        last_point = minline_data_list[-1]

                        # 计算最高价和最低价
                        prices = [float(point.get('p', 0)) for point in minline_data_list if point.get('p')]
                        high_price = max(prices) if prices else 0
                        low_price = min(prices) if prices else 0

                        # 计算总成交量
                        total_volume = sum(int(point.get('v', 0)) for point in minline_data_list if point.get('v'))

                        current_price = float(last_point.get('p', 0))
                        open_price = float(first_point.get('p', 0))

                        # 基本信息
                        result['basic_info'] = {
                            'current_price': current_price,
                            'open_price': open_price,
                            'high_price': high_price,
                            'low_price': low_price,
                            'total_volume': total_volume,
                            'data_points': len(minline_data_list),
                            'first_time': first_point.get('m', ''),
                            'last_time': last_point.get('m', ''),
                            'avg_price': float(last_point.get('avg_p', 0))
                        }

                        # 只保留最近的几个数据点以节省内存
                        recent_points = minline_data_list[-5:] if len(minline_data_list) > 5 else minline_data_list
                        for point in recent_points:
                            if isinstance(point, dict):
                                result['minline_data'].append({
                                    'time': point.get('m', ''),
                                    'price': float(point.get('p', 0)),
                                    'volume': int(point.get('v', 0)),
                                    'avg_price': float(point.get('avg_p', 0))
                                })

        except Exception:
            return None

        return result

    def print_minline_results(self, results):
        """
        打印分时数据结果

        参数：
        - results: 批量获取的结果
        """
        if not results:
            print("没有分时数据结果")
            return

        success_results = results.get('success', [])
        failed_results = results.get('failed', [])

        print(f"\n{'='*80}")
        print(f"ETF分时数据汇总")
        print(f"{'='*80}")
        print(f"成功获取: {len(success_results)} 个")
        print(f"获取失败: {len(failed_results)} 个")

        if success_results:
            print(f"\n{'='*80}")
            print(f"成功获取的ETF分时数据")
            print(f"{'='*80}")

            for i, item in enumerate(success_results, 1):
                etf_info = item['etf_info']
                minline_data = item['minline_data']
                basic_info = minline_data.get('basic_info', {})

                print(f"\n{i}. {etf_info['symbol']} - {etf_info['name']}")
                print(f"   当前价格: {basic_info.get('current_price', 'N/A')}")
                print(f"   开盘价格: {basic_info.get('open_price', 'N/A')}")
                print(f"   最高价格: {basic_info.get('high_price', 'N/A')}")
                print(f"   最低价格: {basic_info.get('low_price', 'N/A')}")
                print(f"   平均价格: {basic_info.get('avg_price', 'N/A')}")
                print(f"   总成交量: {basic_info.get('total_volume', 'N/A'):,}")
                print(f"   数据点数: {basic_info.get('data_points', 'N/A')}")
                print(f"   交易时间: {basic_info.get('first_time', 'N/A')} - {basic_info.get('last_time', 'N/A')}")

                # 显示最近几个分时点
                recent_points = minline_data.get('minline_data', [])
                if recent_points:
                    print(f"   最近分时点:")
                    for point in recent_points[-3:]:  # 显示最近3个点
                        print(f"     {point.get('time', 'N/A')}: 价格={point.get('price', 'N/A')}, 成交量={point.get('volume', 'N/A'):,}")

        if failed_results:
            print(f"\n{'='*80}")
            print(f"获取失败的ETF")
            print(f"{'='*80}")

            for i, etf_info in enumerate(failed_results, 1):
                print(f"{i}. {etf_info['symbol']} - {etf_info['name']}")

    def print_etf_list(self, data, show_details=True):
        """
        格式化打印ETF列表

        参数：
        - data: ETF数据（可以是API响应或ETF列表）
        - show_details: 是否显示详细信息
        """
        if not data:
            print("没有获取到ETF数据")
            return

        # 处理新浪财经API响应格式
        etfs = []
        if isinstance(data, list):
            etfs = data
        elif isinstance(data, dict):
            # 新浪财经API的响应格式: data.result.data.data
            if 'data' in data:
                if isinstance(data['data'], dict) and 'result' in data['data']:
                    # 包装后的响应格式: data.result.data.data
                    result = data['data']['result']
                    if 'data' in result and isinstance(result['data'], dict) and 'data' in result['data']:
                        etfs = result['data']['data']
                    elif 'data' in result and isinstance(result['data'], list):
                        etfs = result['data']
                    else:
                        etfs = result
                elif isinstance(data['data'], dict) and 'data' in data['data']:
                    # 直接的新浪API响应格式
                    if isinstance(data['data']['data'], list):
                        etfs = data['data']['data']
                    else:
                        etfs = [data['data']]
                elif isinstance(data['data'], list):
                    # 直接的列表格式
                    etfs = data['data']
                else:
                    # 其他格式，尝试直接使用
                    etfs = [data['data']]
            elif 'result' in data:
                # 直接的result格式
                if 'data' in data['result'] and isinstance(data['result']['data'], dict) and 'data' in data['result']['data']:
                    etfs = data['result']['data']['data']
                elif 'data' in data['result']:
                    etfs = data['result']['data']
                else:
                    etfs = [data['result']]
            else:
                print("响应数据格式异常")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                return

        if not etfs:
            print("没有找到ETF数据")
            return

        print(f"\n{'='*80}")
        print(f"ETF列表 (共{len(etfs)}条)")
        print(f"{'='*80}")

        for i, etf in enumerate(etfs, 1):
            # 新浪财经API的字段名
            symbol = etf.get('symbol', etf.get('code', 'N/A'))
            name = etf.get('name', 'N/A')

            print(f"\n{i}. ETF代码: {symbol}")
            print(f"   ETF名称: {name}")

            if show_details:
                print(f"   当前价格: {etf.get('price', 'N/A')}")
                print(f"   涨跌幅: {etf.get('change', 'N/A')}%")
                print(f"   成交额: {etf.get('amount', 'N/A')}")
                print(f"   变化速度: {etf.get('change_speed', 'N/A')}")
                print(f"   溢价率: {etf.get('premium_rate', 'N/A')}")
                print(f"   净申购: {etf.get('net_subscription', 'N/A')}")
                print(f"   净买入: {etf.get('net_purchase', 'N/A')}")
                print(f"   总规模: {etf.get('total_scale', 'N/A')}")
                print(f"   单位净值: {etf.get('net_unit_value', 'N/A')}")
                print(f"   增长率: {etf.get('increase_rate', 'N/A')}%")
                print(f"   最大回撤: {etf.get('maximum_pullback', 'N/A')}")
                print(f"   夏普比率: {etf.get('sharpe_ratio', 'N/A')}")
                print(f"   跟踪误差: {etf.get('tracking_error', 'N/A')}")
                print(f"   基金经理: {etf.get('fund_manager', 'N/A')}")
                print(f"   托管人: {etf.get('custodian', 'N/A')}")
                print(f"   评级: {etf.get('grade', 'N/A')}")

                # 检查标志位
                flags = etf.get('flags', {})
                if flags:
                    print(f"   融资融券: {'是' if flags.get('rzrq', 0) == 1 else '否'}")
                    print(f"   沪深港通: {'是' if flags.get('hs_gt', 0) == 1 else '否'}")
                    print(f"   T+0交易: {'是' if flags.get('t0', 0) == 1 else '否'}")
                    print(f"   注销: {'是' if flags.get('zx', 0) == 1 else '否'}")

    def filter_t0_etfs(self, etfs):
        """
        过滤出T+0交易的ETF
        
        参数：
        - etfs: ETF列表
        
        返回：
        - T+0 ETF列表
        """
        if not etfs:
            return []
        
        t0_etfs = []
        for etf in etfs:
            # 根据新浪财经API的实际数据结构判断T+0
            flags = etf.get('flags', {})
            is_t0 = False

            if flags and flags.get('t0', 0) == 1:
                is_t0 = True
            elif (etf.get('isT0') == True or
                  etf.get('isT0') == 'true' or
                  etf.get('isT0') == '1' or
                  etf.get('t0') == True):
                is_t0 = True
            elif ('T+0' in str(etf.get('name', '')) or
                  'T0' in str(etf.get('name', ''))):
                is_t0 = True

            if is_t0:
                t0_etfs.append(etf)

        return t0_etfs

    def test_optimal_thread_count(self, etf_list, thread_counts=None, test_sample_size=30):
        """
        测试最佳线程数配置

        参数：
        - etf_list: ETF列表
        - thread_counts: 要测试的线程数列表，默认[5, 10, 15, 20, 25, 30]
        - test_sample_size: 测试样本大小，默认30个ETF

        返回：
        - 测试结果和推荐配置
        """
        if thread_counts is None:
            thread_counts = [5, 10, 15, 20, 25, 30]

        # 限制测试样本大小
        test_etfs = etf_list[:test_sample_size] if len(etf_list) > test_sample_size else etf_list

        print(f"\n🧪 开始线程数性能测试")
        print(f"{'='*60}")
        print(f"📊 测试样本: {len(test_etfs)} 个ETF")
        print(f"🧵 测试线程数: {thread_counts}")
        print(f"{'='*60}")

        results = []

        for i, thread_count in enumerate(thread_counts, 1):
            print(f"\n🔄 [{i}/{len(thread_counts)}] 测试 {thread_count} 个线程...")

            start_time = time.time()

            try:
                # 执行批量获取（不显示详细进度）
                batch_results = self.get_etf_minline_data_batch(
                    etf_list=test_etfs,
                    max_workers=thread_count,
                    show_progress=False
                )

                # 计算性能指标
                total_time = time.time() - start_time
                success_count = len(batch_results.get('success', []))
                failed_count = len(batch_results.get('failed', []))
                total_count = success_count + failed_count

                success_rate = (success_count / total_count * 100) if total_count > 0 else 0
                success_speed = success_count / total_time if total_time > 0 else 0

                result = {
                    'thread_count': thread_count,
                    'total_time': round(total_time, 2),
                    'success_count': success_count,
                    'failed_count': failed_count,
                    'success_rate': round(success_rate, 1),
                    'success_speed': round(success_speed, 2)
                }

                results.append(result)

                print(f"   ⏱️  耗时: {total_time:.2f}秒")
                print(f"   ✅ 成功率: {success_rate:.1f}%")
                print(f"   🚀 成功速度: {success_speed:.2f} ETF/秒")

                # 短暂休息
                if i < len(thread_counts):
                    time.sleep(2)

            except Exception as e:
                print(f"   ❌ 测试失败: {e}")

        # 分析结果
        if results:
            print(f"\n📊 性能测试结果汇总:")
            print(f"{'='*60}")
            print(f"{'线程数':<8} {'耗时(秒)':<10} {'成功率(%)':<10} {'成功速度':<12}")
            print(f"{'-'*50}")

            for result in results:
                print(f"{result['thread_count']:<8} "
                      f"{result['total_time']:<10} "
                      f"{result['success_rate']:<10} "
                      f"{result['success_speed']:<12}")

            # 推荐最佳配置
            best_speed = max(results, key=lambda x: x['success_speed'])
            best_rate = max(results, key=lambda x: x['success_rate'])

            print(f"\n🏆 推荐配置:")
            print(f"💥 最快速度: {best_speed['thread_count']} 线程 ({best_speed['success_speed']} ETF/秒)")
            print(f"✅ 最高成功率: {best_rate['thread_count']} 线程 ({best_rate['success_rate']}%)")

            return {
                'results': results,
                'best_speed': best_speed,
                'best_rate': best_rate
            }

        return None

    def save_to_file(self, data, filename=None):
        """
        保存数据到文件

        参数：
        - data: 要保存的数据
        - filename: 文件名，默认使用时间戳
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"etf_data_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"✅ 数据已保存到文件: {filename}")
            return filename
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return None


def main():
    """
    主函数 - 获取ETF数据并多线程获取分时数据
    """
    import sys

    # 检查命令行参数
    test_mode = len(sys.argv) > 1 and sys.argv[1] == '--test'

    if test_mode:
        print("🧪 ETF分时数据获取性能测试模式")
    else:
        print("🚀 ETF分时数据获取模式")

    print("="*60)

    try:
        # 创建ETF获取器（启用线程安全）
        getter = ETFListGetter(thread_safe=True)

        # 使用固定参数获取全部ETF数据
        print(f"\n{'='*60}")
        print("获取全部ETF数据 (使用固定参数)")
        print("参数: pageIndex=1&pageSize=999&filter=t0&asc=0&sort=change&type=all")
        print(f"{'='*60}")

        etf_data = getter.get_etf_ranking()

        if etf_data:
            print("\n✅ 成功获取ETF数据!")

            # 解析ETF列表
            etfs = []
            if isinstance(etf_data, dict) and 'data' in etf_data:
                if isinstance(etf_data['data'], dict) and 'result' in etf_data['data']:
                    result = etf_data['data']['result']
                    if 'data' in result and isinstance(result['data'], dict) and 'data' in result['data']:
                        etfs = result['data']['data']
                    elif 'data' in result and isinstance(result['data'], list):
                        etfs = result['data']

            if etfs:
                print(f"📊 解析到 {len(etfs)} 个ETF")

                if test_mode:
                    # 🧪 性能测试模式
                    print(f"\n{'='*60}")
                    print("线程数性能测试模式")
                    print(f"{'='*60}")

                    test_results = getter.test_optimal_thread_count(
                        etf_list=etfs,
                        thread_counts=[5, 10, 15, 20, 25, 30, 35, 40],
                        test_sample_size=50
                    )

                    if test_results:
                        # 保存测试结果
                        test_filename = getter.save_to_file(test_results, "thread_performance_test.json")
                        if test_filename:
                            print(f"📁 性能测试结果已保存到: {test_filename}")
                else:
                    # 🚀 正常获取模式
                    print(f"\n{'='*60}")
                    print("单代理多线程并发获取全部ETF分时数据")
                    print(f"{'='*60}")

                    # 获取全部ETF的分时数据（使用性能测试得出的最佳配置）
                    minline_results = getter.get_etf_minline_data_batch(
                        etf_list=etfs,
                        max_workers=40,  # 40个线程并发（性能测试最佳配置）
                        show_progress=True  # 显示详细进度
                    )

                    # 打印分时数据结果
                    getter.print_minline_results(minline_results)

                    # 保存分时数据到文件
                    if minline_results['success']:
                        minline_filename = getter.save_to_file(minline_results, "etf_minline_data.json")
                        if minline_filename:
                            print(f"📁 分时数据已保存到: {minline_filename}")

            # 保存原始ETF数据到文件
            filename = getter.save_to_file(etf_data, "all_etf_data.json")
            if filename:
                print(f"📁 ETF列表数据已保存到: {filename}")
        else:
            print("❌ 获取ETF数据失败")

        print(f"\n🎉 程序执行完成！")

    except Exception as e:
        print(f"❌ 程序执行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
