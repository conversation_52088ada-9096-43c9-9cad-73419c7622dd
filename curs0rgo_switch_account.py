'''
此脚本用于调用本地服务接口以切换账号。
'''
import requests
import json
from colorama import Fore, Style

def switch_account(email: str, token: str) -> bool:
    """
    调用本地接口切换账号。

    Args:
        email: 用户的邮箱。
        token: 用户的token。

    Returns:
        True 如果切换成功 (HTTP 状态码 204)，否则 False。
    """
    url = "http://localhost:14285/refresh"
    headers = {
        "Content-Type": "application/json"
    }
    data = {
        "email": email,
        "token": token
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        if response.status_code == 204:
            print("账号切换成功！")
            return True
        else:
            try:
                error_message = response.json()
                print(f"账号切换失败。状态码: {response.status_code}, 原因: {error_message.get('error', '未知错误')}")
            except json.JSONDecodeError:
                print(f"账号切换失败。状态码: {response.status_code}, 响应内容不是有效的JSON: {response.text}")
            print("  请检查本地 curs0rgo 服务是否正确运行。")
            print(f"{Fore.BLUE}  调用方式参考: https://linux.do/t/topic/533625{Style.RESET_ALL}")
            print(f"{Fore.GREEN}  curs0rgo 下载: https://github.com/J3n5en/curs0r-release{Style.RESET_ALL}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"请求接口时发生错误: {e}")
        print("  请确保本地 curs0rgo 服务已启动并监听在 http://localhost:14285")
        print(f"{Fore.BLUE}  调用方式参考: https://linux.do/t/topic/533625{Style.RESET_ALL}")
        print(f"{Fore.GREEN}  curs0rgo 下载: https://github.com/J3n5en/curs0r-release{Style.RESET_ALL}")
        return False

if __name__ == "__main__":
    # 示例用法：请替换为实际的 email 和 token
    test_email = "<EMAIL>"
    test_token = "eyJxxxxxxx"
    
    print(f"正在尝试切换账号，邮箱: {test_email}")
    if switch_account(test_email, test_token):
        print("操作完成。")
    else:
        print("操作失败。") 